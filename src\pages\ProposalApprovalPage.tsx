import React, { useState } from 'react'
import { Card, Row, Col, Typo<PERSON>, Button, Table, Tag, Space, Modal, Form, Input, Rate, Divider, Avatar, Image, Steps, Timeline, message } from 'antd'
import { useNavigate, useParams } from 'react-router-dom'
import {
  CheckCircleOutlined,
  CloseCircleOutlined,
  EyeOutlined,
  DownloadOutlined,
  UserOutlined,
  CalendarOutlined,
  FileTextOutlined,
  AuditOutlined,
  CommentOutlined,
  StarOutlined,
  ArrowLeftOutlined,
} from '@ant-design/icons'
import { useAuthStore } from '@/store/authStore'
import type { ColumnsType } from 'antd/es/table'

const { Title, Paragraph, Text } = Typography
const { TextArea } = Input
const { Step } = Steps

interface ProposalDetail {
  id: string
  title: string
  projectName: string
  submittedBy: string
  submittedAt: string
  status: 'pending' | 'approved' | 'rejected'
  description: string
  estimatedBudget: number
  estimatedDuration: number
  attachments: Array<{
    id: string
    name: string
    type: string
    url: string
    thumbnail?: string
  }>
  reviewHistory: Array<{
    id: string
    reviewer: string
    action: 'submitted' | 'approved' | 'rejected' | 'commented'
    comment?: string
    timestamp: string
    rating?: number
  }>
}

interface PendingProposal {
  id: string
  title: string
  projectName: string
  submittedBy: string
  submittedAt: string
  priority: 'high' | 'medium' | 'low'
  estimatedBudget: number
}

const ProposalApprovalPage: React.FC = () => {
  const navigate = useNavigate()
  const { proposalId } = useParams()
  const { user } = useAuthStore()
  const [currentView, setCurrentView] = useState<'list' | 'detail'>(proposalId ? 'detail' : 'list')
  const [selectedProposal, setSelectedProposal] = useState<ProposalDetail | null>(null)
  const [isApprovalModalVisible, setIsApprovalModalVisible] = useState(false)
  const [approvalAction, setApprovalAction] = useState<'approve' | 'reject'>('approve')
  const [approvalForm] = Form.useForm()

  // 模拟待审批方案列表
  const [pendingProposals] = useState<PendingProposal[]>([
    {
      id: '1',
      title: '汽车展主展区设计方案',
      projectName: '2024春季汽车展',
      submittedBy: '学生A',
      submittedAt: '2024-01-20 14:30',
      priority: 'high',
      estimatedBudget: 200000
    },
    {
      id: '2',
      title: '科技会议室布局方案',
      projectName: '科技创新大会',
      submittedBy: '学生B',
      submittedAt: '2024-01-19 16:45',
      priority: 'medium',
      estimatedBudget: 150000
    }
  ])

  // 模拟方案详情
  const [proposalDetail] = useState<ProposalDetail>({
    id: '1',
    title: '汽车展主展区设计方案',
    projectName: '2024春季汽车展',
    submittedBy: '学生A',
    submittedAt: '2024-01-20 14:30',
    status: 'pending',
    description: '本方案采用现代化设计理念，以"未来出行"为主题，通过科技感的展示手段，突出汽车产品的创新性和前瞻性。主展区分为三个功能区域：产品展示区、互动体验区和洽谈区。整体设计注重空间的流动性和视觉的冲击力。',
    estimatedBudget: 200000,
    estimatedDuration: 15,
    attachments: [
      {
        id: '1',
        name: '设计方案说明书.pdf',
        type: 'pdf',
        url: '/files/proposal.pdf'
      },
      {
        id: '2',
        name: '平面布局图.jpg',
        type: 'image',
        url: '/api/placeholder/800/600',
        thumbnail: '/api/placeholder/200/150'
      },
      {
        id: '3',
        name: '3D效果图.jpg',
        type: 'image',
        url: '/api/placeholder/800/600',
        thumbnail: '/api/placeholder/200/150'
      },
      {
        id: '4',
        name: '材料清单.xlsx',
        type: 'excel',
        url: '/files/materials.xlsx'
      }
    ],
    reviewHistory: [
      {
        id: '1',
        reviewer: '学生A',
        action: 'submitted',
        comment: '提交设计方案，请老师审阅',
        timestamp: '2024-01-20 14:30'
      }
    ]
  })

  const pendingColumns: ColumnsType<PendingProposal> = [
    {
      title: '方案信息',
      key: 'proposalInfo',
      render: (_, record) => (
        <Space direction="vertical" size={0}>
          <Text strong style={{ cursor: 'pointer' }} onClick={() => handleViewProposal(record.id)}>
            {record.title}
          </Text>
          <Text type="secondary" style={{ fontSize: 12 }}>
            项目：{record.projectName}
          </Text>
        </Space>
      )
    },
    {
      title: '申请人',
      dataIndex: 'submittedBy',
      key: 'submittedBy',
      render: (name) => (
        <Space>
          <Avatar size="small" icon={<UserOutlined />} />
          <Text>{name}</Text>
        </Space>
      )
    },
    {
      title: '提交时间',
      dataIndex: 'submittedAt',
      key: 'submittedAt',
      render: (time) => (
        <Space>
          <CalendarOutlined />
          <Text>{time}</Text>
        </Space>
      )
    },
    {
      title: '优先级',
      dataIndex: 'priority',
      key: 'priority',
      render: (priority) => (
        <Tag color={priority === 'high' ? 'red' : priority === 'medium' ? 'orange' : 'blue'}>
          {priority === 'high' ? '高' : priority === 'medium' ? '中' : '低'}
        </Tag>
      )
    },
    {
      title: '预算',
      dataIndex: 'estimatedBudget',
      key: 'estimatedBudget',
      render: (budget) => `¥${budget.toLocaleString()}`
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space>
          <Button 
            type="primary" 
            icon={<EyeOutlined />}
            onClick={() => handleViewProposal(record.id)}
          >
            审阅
          </Button>
        </Space>
      )
    }
  ]

  const handleViewProposal = (id: string) => {
    setCurrentView('detail')
    // 这里应该根据ID获取详细信息
    setSelectedProposal(proposalDetail)
  }

  const handleBackToList = () => {
    setCurrentView('list')
    setSelectedProposal(null)
  }

  const handleApproval = (action: 'approve' | 'reject') => {
    setApprovalAction(action)
    setIsApprovalModalVisible(true)
  }

  const handleSubmitApproval = async (values: any) => {
    console.log('审批操作:', approvalAction, values)
    setIsApprovalModalVisible(false)
    approvalForm.resetFields()
    message.success(approvalAction === 'approve' ? '方案已批准' : '方案已驳回')
    handleBackToList()
  }

  const renderProposalList = () => (
    <>
      <div style={{ marginBottom: 24 }}>
        <Title level={2}>方案审批</Title>
        <Paragraph>
          审阅和评估学生提交的设计方案
        </Paragraph>
      </div>

      <Card title="待审批方案">
        <Table
          columns={pendingColumns}
          dataSource={pendingProposals}
          rowKey="id"
          pagination={{ pageSize: 10 }}
        />
      </Card>
    </>
  )

  const renderProposalDetail = () => {
    if (!selectedProposal) return null

    return (
      <>
        <div style={{ marginBottom: 24 }}>
          <Button 
            icon={<ArrowLeftOutlined />} 
            onClick={handleBackToList}
            style={{ marginBottom: 16 }}
          >
            返回列表
          </Button>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <div>
              <Title level={2}>{selectedProposal.title}</Title>
              <Space>
                <Text type="secondary">项目：{selectedProposal.projectName}</Text>
                <Tag color={selectedProposal.status === 'pending' ? 'orange' : 
                           selectedProposal.status === 'approved' ? 'green' : 'red'}>
                  {selectedProposal.status === 'pending' ? '待审批' : 
                   selectedProposal.status === 'approved' ? '已批准' : '已驳回'}
                </Tag>
              </Space>
            </div>
            {selectedProposal.status === 'pending' && (
              <Space>
                <Button 
                  type="primary" 
                  icon={<CheckCircleOutlined />}
                  onClick={() => handleApproval('approve')}
                >
                  批准
                </Button>
                <Button 
                  danger 
                  icon={<CloseCircleOutlined />}
                  onClick={() => handleApproval('reject')}
                >
                  驳回
                </Button>
              </Space>
            )}
          </div>
        </div>

        <Row gutter={24}>
          {/* 左侧：方案内容 */}
          <Col span={16}>
            <Card title="方案详情" style={{ marginBottom: 24 }}>
              <Space direction="vertical" style={{ width: '100%' }}>
                <div>
                  <Text strong>申请人：</Text>
                  <Space>
                    <Avatar size="small" icon={<UserOutlined />} />
                    <Text>{selectedProposal.submittedBy}</Text>
                  </Space>
                </div>
                <div>
                  <Text strong>提交时间：</Text>
                  <Text>{selectedProposal.submittedAt}</Text>
                </div>
                <div>
                  <Text strong>预估预算：</Text>
                  <Text>¥{selectedProposal.estimatedBudget.toLocaleString()}</Text>
                </div>
                <div>
                  <Text strong>预估工期：</Text>
                  <Text>{selectedProposal.estimatedDuration}天</Text>
                </div>
                <Divider />
                <div>
                  <Text strong>方案描述：</Text>
                  <Paragraph style={{ marginTop: 8 }}>
                    {selectedProposal.description}
                  </Paragraph>
                </div>
              </Space>
            </Card>

            <Card title="方案附件">
              <Row gutter={[16, 16]}>
                {selectedProposal.attachments.map(attachment => (
                  <Col span={6} key={attachment.id}>
                    <Card
                      size="small"
                      hoverable
                      cover={
                        attachment.type === 'image' ? (
                          <Image
                            src={attachment.thumbnail || attachment.url}
                            alt={attachment.name}
                            style={{ height: 120, objectFit: 'cover' }}
                          />
                        ) : (
                          <div style={{ 
                            height: 120, 
                            display: 'flex', 
                            alignItems: 'center', 
                            justifyContent: 'center',
                            background: '#f5f5f5'
                          }}>
                            <FileTextOutlined style={{ fontSize: 32, color: '#999' }} />
                          </div>
                        )
                      }
                      actions={[
                        <Button 
                          type="link" 
                          icon={<DownloadOutlined />}
                          href={attachment.url}
                          target="_blank"
                        >
                          下载
                        </Button>
                      ]}
                    >
                      <Card.Meta
                        title={<Text ellipsis style={{ fontSize: 12 }}>{attachment.name}</Text>}
                      />
                    </Card>
                  </Col>
                ))}
              </Row>
            </Card>
          </Col>

          {/* 右侧：审批流程 */}
          <Col span={8}>
            <Card title="审批流程">
              <Timeline>
                {selectedProposal.reviewHistory.map(history => (
                  <Timeline.Item
                    key={history.id}
                    dot={
                      history.action === 'approved' ? <CheckCircleOutlined style={{ color: '#52c41a' }} /> :
                      history.action === 'rejected' ? <CloseCircleOutlined style={{ color: '#ff4d4f' }} /> :
                      history.action === 'commented' ? <CommentOutlined style={{ color: '#1890ff' }} /> :
                      <AuditOutlined style={{ color: '#faad14' }} />
                    }
                  >
                    <div>
                      <Text strong>{history.reviewer}</Text>
                      <Text type="secondary" style={{ marginLeft: 8, fontSize: 12 }}>
                        {history.timestamp}
                      </Text>
                    </div>
                    <div style={{ marginTop: 4 }}>
                      <Tag color={
                        history.action === 'approved' ? 'green' :
                        history.action === 'rejected' ? 'red' :
                        history.action === 'commented' ? 'blue' : 'orange'
                      }>
                        {history.action === 'submitted' ? '提交方案' :
                         history.action === 'approved' ? '批准' :
                         history.action === 'rejected' ? '驳回' : '评论'}
                      </Tag>
                    </div>
                    {history.comment && (
                      <div style={{ marginTop: 8, padding: 8, background: '#f5f5f5', borderRadius: 4 }}>
                        <Text style={{ fontSize: 12 }}>{history.comment}</Text>
                      </div>
                    )}
                    {history.rating && (
                      <div style={{ marginTop: 8 }}>
                        <Rate disabled defaultValue={history.rating} size="small" />
                      </div>
                    )}
                  </Timeline.Item>
                ))}
              </Timeline>
            </Card>
          </Col>
        </Row>
      </>
    )
  }

  return (
    <div style={{ padding: '24px' }}>
      {currentView === 'list' && renderProposalList()}
      {currentView === 'detail' && renderProposalDetail()}

      {/* 审批模态框 */}
      <Modal
        title={approvalAction === 'approve' ? '批准方案' : '驳回方案'}
        open={isApprovalModalVisible}
        onOk={() => approvalForm.submit()}
        onCancel={() => setIsApprovalModalVisible(false)}
        width={500}
      >
        <Form
          form={approvalForm}
          layout="vertical"
          onFinish={handleSubmitApproval}
        >
          {approvalAction === 'approve' && (
            <Form.Item name="rating" label="方案评分">
              <Rate />
            </Form.Item>
          )}
          
          <Form.Item 
            name="comment" 
            label={approvalAction === 'approve' ? '审批意见' : '驳回原因'}
            rules={approvalAction === 'reject' ? [{ required: true, message: '请填写驳回原因' }] : []}
          >
            <TextArea 
              rows={4} 
              placeholder={approvalAction === 'approve' ? '请输入审批意见...' : '请详细说明驳回原因...'}
            />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}

export default ProposalApprovalPage
