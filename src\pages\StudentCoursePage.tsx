import React, { useState, useRef } from 'react'
import { Layout, Card, Tabs, Button, Space, Typography, Progress, List, Avatar, Tag, Input, Rate, Divider, Row, Col } from 'antd'
import {
  BookOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  FileTextOutlined,
  EditOutlined,
  Clock<PERSON>ircleOutlined,
  CheckCircleOutlined,
  StarOutlined,
  DownloadOutlined,
  FullscreenOutlined,
  SoundOutlined,
  SettingOutlined,
  NotificationOutlined,
  QuestionCircleOutlined,
} from '@ant-design/icons'
import { useAuthStore } from '@/store/authStore'

const { Sider, Content } = Layout
const { TabPane } = Tabs
const { Title, Text, Paragraph } = Typography
const { TextArea } = Input

interface Assignment {
  id: string
  title: string
  type: 'theory' | 'practice'
  dueDate: string
  status: 'pending' | 'submitted' | 'graded'
  score?: number
  maxScore: number
  description: string
}

interface Note {
  id: string
  content: string
  timestamp: string
  videoTime?: number
}

interface LearningProgress {
  videoProgress: number
  assignmentProgress: number
  overallProgress: number
}

const StudentCoursePage: React.FC = () => {
  const { user } = useAuthStore()
  const videoRef = useRef<HTMLVideoElement>(null)
  const [activeTab, setActiveTab] = useState('preview')
  const [isPlaying, setIsPlaying] = useState(false)
  const [currentTime, setCurrentTime] = useState(0)
  const [duration, setDuration] = useState(0)
  const [volume, setVolume] = useState(1)
  const [playbackRate, setPlaybackRate] = useState(1)
  const [notes, setNotes] = useState<Note[]>([])
  const [currentNote, setCurrentNote] = useState('')

  // 模拟课程数据
  const courseData = {
    id: '1',
    title: '会展策划基础',
    instructor: '张教授',
    description: '本课程将系统介绍会展策划的基本理论、方法和实践技能',
    totalLessons: 12,
    completedLessons: 8,
    currentLesson: {
      id: '1-9',
      title: '第九课：展位设计原理',
      videoUrl: '/api/placeholder/video.mp4',
      duration: 1800, // 30分钟
      materials: [
        { id: '1', name: '课件-展位设计原理.pptx', size: '2.5MB', url: '/files/lesson9.pptx' },
        { id: '2', name: '参考资料-设计案例.pdf', size: '5.2MB', url: '/files/cases.pdf' }
      ]
    }
  }

  // 模拟作业数据
  const [assignments] = useState<Assignment[]>([
    {
      id: '1',
      title: '展位设计方案',
      type: 'practice',
      dueDate: '2024-01-25',
      status: 'pending',
      maxScore: 100,
      description: '根据课程内容，设计一个汽车展的展位方案，要求包含平面布局和3D效果图'
    },
    {
      id: '2',
      title: '理论知识测试',
      type: 'theory',
      dueDate: '2024-01-22',
      status: 'graded',
      score: 85,
      maxScore: 100,
      description: '测试对展位设计基本原理的理解'
    }
  ])

  const [progress] = useState<LearningProgress>({
    videoProgress: 75,
    assignmentProgress: 60,
    overallProgress: 68
  })

  const handlePlayPause = () => {
    if (videoRef.current) {
      if (isPlaying) {
        videoRef.current.pause()
      } else {
        videoRef.current.play()
      }
      setIsPlaying(!isPlaying)
    }
  }

  const handleAddNote = () => {
    if (currentNote.trim()) {
      const newNote: Note = {
        id: Date.now().toString(),
        content: currentNote,
        timestamp: new Date().toLocaleTimeString(),
        videoTime: currentTime
      }
      setNotes([...notes, newNote])
      setCurrentNote('')
    }
  }

  const handleStartAssignment = (assignment: Assignment) => {
    if (assignment.type === 'practice') {
      // 启动3D编辑器
      console.log('启动3D编辑器进行实操作业')
      window.open('/design-3d', '_blank')
    } else {
      // 启动理论测试
      console.log('开始理论测试')
    }
  }

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = Math.floor(seconds % 60)
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  }

  return (
    <Layout style={{ height: '100vh' }}>
      {/* 左侧导航 */}
      <Sider width={280} style={{ background: '#fff', borderRight: '1px solid #f0f0f0' }}>
        <div style={{ padding: '16px' }}>
          <Title level={4}>{courseData.title}</Title>
          <Text type="secondary">讲师：{courseData.instructor}</Text>
          
          <Divider />
          
          {/* 学习进度 */}
          <Card size="small" title="学习进度" style={{ marginBottom: 16 }}>
            <Space direction="vertical" style={{ width: '100%' }}>
              <div>
                <Text>总体进度</Text>
                <Progress percent={progress.overallProgress} size="small" />
              </div>
              <div>
                <Text>视频学习</Text>
                <Progress percent={progress.videoProgress} size="small" strokeColor="#52c41a" />
              </div>
              <div>
                <Text>作业完成</Text>
                <Progress percent={progress.assignmentProgress} size="small" strokeColor="#1890ff" />
              </div>
            </Space>
          </Card>

          {/* 导航菜单 */}
          <div>
            <Button 
              type={activeTab === 'preview' ? 'primary' : 'text'}
              icon={<BookOutlined />}
              block
              style={{ textAlign: 'left', marginBottom: 8 }}
              onClick={() => setActiveTab('preview')}
            >
              预习
            </Button>
            <Button 
              type={activeTab === 'learning' ? 'primary' : 'text'}
              icon={<PlayCircleOutlined />}
              block
              style={{ textAlign: 'left', marginBottom: 8 }}
              onClick={() => setActiveTab('learning')}
            >
              学习
            </Button>
            <Button 
              type={activeTab === 'assignments' ? 'primary' : 'text'}
              icon={<EditOutlined />}
              block
              style={{ textAlign: 'left', marginBottom: 8 }}
              onClick={() => setActiveTab('assignments')}
            >
              作业
            </Button>
            <Button 
              type={activeTab === 'practice' ? 'primary' : 'text'}
              icon={<QuestionCircleOutlined />}
              block
              style={{ textAlign: 'left', marginBottom: 8 }}
              onClick={() => setActiveTab('practice')}
            >
              练习
            </Button>
            <Button 
              type={activeTab === 'notes' ? 'primary' : 'text'}
              icon={<FileTextOutlined />}
              block
              style={{ textAlign: 'left' }}
              onClick={() => setActiveTab('notes')}
            >
              我的笔记
            </Button>
          </div>
        </div>
      </Sider>

      {/* 主内容区 */}
      <Content style={{ background: '#fff' }}>
        {activeTab === 'learning' && (
          <div style={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
            {/* 视频播放器 */}
            <div style={{ 
              height: '60%', 
              background: '#000', 
              position: 'relative',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}>
              <div style={{ 
                width: '80%', 
                height: '80%', 
                background: '#333',
                borderRadius: 8,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                color: '#fff'
              }}>
                <div style={{ textAlign: 'center' }}>
                  <PlayCircleOutlined style={{ fontSize: 64, marginBottom: 16 }} />
                  <div>视频播放区域</div>
                  <div style={{ fontSize: 14, marginTop: 8 }}>
                    {courseData.currentLesson.title}
                  </div>
                </div>
              </div>

              {/* 视频控制栏 */}
              <div style={{
                position: 'absolute',
                bottom: 0,
                left: 0,
                right: 0,
                background: 'rgba(0,0,0,0.8)',
                padding: '12px 16px',
                color: '#fff'
              }}>
                <Row align="middle" gutter={16}>
                  <Col>
                    <Button 
                      type="text" 
                      icon={isPlaying ? <PauseCircleOutlined /> : <PlayCircleOutlined />}
                      onClick={handlePlayPause}
                      style={{ color: '#fff' }}
                    />
                  </Col>
                  <Col flex={1}>
                    <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                      <Text style={{ color: '#fff', fontSize: 12 }}>
                        {formatTime(currentTime)}
                      </Text>
                      <div style={{ 
                        flex: 1, 
                        height: 4, 
                        background: 'rgba(255,255,255,0.3)', 
                        borderRadius: 2,
                        position: 'relative'
                      }}>
                        <div style={{
                          width: `${(currentTime / duration) * 100}%`,
                          height: '100%',
                          background: '#1890ff',
                          borderRadius: 2
                        }} />
                      </div>
                      <Text style={{ color: '#fff', fontSize: 12 }}>
                        {formatTime(duration)}
                      </Text>
                    </div>
                  </Col>
                  <Col>
                    <Space>
                      <Button type="text" icon={<SoundOutlined />} style={{ color: '#fff' }} />
                      <Button type="text" icon={<SettingOutlined />} style={{ color: '#fff' }} />
                      <Button type="text" icon={<FullscreenOutlined />} style={{ color: '#fff' }} />
                    </Space>
                  </Col>
                </Row>
              </div>
            </div>

            {/* 课件和笔记区域 */}
            <div style={{ height: '40%', borderTop: '1px solid #f0f0f0' }}>
              <Tabs defaultActiveKey="materials" style={{ height: '100%' }}>
                <TabPane tab="课件资料" key="materials">
                  <div style={{ padding: '16px' }}>
                    <List
                      dataSource={courseData.currentLesson.materials}
                      renderItem={material => (
                        <List.Item
                          actions={[
                            <Button type="link" icon={<DownloadOutlined />}>
                              下载
                            </Button>
                          ]}
                        >
                          <List.Item.Meta
                            avatar={<Avatar icon={<FileTextOutlined />} />}
                            title={material.name}
                            description={`文件大小: ${material.size}`}
                          />
                        </List.Item>
                      )}
                    />
                  </div>
                </TabPane>
                <TabPane tab="实时笔记" key="notes">
                  <div style={{ padding: '16px', height: '100%', display: 'flex', flexDirection: 'column' }}>
                    <div style={{ marginBottom: 16 }}>
                      <TextArea
                        value={currentNote}
                        onChange={(e) => setCurrentNote(e.target.value)}
                        placeholder="在这里记录学习笔记..."
                        rows={3}
                        style={{ marginBottom: 8 }}
                      />
                      <Button type="primary" onClick={handleAddNote}>
                        添加笔记
                      </Button>
                    </div>
                    <div style={{ flex: 1, overflow: 'auto' }}>
                      <List
                        dataSource={notes}
                        renderItem={note => (
                          <List.Item>
                            <List.Item.Meta
                              title={
                                <Space>
                                  <Text>{note.timestamp}</Text>
                                  {note.videoTime && (
                                    <Tag color="blue">{formatTime(note.videoTime)}</Tag>
                                  )}
                                </Space>
                              }
                              description={note.content}
                            />
                          </List.Item>
                        )}
                      />
                    </div>
                  </div>
                </TabPane>
              </Tabs>
            </div>
          </div>
        )}

        {activeTab === 'assignments' && (
          <div style={{ padding: '24px' }}>
            <Title level={3}>课程作业</Title>
            <List
              dataSource={assignments}
              renderItem={assignment => (
                <List.Item
                  actions={[
                    assignment.status === 'pending' ? (
                      <Button 
                        type="primary" 
                        onClick={() => handleStartAssignment(assignment)}
                      >
                        {assignment.type === 'practice' ? '开始作业' : '开始测试'}
                      </Button>
                    ) : assignment.status === 'submitted' ? (
                      <Tag color="blue">已提交</Tag>
                    ) : (
                      <Space>
                        <Tag color="green">已评分</Tag>
                        <Text strong>{assignment.score}/{assignment.maxScore}</Text>
                      </Space>
                    )
                  ]}
                >
                  <List.Item.Meta
                    avatar={
                      <Avatar 
                        icon={assignment.type === 'practice' ? <EditOutlined /> : <QuestionCircleOutlined />}
                        style={{ 
                          backgroundColor: assignment.type === 'practice' ? '#52c41a' : '#1890ff' 
                        }}
                      />
                    }
                    title={
                      <Space>
                        {assignment.title}
                        <Tag color={assignment.type === 'practice' ? 'green' : 'blue'}>
                          {assignment.type === 'practice' ? '实操作业' : '理论测试'}
                        </Tag>
                      </Space>
                    }
                    description={
                      <Space direction="vertical">
                        <Text>{assignment.description}</Text>
                        <Text type="secondary">
                          <ClockCircleOutlined /> 截止时间：{assignment.dueDate}
                        </Text>
                      </Space>
                    }
                  />
                </List.Item>
              )}
            />
          </div>
        )}

        {(activeTab === 'preview' || activeTab === 'practice' || activeTab === 'notes') && (
          <div style={{ 
            padding: '24px', 
            textAlign: 'center', 
            color: '#999',
            height: '100%',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}>
            <div>
              <BookOutlined style={{ fontSize: 64, marginBottom: 16 }} />
              <div>该功能正在开发中...</div>
            </div>
          </div>
        )}
      </Content>
    </Layout>
  )
}

export default StudentCoursePage
