"""
智能教学代理 - 主应用入口
基于 Streamlit 的多页面桌面应用程序
包含客户需求预测和供应商选择两个核心功能模块
"""

import streamlit as st
import pandas as pd
from datetime import datetime

# 页面配置
st.set_page_config(
    page_title="智能教学代理",
    page_icon="🎓",
    layout="wide",
    initial_sidebar_state="expanded"
)

# 自定义CSS样式
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
        font-weight: bold;
    }
    .sub-header {
        font-size: 1.5rem;
        color: #2c3e50;
        margin-top: 2rem;
        margin-bottom: 1rem;
        border-left: 4px solid #1f77b4;
        padding-left: 1rem;
    }
    .feature-card {
        background-color: #f8f9fa;
        padding: 1.5rem;
        border-radius: 10px;
        border-left: 4px solid #1f77b4;
        margin: 1rem 0;
    }
    .highlight-text {
        color: #e74c3c;
        font-weight: bold;
    }
    .info-box {
        background-color: #e8f4fd;
        padding: 1rem;
        border-radius: 5px;
        border: 1px solid #bee5eb;
        margin: 1rem 0;
    }
</style>
""", unsafe_allow_html=True)

def main():
    """主页面内容"""
    
    # 主标题
    st.markdown('<h1 class="main-header">🎓 智能教学代理系统</h1>', unsafe_allow_html=True)
    
    # 欢迎信息
    st.markdown("""
    <div class="info-box">
        <h3>🌟 欢迎使用智能教学代理系统！</h3>
        <p>本系统是专为商业数据分析实训教学设计的智能化教学工具，通过交互式操作帮助师生掌握数据分析和决策支持的核心概念。</p>
    </div>
    """, unsafe_allow_html=True)
    
    # 系统概述
    st.markdown('<h2 class="sub-header">📋 系统概述</h2>', unsafe_allow_html=True)
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("""
        <div class="feature-card">
            <h4>🎯 教学目标</h4>
            <ul>
                <li>掌握时间序列预测的基本流程</li>
                <li>理解多标准决策分析方法</li>
                <li>培养数据驱动的决策思维</li>
                <li>提升商业数据分析能力</li>
            </ul>
        </div>
        """, unsafe_allow_html=True)
    
    with col2:
        st.markdown("""
        <div class="feature-card">
            <h4>💡 系统特色</h4>
            <ul>
                <li>交互式可视化界面</li>
                <li>实时计算和分析</li>
                <li>支持离线使用</li>
                <li>适合教学演示</li>
            </ul>
        </div>
        """, unsafe_allow_html=True)
    
    # 核心功能模块
    st.markdown('<h2 class="sub-header">🔧 核心功能模块</h2>', unsafe_allow_html=True)
    
    # 模块1：客户需求预测
    st.markdown("""
    <div class="feature-card">
        <h3>📈 模块一：客户需求预测</h3>
        <p><strong>功能描述：</strong>通过时间序列分析技术，帮助学生掌握需求预测的基本流程和核心概念。</p>
        
        <h4>主要功能：</h4>
        <ul>
            <li><span class="highlight-text">数据选择：</span>提供多个预置数据集，模拟不同业务场景</li>
            <li><span class="highlight-text">预测设置：</span>自定义预测周期长度</li>
            <li><span class="highlight-text">模型分析：</span>使用Prophet模型进行时间序列预测</li>
            <li><span class="highlight-text">结果可视化：</span>交互式图表展示预测结果和置信区间</li>
            <li><span class="highlight-text">成分分解：</span>分析趋势、季节性等影响因素</li>
            <li><span class="highlight-text">性能评估：</span>计算MAE和RMSE等评估指标</li>
        </ul>
        
        <h4>学习收获：</h4>
        <p>学生将学会如何进行时间序列预测，理解影响销售的关键因素，掌握模型性能评估方法。</p>
    </div>
    """, unsafe_allow_html=True)
    
    # 模块2：供应商选择
    st.markdown("""
    <div class="feature-card">
        <h3>🏭 模块二：供应商选择</h3>
        <p><strong>功能描述：</strong>结合AHP层次分析法和TOPSIS决策方法，教授多标准供应商评估技术。</p>
        
        <h4>主要功能：</h4>
        <ul>
            <li><span class="highlight-text">权重设置：</span>通过AHP成对比较确定评估标准权重</li>
            <li><span class="highlight-text">语义化界面：</span>直观的滑块操作，无需复杂数学计算</li>
            <li><span class="highlight-text">综合评估：</span>使用TOPSIS方法进行供应商排序</li>
            <li><span class="highlight-text">结果展示：</span>详细的排名报告和得分分析</li>
            <li><span class="highlight-text">雷达图对比：</span>可视化供应商能力对比</li>
            <li><span class="highlight-text">透明决策：</span>完整展示决策过程和依据</li>
        </ul>
        
        <h4>学习收获：</h4>
        <p>学生将掌握多标准决策分析方法，理解如何平衡不同评估标准，培养科学的决策思维。</p>
    </div>
    """, unsafe_allow_html=True)
    
    # 使用指南
    st.markdown('<h2 class="sub-header">📖 使用指南</h2>', unsafe_allow_html=True)
    
    st.markdown("""
    <div class="info-box">
        <h4>🚀 快速开始</h4>
        <ol>
            <li><strong>选择功能模块：</strong>使用左侧导航栏选择要使用的功能模块</li>
            <li><strong>设置参数：</strong>根据界面提示设置相关参数</li>
            <li><strong>运行分析：</strong>点击相应按钮开始分析</li>
            <li><strong>查看结果：</strong>通过选项卡查看不同类型的分析结果</li>
            <li><strong>交互探索：</strong>使用鼠标悬停、缩放等操作深入探索</li>
        </ol>
    </div>
    """, unsafe_allow_html=True)
    
    # 技术说明
    st.markdown('<h2 class="sub-header">⚙️ 技术说明</h2>', unsafe_allow_html=True)
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.markdown("""
        **核心技术**
        - Streamlit Web框架
        - Prophet时间序列预测
        - AHP层次分析法
        - TOPSIS决策方法
        """)
    
    with col2:
        st.markdown("""
        **可视化工具**
        - Plotly交互式图表
        - 雷达图对比分析
        - 动态数据表格
        - 自定义CSS样式
        """)
    
    with col3:
        st.markdown("""
        **系统特性**
        - 离线运行支持
        - 响应式界面设计
        - 实时计算分析
        - 教学友好界面
        """)
    
    # 页脚信息
    st.markdown("---")
    st.markdown("""
    <div style="text-align: center; color: #7f8c8d; margin-top: 2rem;">
        <p>💡 <strong>提示：</strong>请使用左侧导航栏选择具体的功能模块开始学习和实践！</p>
        <p>📧 如有问题，请联系教学团队获取支持。</p>
    </div>
    """, unsafe_allow_html=True)

if __name__ == "__main__":
    main()
