import React, { useState, useRef, useEffect } from 'react'
import { Layout, Button, Card, Space, Tabs, Tree, Slider, Select, Input, Tooltip, Divider, Row, Col, Typography, Tag, Table, Drawer, Statistic } from 'antd'
import {
  SaveOutlined,
  UndoOutlined,
  RedoOutlined,
  DownloadOutlined,
  UploadOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  EyeOutlined,
  SettingOutlined,
  BgColorsOutlined,
  BulbOutlined,
  BoxPlotOutlined,
  DragOutlined,
  RotateRightOutlined,
  ExpandOutlined,
  CameraOutlined,
  SunOutlined,
  MoonOutlined,
  CloudOutlined,
  ThunderboltOutlined,
  DollarOutlined,
  CalculatorOutlined,
  FileExcelOutlined,
  DeleteOutlined,
} from '@ant-design/icons'
import { useAuthStore } from '@/store/authStore'

const { Header, Sider, Content } = Layout
const { TabPane } = Tabs
const { TreeNode } = Tree
const { Text, Title } = Typography

interface SceneObject {
  id: string
  name: string
  type: 'model' | 'light' | 'camera' | 'group'
  visible: boolean
  locked: boolean
  children?: SceneObject[]
}

interface Material {
  id: string
  name: string
  type: 'basic' | 'pbr' | 'glass' | 'metal'
  thumbnail: string
  properties: Record<string, any>
}

interface Asset {
  id: string
  name: string
  category: 'structure' | 'furniture' | 'decoration' | 'lighting'
  thumbnail: string
  fileSize: string
  tags: string[]
  materialId?: string // 关联的材料ID
  unitPrice?: number // 材料单价
  unit?: string // 材料单位
}

interface MaterialUsage {
  materialId: string
  materialName: string
  quantity: number
  unitPrice: number
  totalPrice: number
}

interface CostCalculation {
  totalMaterials: number
  totalCost: number
  materialBreakdown: MaterialUsage[]
}

const Editor3DPage: React.FC = () => {
  const { user } = useAuthStore()
  const viewportRef = useRef<HTMLDivElement>(null)
  const [selectedTool, setSelectedTool] = useState<'select' | 'move' | 'rotate' | 'scale'>('select')
  const [viewMode, setViewMode] = useState<'perspective' | 'orthographic'>('perspective')
  const [cameraMode, setCameraMode] = useState<'orbit' | 'fly' | 'first-person'>('orbit')
  const [selectedObject, setSelectedObject] = useState<SceneObject | null>(null)
  const [sceneState, setSceneState] = useState<'day' | 'night'>('day')
  const [isPlaying, setIsPlaying] = useState(false)
  const [costCalculation, setCostCalculation] = useState<CostCalculation>({
    totalMaterials: 0,
    totalCost: 0,
    materialBreakdown: []
  })
  const [isCostPanelVisible, setIsCostPanelVisible] = useState(false)

  // 模拟场景对象数据
  const [sceneObjects] = useState<SceneObject[]>([
    {
      id: '1',
      name: '主展台',
      type: 'group',
      visible: true,
      locked: false,
      children: [
        { id: '1-1', name: '展台结构', type: 'model', visible: true, locked: false },
        { id: '1-2', name: '展示柜', type: 'model', visible: true, locked: false }
      ]
    },
    {
      id: '2',
      name: '照明系统',
      type: 'group',
      visible: true,
      locked: false,
      children: [
        { id: '2-1', name: '主光源', type: 'light', visible: true, locked: false },
        { id: '2-2', name: '环境光', type: 'light', visible: true, locked: false }
      ]
    },
    {
      id: '3',
      name: '摄像机',
      type: 'camera',
      visible: true,
      locked: false
    }
  ])

  // 模拟材质库
  const [materials] = useState<Material[]>([
    { id: '1', name: '木纹', type: 'pbr', thumbnail: '/api/placeholder/80/80', properties: {} },
    { id: '2', name: '金属', type: 'metal', thumbnail: '/api/placeholder/80/80', properties: {} },
    { id: '3', name: '玻璃', type: 'glass', thumbnail: '/api/placeholder/80/80', properties: {} },
    { id: '4', name: '布料', type: 'basic', thumbnail: '/api/placeholder/80/80', properties: {} }
  ])

  // 模拟素材库（与材料库关联）
  const [assets] = useState<Asset[]>([
    {
      id: '1',
      name: '展台桁架',
      category: 'structure',
      thumbnail: '/api/placeholder/80/80',
      fileSize: '2.5MB',
      tags: ['桁架', '结构'],
      materialId: '1', // 关联材料库中的铝合金展台桁架
      unitPrice: 280,
      unit: '件'
    },
    {
      id: '2',
      name: '展示柜',
      category: 'furniture',
      thumbnail: '/api/placeholder/80/80',
      fileSize: '1.8MB',
      tags: ['展柜', '家具'],
      materialId: '3', // 关联材料库中的亚克力展示柜
      unitPrice: 450,
      unit: '件'
    },
    {
      id: '3',
      name: '装饰品',
      category: 'decoration',
      thumbnail: '/api/placeholder/80/80',
      fileSize: '0.5MB',
      tags: ['装饰']
      // 无关联材料，不计入成本
    },
    {
      id: '4',
      name: 'LED灯',
      category: 'lighting',
      thumbnail: '/api/placeholder/80/80',
      fileSize: '0.3MB',
      tags: ['照明'],
      materialId: '2', // 关联材料库中的LED射灯
      unitPrice: 120,
      unit: '件'
    }
  ])

  const handleToolSelect = (tool: 'select' | 'move' | 'rotate' | 'scale') => {
    setSelectedTool(tool)
  }

  const handleSave = () => {
    console.log('保存场景')
  }

  const handleExport = () => {
    console.log('导出场景')
  }

  const handleRender = () => {
    console.log('开始渲染')
  }

  const handleSceneStateChange = (state: 'day' | 'night') => {
    setSceneState(state)
    console.log('切换场景状态:', state)
  }

  // 处理素材拖拽到场景中
  const handleAssetDrop = (asset: Asset) => {
    console.log('添加素材到场景:', asset.name)

    // 如果素材关联了材料，自动计算成本
    if (asset.materialId && asset.unitPrice) {
      updateMaterialUsage(asset.materialId, asset.name, 1, asset.unitPrice)
    }
  }

  // 更新材料使用情况和成本计算
  const updateMaterialUsage = (materialId: string, materialName: string, quantity: number, unitPrice: number) => {
    setCostCalculation(prev => {
      const existingIndex = prev.materialBreakdown.findIndex(item => item.materialId === materialId)
      let newBreakdown = [...prev.materialBreakdown]

      if (existingIndex >= 0) {
        // 更新现有材料的数量
        newBreakdown[existingIndex] = {
          ...newBreakdown[existingIndex],
          quantity: newBreakdown[existingIndex].quantity + quantity,
          totalPrice: (newBreakdown[existingIndex].quantity + quantity) * unitPrice
        }
      } else {
        // 添加新材料
        newBreakdown.push({
          materialId,
          materialName,
          quantity,
          unitPrice,
          totalPrice: quantity * unitPrice
        })
      }

      const totalCost = newBreakdown.reduce((sum, item) => sum + item.totalPrice, 0)
      const totalMaterials = newBreakdown.reduce((sum, item) => sum + item.quantity, 0)

      return {
        totalMaterials,
        totalCost,
        materialBreakdown: newBreakdown
      }
    })

    // 显示成本面板
    setIsCostPanelVisible(true)

    // 发送使用记录到后端（模拟）
    console.log('材料使用记录:', {
      materialId,
      materialName,
      quantity,
      unitPrice,
      totalPrice: quantity * unitPrice,
      timestamp: new Date().toISOString()
    })
  }

  // 移除材料使用
  const removeMaterialUsage = (materialId: string) => {
    setCostCalculation(prev => {
      const newBreakdown = prev.materialBreakdown.filter(item => item.materialId !== materialId)
      const totalCost = newBreakdown.reduce((sum, item) => sum + item.totalPrice, 0)
      const totalMaterials = newBreakdown.reduce((sum, item) => sum + item.quantity, 0)

      return {
        totalMaterials,
        totalCost,
        materialBreakdown: newBreakdown
      }
    })
  }

  // 导出成本报告
  const exportCostReport = () => {
    const report = {
      projectName: '当前项目',
      timestamp: new Date().toISOString(),
      totalCost: costCalculation.totalCost,
      totalMaterials: costCalculation.totalMaterials,
      materials: costCalculation.materialBreakdown
    }

    console.log('导出成本报告:', report)
    // 这里可以实现实际的导出功能
  }

  const renderTreeNodes = (data: SceneObject[]): React.ReactNode => {
    return data.map(item => {
      if (item.children) {
        return (
          <TreeNode
            key={item.id}
            title={
              <Space>
                <span>{item.name}</span>
                {!item.visible && <EyeOutlined style={{ color: '#ccc' }} />}
                {item.locked && <SettingOutlined style={{ color: '#ccc' }} />}
              </Space>
            }
          >
            {renderTreeNodes(item.children)}
          </TreeNode>
        )
      }
      return (
        <TreeNode
          key={item.id}
          title={
            <Space>
              <span>{item.name}</span>
              {!item.visible && <EyeOutlined style={{ color: '#ccc' }} />}
              {item.locked && <SettingOutlined style={{ color: '#ccc' }} />}
            </Space>
          }
        />
      )
    })
  }

  return (
    <Layout style={{ height: '100vh' }}>
      {/* 顶部菜单栏 */}
      <Header style={{ 
        background: '#fff', 
        padding: '0 16px', 
        borderBottom: '1px solid #f0f0f0',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center'
      }}>
        <div>
          <Space size="middle">
            <Title level={4} style={{ margin: 0 }}>3D编辑器</Title>
            <Divider type="vertical" />
            <Space>
              <Button icon={<SaveOutlined />} onClick={handleSave}>保存</Button>
              <Button icon={<UndoOutlined />}>撤销</Button>
              <Button icon={<RedoOutlined />}>重做</Button>
            </Space>
            <Divider type="vertical" />
            <Space>
              <Button icon={<DownloadOutlined />} onClick={handleExport}>导出</Button>
              <Button
                icon={<CalculatorOutlined />}
                onClick={() => setIsCostPanelVisible(true)}
                style={{
                  backgroundColor: costCalculation.totalCost > 0 ? '#fff2e8' : undefined,
                  borderColor: costCalculation.totalCost > 0 ? '#ffbb96' : undefined,
                  color: costCalculation.totalCost > 0 ? '#d4380d' : undefined
                }}
              >
                成本计算 {costCalculation.totalCost > 0 && `(¥${costCalculation.totalCost.toFixed(2)})`}
              </Button>
              <Button type="primary" icon={<CameraOutlined />} onClick={handleRender}>
                渲染
              </Button>
            </Space>
          </Space>
        </div>
        <div>
          <Space>
            <Text>用户：{user?.username}</Text>
            <Button icon={<UploadOutlined />}>上传到项目</Button>
          </Space>
        </div>
      </Header>

      <Layout>
        {/* 左侧工具面板 */}
        <Sider width={280} style={{ background: '#fafafa', borderRight: '1px solid #f0f0f0' }}>
          <div style={{ padding: '16px' }}>
            {/* 工具栏 */}
            <Card size="small" title="工具" style={{ marginBottom: 16 }}>
              <Space wrap>
                <Tooltip title="选择">
                  <Button 
                    type={selectedTool === 'select' ? 'primary' : 'default'}
                    icon={<DragOutlined />}
                    onClick={() => handleToolSelect('select')}
                  />
                </Tooltip>
                <Tooltip title="移动">
                  <Button 
                    type={selectedTool === 'move' ? 'primary' : 'default'}
                    icon={<DragOutlined />}
                    onClick={() => handleToolSelect('move')}
                  />
                </Tooltip>
                <Tooltip title="旋转">
                  <Button 
                    type={selectedTool === 'rotate' ? 'primary' : 'default'}
                    icon={<RotateRightOutlined />}
                    onClick={() => handleToolSelect('rotate')}
                  />
                </Tooltip>
                <Tooltip title="缩放">
                  <Button 
                    type={selectedTool === 'scale' ? 'primary' : 'default'}
                    icon={<ExpandOutlined />}
                    onClick={() => handleToolSelect('scale')}
                  />
                </Tooltip>
              </Space>
            </Card>

            {/* 视角模式 */}
            <Card size="small" title="视角" style={{ marginBottom: 16 }}>
              <Space direction="vertical" style={{ width: '100%' }}>
                <Select
                  value={viewMode}
                  onChange={setViewMode}
                  style={{ width: '100%' }}
                >
                  <Select.Option value="perspective">透视</Select.Option>
                  <Select.Option value="orthographic">正交</Select.Option>
                </Select>
                <Select
                  value={cameraMode}
                  onChange={setCameraMode}
                  style={{ width: '100%' }}
                >
                  <Select.Option value="orbit">环绕</Select.Option>
                  <Select.Option value="fly">飞行</Select.Option>
                  <Select.Option value="first-person">第一人称</Select.Option>
                </Select>
              </Space>
            </Card>

            {/* 环境控制 */}
            <Card size="small" title="环境" style={{ marginBottom: 16 }}>
              <Space direction="vertical" style={{ width: '100%' }}>
                <div>
                  <Text>场景状态：</Text>
                  <Space>
                    <Button 
                      size="small"
                      type={sceneState === 'day' ? 'primary' : 'default'}
                      icon={<SunOutlined />}
                      onClick={() => handleSceneStateChange('day')}
                    >
                      日间
                    </Button>
                    <Button 
                      size="small"
                      type={sceneState === 'night' ? 'primary' : 'default'}
                      icon={<MoonOutlined />}
                      onClick={() => handleSceneStateChange('night')}
                    >
                      夜间
                    </Button>
                  </Space>
                </div>
                <div>
                  <Text>天气：</Text>
                  <Space>
                    <Button size="small" icon={<SunOutlined />}>晴天</Button>
                    <Button size="small" icon={<CloudOutlined />}>多云</Button>
                    <Button size="small" icon={<ThunderboltOutlined />}>雨天</Button>
                  </Space>
                </div>
              </Space>
            </Card>
          </div>
        </Sider>

        {/* 中央3D视口 */}
        <Content style={{ position: 'relative' }}>
          <div 
            ref={viewportRef}
            style={{ 
              width: '100%', 
              height: '100%', 
              background: 'linear-gradient(to bottom, #87CEEB, #98FB98)',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              position: 'relative'
            }}
          >
            <div style={{ 
              textAlign: 'center', 
              color: '#666',
              fontSize: 18
            }}>
              <BoxPlotOutlined style={{ fontSize: 64, marginBottom: 16, display: 'block' }} />
              <div>3D视口区域</div>
              <div style={{ fontSize: 14, marginTop: 8 }}>
                当前工具: {selectedTool} | 视角: {viewMode} | 相机: {cameraMode}
              </div>
            </div>

            {/* 视口控制按钮 */}
            <div style={{ 
              position: 'absolute', 
              top: 16, 
              right: 16,
              background: 'rgba(255,255,255,0.9)',
              borderRadius: 6,
              padding: 8
            }}>
              <Space>
                <Button 
                  size="small"
                  type={isPlaying ? 'primary' : 'default'}
                  icon={isPlaying ? <PauseCircleOutlined /> : <PlayCircleOutlined />}
                  onClick={() => setIsPlaying(!isPlaying)}
                >
                  {isPlaying ? '暂停' : '播放'}
                </Button>
                <Button size="small" icon={<EyeOutlined />}>预览</Button>
              </Space>
            </div>
          </div>
        </Content>

        {/* 右侧属性面板 */}
        <Sider width={320} style={{ background: '#fafafa', borderLeft: '1px solid #f0f0f0' }}>
          <Tabs defaultActiveKey="inspector" style={{ height: '100%' }}>
            <TabPane tab="属性" key="inspector">
              <div style={{ padding: '16px' }}>
                {selectedObject ? (
                  <Card size="small" title={`${selectedObject.name} 属性`}>
                    <Space direction="vertical" style={{ width: '100%' }}>
                      <div>
                        <Text>位置 X:</Text>
                        <Slider min={-100} max={100} defaultValue={0} />
                      </div>
                      <div>
                        <Text>位置 Y:</Text>
                        <Slider min={-100} max={100} defaultValue={0} />
                      </div>
                      <div>
                        <Text>位置 Z:</Text>
                        <Slider min={-100} max={100} defaultValue={0} />
                      </div>
                      <Divider />
                      <div>
                        <Text>旋转 X:</Text>
                        <Slider min={0} max={360} defaultValue={0} />
                      </div>
                      <div>
                        <Text>旋转 Y:</Text>
                        <Slider min={0} max={360} defaultValue={0} />
                      </div>
                      <div>
                        <Text>旋转 Z:</Text>
                        <Slider min={0} max={360} defaultValue={0} />
                      </div>
                    </Space>
                  </Card>
                ) : (
                  <div style={{ textAlign: 'center', color: '#999', marginTop: 50 }}>
                    选择一个对象查看属性
                  </div>
                )}
              </div>
            </TabPane>

            <TabPane tab="层级" key="hierarchy">
              <div style={{ padding: '16px' }}>
                <Card size="small" title="场景层级">
                  <Tree
                    showLine
                    defaultExpandAll
                    onSelect={(keys) => {
                      if (keys.length > 0) {
                        // 这里应该根据选中的key找到对应的对象
                        console.log('选中对象:', keys[0])
                      }
                    }}
                  >
                    {renderTreeNodes(sceneObjects)}
                  </Tree>
                </Card>
              </div>
            </TabPane>
          </Tabs>
        </Sider>
      </Layout>

      {/* 底部素材库 */}
      <div style={{ 
        height: 200, 
        background: '#fff', 
        borderTop: '1px solid #f0f0f0',
        overflow: 'hidden'
      }}>
        <Tabs defaultActiveKey="assets" style={{ height: '100%' }}>
          <TabPane tab="素材库" key="assets">
            <div style={{ padding: '8px 16px', height: '100%', overflow: 'auto' }}>
              <Row gutter={[8, 8]}>
                {assets.map(asset => (
                  <Col key={asset.id}>
                    <Card
                      size="small"
                      hoverable
                      style={{
                        width: 120,
                        cursor: 'grab',
                        border: asset.materialId ? '1px solid #52c41a' : undefined
                      }}
                      cover={
                        <div style={{ position: 'relative' }}>
                          <img
                            alt={asset.name}
                            src={asset.thumbnail}
                            style={{ height: 80, objectFit: 'cover' }}
                          />
                          {asset.materialId && (
                            <Tag
                              color="green"
                              size="small"
                              style={{
                                position: 'absolute',
                                top: 4,
                                right: 4,
                                fontSize: 10
                              }}
                            >
                              <DollarOutlined />
                            </Tag>
                          )}
                        </div>
                      }
                      bodyStyle={{ padding: 8 }}
                      onClick={() => handleAssetDrop(asset)}
                    >
                      <div style={{ textAlign: 'center' }}>
                        <Text style={{ fontSize: 12 }} ellipsis>{asset.name}</Text>
                        {asset.unitPrice && (
                          <div style={{ marginTop: 2 }}>
                            <Text style={{ fontSize: 10, color: '#f5222d' }}>
                              ¥{asset.unitPrice}/{asset.unit}
                            </Text>
                          </div>
                        )}
                        <div style={{ marginTop: 4 }}>
                          {asset.tags.slice(0, 1).map(tag => (
                            <Tag key={tag} size="small" style={{ fontSize: 10 }}>{tag}</Tag>
                          ))}
                        </div>
                      </div>
                    </Card>
                  </Col>
                ))}
              </Row>
            </div>
          </TabPane>

          <TabPane tab="材质库" key="materials">
            <div style={{ padding: '8px 16px', height: '100%', overflow: 'auto' }}>
              <Row gutter={[8, 8]}>
                {materials.map(material => (
                  <Col key={material.id}>
                    <Card
                      size="small"
                      hoverable
                      style={{ width: 100 }}
                      cover={
                        <img 
                          alt={material.name} 
                          src={material.thumbnail}
                          style={{ height: 60, objectFit: 'cover' }}
                        />
                      }
                      bodyStyle={{ padding: 6 }}
                    >
                      <Text style={{ fontSize: 12 }} ellipsis>{material.name}</Text>
                    </Card>
                  </Col>
                ))}
              </Row>
            </div>
          </TabPane>
        </Tabs>
      </div>

      {/* 成本计算面板 */}
      <Drawer
        title="材料成本计算"
        placement="right"
        width={400}
        open={isCostPanelVisible}
        onClose={() => setIsCostPanelVisible(false)}
        extra={
          <Space>
            <Button icon={<FileExcelOutlined />} onClick={exportCostReport}>
              导出报告
            </Button>
          </Space>
        }
      >
        <Space direction="vertical" style={{ width: '100%' }}>
          {/* 成本统计 */}
          <Card size="small">
            <Row gutter={16}>
              <Col span={12}>
                <Statistic
                  title="材料总数"
                  value={costCalculation.totalMaterials}
                  suffix="件"
                  valueStyle={{ fontSize: 16 }}
                />
              </Col>
              <Col span={12}>
                <Statistic
                  title="总成本"
                  value={costCalculation.totalCost}
                  prefix="¥"
                  precision={2}
                  valueStyle={{ fontSize: 16, color: '#f5222d' }}
                />
              </Col>
            </Row>
          </Card>

          {/* 材料明细 */}
          <Card size="small" title="材料明细">
            {costCalculation.materialBreakdown.length > 0 ? (
              <Table
                size="small"
                dataSource={costCalculation.materialBreakdown}
                rowKey="materialId"
                pagination={false}
                columns={[
                  {
                    title: '材料名称',
                    dataIndex: 'materialName',
                    key: 'materialName',
                    render: (name) => (
                      <Text style={{ fontSize: 12 }}>{name}</Text>
                    )
                  },
                  {
                    title: '数量',
                    dataIndex: 'quantity',
                    key: 'quantity',
                    width: 60,
                    render: (qty) => (
                      <Text style={{ fontSize: 12 }}>{qty}</Text>
                    )
                  },
                  {
                    title: '单价',
                    dataIndex: 'unitPrice',
                    key: 'unitPrice',
                    width: 80,
                    render: (price) => (
                      <Text style={{ fontSize: 12 }}>¥{price}</Text>
                    )
                  },
                  {
                    title: '小计',
                    dataIndex: 'totalPrice',
                    key: 'totalPrice',
                    width: 80,
                    render: (total) => (
                      <Text strong style={{ fontSize: 12, color: '#f5222d' }}>
                        ¥{total.toFixed(2)}
                      </Text>
                    )
                  },
                  {
                    title: '操作',
                    key: 'action',
                    width: 50,
                    render: (_, record) => (
                      <Button
                        type="text"
                        size="small"
                        icon={<DeleteOutlined />}
                        onClick={() => removeMaterialUsage(record.materialId)}
                        style={{ color: '#f5222d' }}
                      />
                    )
                  }
                ]}
              />
            ) : (
              <div style={{ textAlign: 'center', padding: '20px 0', color: '#999' }}>
                <DollarOutlined style={{ fontSize: 32, marginBottom: 8 }} />
                <div>暂无材料使用记录</div>
                <div style={{ fontSize: 12 }}>从素材库拖拽带有价格标识的素材到场景中</div>
              </div>
            )}
          </Card>

          {/* 成本说明 */}
          <Card size="small" title="说明">
            <Space direction="vertical" style={{ width: '100%' }}>
              <div style={{ fontSize: 12 }}>
                <Tag color="green" size="small">
                  <DollarOutlined />
                </Tag>
                <Text style={{ fontSize: 12 }}>绿色标识的素材已关联材料库价格</Text>
              </div>
              <div style={{ fontSize: 12 }}>
                • 点击素材自动添加到场景并计算成本
              </div>
              <div style={{ fontSize: 12 }}>
                • 成本数据实时同步到项目管理平台
              </div>
              <div style={{ fontSize: 12 }}>
                • 可导出详细的材料成本报告
              </div>
            </Space>
          </Card>
        </Space>
      </Drawer>
    </Layout>
  )
}

export default Editor3DPage
