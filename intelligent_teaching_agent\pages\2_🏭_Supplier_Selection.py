"""
供应商选择模块
结合AHP层次分析法和TOPSIS决策方法，教授多标准供应商评估技术
"""

import streamlit as st
import pandas as pd
import numpy as np
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots

# 页面配置
st.set_page_config(
    page_title="供应商选择",
    page_icon="🏭",
    layout="wide"
)

# 自定义CSS
st.markdown("""
<style>
    .metric-card {
        background-color: #f8f9fa;
        padding: 1rem;
        border-radius: 10px;
        border-left: 4px solid #2ca02c;
        margin: 0.5rem 0;
    }
    .supplier-card {
        background-color: #e8f5e8;
        padding: 1rem;
        border-radius: 10px;
        border: 2px solid #2ca02c;
        margin: 0.5rem 0;
    }
    .ranking-header {
        background: linear-gradient(90deg, #2ca02c, #17a2b8);
        color: white;
        padding: 0.5rem;
        border-radius: 5px;
        text-align: center;
        font-weight: bold;
    }
</style>
""", unsafe_allow_html=True)

def create_ahp_matrix(price_quality, price_delivery, quality_delivery):
    """根据成对比较结果创建AHP判断矩阵"""
    # 创建3x3判断矩阵
    matrix = np.array([
        [1.0, price_quality, price_delivery],
        [1.0/price_quality, 1.0, quality_delivery],
        [1.0/price_delivery, 1.0/quality_delivery, 1.0]
    ])
    return matrix

def calculate_ahp_weights(matrix):
    """计算AHP权重"""
    # 计算特征向量（权重）
    eigenvalues, eigenvectors = np.linalg.eig(matrix)
    max_eigenvalue_index = np.argmax(eigenvalues.real)
    weights = eigenvectors[:, max_eigenvalue_index].real
    weights = weights / np.sum(weights)
    
    # 计算一致性比率
    n = len(matrix)
    max_eigenvalue = eigenvalues[max_eigenvalue_index].real
    ci = (max_eigenvalue - n) / (n - 1)
    
    # 随机一致性指标
    ri_values = {3: 0.58, 4: 0.90, 5: 1.12, 6: 1.24, 7: 1.32, 8: 1.41, 9: 1.45}
    ri = ri_values.get(n, 0)
    
    cr = ci / ri if ri > 0 else 0
    
    return weights, cr

def generate_supplier_data():
    """生成示例供应商数据"""
    np.random.seed(42)
    
    suppliers = [
        "华东制造有限公司",
        "精工科技集团", 
        "绿色环保材料",
        "快速物流供应",
        "品质优先企业",
        "成本控制专家",
        "创新技术公司"
    ]
    
    # 生成评估数据（分数越高越好，但价格相反）
    data = []
    for supplier in suppliers:
        # 价格（1-10，分数越高表示价格越低，即越好）
        price_score = np.random.uniform(3, 9)
        # 质量（1-10，分数越高越好）
        quality_score = np.random.uniform(4, 9.5)
        # 交付表现（1-10，分数越高越好）
        delivery_score = np.random.uniform(3.5, 9)
        
        data.append({
            'supplier': supplier,
            'price': price_score,
            'quality': quality_score,
            'delivery': delivery_score
        })
    
    return pd.DataFrame(data)

def normalize_matrix(matrix):
    """标准化决策矩阵（TOPSIS方法）"""
    # 计算每列的平方和
    col_sums = np.sqrt(np.sum(matrix**2, axis=0))
    # 标准化
    normalized = matrix / col_sums
    return normalized

def calculate_topsis_scores(data, weights):
    """计算TOPSIS综合得分"""
    # 提取决策矩阵
    matrix = data[['price', 'quality', 'delivery']].values
    
    # 标准化矩阵
    normalized_matrix = normalize_matrix(matrix)
    
    # 加权标准化矩阵
    weighted_matrix = normalized_matrix * weights
    
    # 确定正理想解和负理想解
    # 所有标准都是越大越好（价格已经转换为价格优势）
    positive_ideal = np.max(weighted_matrix, axis=0)
    negative_ideal = np.min(weighted_matrix, axis=0)
    
    # 计算到正理想解和负理想解的距离
    d_positive = np.sqrt(np.sum((weighted_matrix - positive_ideal)**2, axis=1))
    d_negative = np.sqrt(np.sum((weighted_matrix - negative_ideal)**2, axis=1))
    
    # 计算相对接近度
    scores = d_negative / (d_positive + d_negative)
    
    return scores, normalized_matrix

def create_radar_chart(data, top_n=5):
    """创建供应商能力雷达图"""
    # 选择前N名供应商
    top_suppliers = data.head(top_n)
    
    fig = go.Figure()
    
    categories = ['价格优势', '质量水平', '交付表现']
    
    colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd']
    
    for i, (_, supplier) in enumerate(top_suppliers.iterrows()):
        values = [supplier['price'], supplier['quality'], supplier['delivery']]
        values += [values[0]]  # 闭合雷达图
        
        fig.add_trace(go.Scatterpolar(
            r=values,
            theta=categories + [categories[0]],
            fill='toself',
            name=supplier['supplier'],
            line_color=colors[i % len(colors)],
            fillcolor=colors[i % len(colors)],
            opacity=0.6
        ))
    
    fig.update_layout(
        polar=dict(
            radialaxis=dict(
                visible=True,
                range=[0, 10]
            )),
        showlegend=True,
        title="供应商能力对比雷达图",
        height=500
    )
    
    return fig

def main():
    st.title("🏭 供应商选择")
    st.markdown("结合AHP层次分析法和TOPSIS决策方法，进行多标准供应商评估")
    
    # 侧边栏控制
    st.sidebar.header("⚖️ 决策标准权重设置")
    st.sidebar.markdown("通过成对比较确定各评估标准的相对重要性")
    
    # AHP成对比较滑块
    st.sidebar.subheader("成对比较")
    
    # 价格 vs 质量
    price_quality = st.sidebar.select_slider(
        "价格 vs 质量",
        options=[1/9, 1/7, 1/5, 1/3, 1, 3, 5, 7, 9],
        value=1,
        format_func=lambda x: {
            1/9: "质量极端重要", 1/7: "质量很重要", 1/5: "质量重要",
            1/3: "质量稍微重要", 1: "同等重要", 3: "价格稍微重要",
            5: "价格重要", 7: "价格很重要", 9: "价格极端重要"
        }[x],
        help="比较价格和质量的相对重要性"
    )
    
    # 价格 vs 交付
    price_delivery = st.sidebar.select_slider(
        "价格 vs 交付表现",
        options=[1/9, 1/7, 1/5, 1/3, 1, 3, 5, 7, 9],
        value=1,
        format_func=lambda x: {
            1/9: "交付极端重要", 1/7: "交付很重要", 1/5: "交付重要",
            1/3: "交付稍微重要", 1: "同等重要", 3: "价格稍微重要",
            5: "价格重要", 7: "价格很重要", 9: "价格极端重要"
        }[x],
        help="比较价格和交付表现的相对重要性"
    )
    
    # 质量 vs 交付
    quality_delivery = st.sidebar.select_slider(
        "质量 vs 交付表现",
        options=[1/9, 1/7, 1/5, 1/3, 1, 3, 5, 7, 9],
        value=1,
        format_func=lambda x: {
            1/9: "交付极端重要", 1/7: "交付很重要", 1/5: "交付重要",
            1/3: "交付稍微重要", 1: "同等重要", 3: "质量稍微重要",
            5: "质量重要", 7: "质量很重要", 9: "质量极端重要"
        }[x],
        help="比较质量和交付表现的相对重要性"
    )
    
    # 开始评估按钮
    if st.sidebar.button("🚀 开始评估供应商", type="primary"):
        with st.spinner("正在进行供应商评估..."):
            # 创建AHP判断矩阵
            ahp_matrix = create_ahp_matrix(price_quality, price_delivery, quality_delivery)
            
            # 计算权重
            weights, consistency_ratio = calculate_ahp_weights(ahp_matrix)
            
            # 生成供应商数据
            supplier_data = generate_supplier_data()
            
            # 计算TOPSIS得分
            topsis_scores, normalized_matrix = calculate_topsis_scores(supplier_data, weights)
            
            # 添加得分和排名
            supplier_data['topsis_score'] = topsis_scores
            supplier_data['rank'] = supplier_data['topsis_score'].rank(ascending=False, method='min').astype(int)
            
            # 按排名排序
            supplier_data = supplier_data.sort_values('rank')
            
            # 存储结果
            st.session_state.evaluation_results = {
                'supplier_data': supplier_data,
                'weights': weights,
                'consistency_ratio': consistency_ratio,
                'normalized_matrix': normalized_matrix,
                'ahp_matrix': ahp_matrix
            }
    
    # 显示结果
    if 'evaluation_results' in st.session_state:
        results = st.session_state.evaluation_results
        
        # 权重信息
        st.subheader("📊 评估标准权重")
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.metric("价格权重", f"{results['weights'][0]:.3f}")
        with col2:
            st.metric("质量权重", f"{results['weights'][1]:.3f}")
        with col3:
            st.metric("交付权重", f"{results['weights'][2]:.3f}")
        with col4:
            consistency_status = "✅ 一致" if results['consistency_ratio'] < 0.1 else "⚠️ 需调整"
            st.metric("一致性检验", consistency_status)
        
        if results['consistency_ratio'] >= 0.1:
            st.warning(f"一致性比率 {results['consistency_ratio']:.3f} 较高，建议调整成对比较结果")
        
        # 最终排名报告
        st.subheader("🏆 供应商排名报告")
        
        # 突出显示最优供应商
        best_supplier = results['supplier_data'].iloc[0]
        st.markdown(f"""
        <div class="supplier-card">
            <h3>🥇 推荐供应商: {best_supplier['supplier']}</h3>
            <p><strong>综合得分:</strong> {best_supplier['topsis_score']:.4f}</p>
            <p><strong>优势分析:</strong> 在当前权重设置下表现最佳</p>
        </div>
        """, unsafe_allow_html=True)
        
        # 详细排名表格
        display_data = results['supplier_data'].copy()
        display_data['价格优势'] = display_data['price'].round(2)
        display_data['质量水平'] = display_data['quality'].round(2)
        display_data['交付表现'] = display_data['delivery'].round(2)
        display_data['综合得分'] = display_data['topsis_score'].round(4)
        display_data['排名'] = display_data['rank']
        
        final_table = display_data[['排名', 'supplier', '综合得分', '价格优势', '质量水平', '交付表现']]
        final_table.columns = ['排名', '供应商名称', '综合得分', '价格优势', '质量水平', '交付表现']
        
        st.dataframe(
            final_table,
            use_container_width=True,
            hide_index=True
        )
        
        # 供应商能力雷达图
        st.subheader("📈 供应商能力对比")
        
        col1, col2 = st.columns([2, 1])
        
        with col1:
            radar_fig = create_radar_chart(results['supplier_data'])
            st.plotly_chart(radar_fig, use_container_width=True)
        
        with col2:
            st.markdown("**雷达图说明:**")
            st.markdown("""
            - **价格优势**: 分数越高表示价格越有竞争力
            - **质量水平**: 分数越高表示质量越好
            - **交付表现**: 分数越高表示交付能力越强
            
            **图形解读:**
            - 多边形面积越大，综合能力越强
            - 形状越规则，各项能力越均衡
            - 突出的角表示该项能力的优势
            """)
        
        # 决策分析
        st.subheader("🔍 决策分析")
        
        tab1, tab2 = st.tabs(["权重敏感性", "供应商对比"])
        
        with tab1:
            st.markdown("**权重设置对排名的影响:**")
            
            # 显示AHP矩阵
            st.markdown("**AHP判断矩阵:**")
            matrix_df = pd.DataFrame(
                results['ahp_matrix'],
                columns=['价格', '质量', '交付'],
                index=['价格', '质量', '交付']
            )
            st.dataframe(matrix_df.round(3))
            
            st.markdown(f"**一致性比率:** {results['consistency_ratio']:.4f}")
            if results['consistency_ratio'] < 0.1:
                st.success("✅ 判断矩阵具有良好的一致性")
            else:
                st.warning("⚠️ 判断矩阵一致性较差，建议重新调整成对比较")
        
        with tab2:
            st.markdown("**前三名供应商详细对比:**")
            
            top3 = results['supplier_data'].head(3)
            
            for i, (_, supplier) in enumerate(top3.iterrows()):
                rank_emoji = ["🥇", "🥈", "🥉"][i]
                st.markdown(f"""
                <div class="metric-card">
                    <h4>{rank_emoji} {supplier['supplier']}</h4>
                    <p><strong>综合得分:</strong> {supplier['topsis_score']:.4f}</p>
                    <p><strong>价格优势:</strong> {supplier['price']:.2f}/10 | 
                       <strong>质量水平:</strong> {supplier['quality']:.2f}/10 | 
                       <strong>交付表现:</strong> {supplier['delivery']:.2f}/10</p>
                </div>
                """, unsafe_allow_html=True)
    
    else:
        # 初始状态显示
        st.info("👈 请在左侧设置评估标准权重并点击'开始评估供应商'")
        
        # 功能介绍
        st.markdown("""
        ### 📚 学习目标
        
        通过本模块，您将学会：
        
        1. **AHP层次分析法**: 掌握多标准决策中权重确定的科学方法
        2. **TOPSIS决策技术**: 学习理想解排序的决策分析方法
        3. **成对比较**: 理解如何将主观判断转化为客观权重
        4. **一致性检验**: 确保决策判断的逻辑一致性
        5. **可视化分析**: 通过雷达图等工具直观比较方案
        
        ### 🎯 操作步骤
        
        1. 在左侧进行三组成对比较，设定标准权重
        2. 点击"开始评估供应商"按钮
        3. 查看权重计算结果和一致性检验
        4. 分析供应商排名报告
        5. 通过雷达图对比供应商能力
        6. 进行决策敏感性分析
        
        ### 📊 评估标准说明
        
        - **价格**: 供应商的价格竞争力（分数越高表示价格越优惠）
        - **质量**: 产品或服务的质量水平（分数越高表示质量越好）
        - **交付**: 交付速度和可靠性（分数越高表示交付能力越强）
        """)

if __name__ == "__main__":
    main()
