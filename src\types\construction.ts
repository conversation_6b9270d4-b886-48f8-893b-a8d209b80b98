// 施工工序管理相关类型定义

export interface ConstructionTask {
  id: string
  name: string
  description: string
  category: 'preparation' | 'structure' | 'decoration' | 'installation' | 'finishing' | 'inspection'
  priority: 'low' | 'medium' | 'high' | 'urgent'
  status: 'pending' | 'in_progress' | 'completed' | 'delayed' | 'cancelled'
  progress: number // 0-100
  startDate: string
  endDate: string
  actualStartDate?: string
  actualEndDate?: string
  estimatedDuration: number // 天数
  actualDuration?: number // 天数
  assignedTo: string[] // 分配给的学生ID
  dependencies: string[] // 依赖的任务ID
  materials: string[] // 需要的材料ID
  notes: string
  createdBy: string // 创建者ID（老师）
  createdAt: string
  updatedAt: string
  projectId: string
}

export interface ConstructionPhase {
  id: string
  name: string
  description: string
  order: number
  startDate: string
  endDate: string
  status: 'not_started' | 'in_progress' | 'completed' | 'delayed'
  progress: number // 0-100
  tasks: ConstructionTask[]
  projectId: string
}

export interface ProjectSchedule {
  id: string
  projectId: string
  projectName: string
  totalDuration: number // 总工期（天）
  startDate: string
  endDate: string
  actualStartDate?: string
  actualEndDate?: string
  status: 'planning' | 'in_progress' | 'completed' | 'delayed' | 'cancelled'
  progress: number // 0-100
  phases: ConstructionPhase[]
  milestones: Milestone[]
  createdBy: string
  createdAt: string
  updatedAt: string
}

export interface Milestone {
  id: string
  name: string
  description: string
  date: string
  status: 'pending' | 'completed' | 'missed'
  type: 'start' | 'phase_completion' | 'inspection' | 'delivery' | 'custom'
  projectId: string
}

export interface TaskProgress {
  taskId: string
  date: string
  progress: number
  notes: string
  reportedBy: string // 学生ID
  verifiedBy?: string // 老师ID
  isVerified: boolean
  createdAt: string
}

export interface WorkLog {
  id: string
  taskId: string
  studentId: string
  date: string
  hoursWorked: number
  description: string
  issues?: string
  photos?: string[]
  createdAt: string
}

// 甘特图数据格式
export interface GanttTask {
  id: string
  name: string
  start: Date
  end: Date
  progress: number
  dependencies?: string[]
  type: 'task' | 'milestone' | 'phase'
  color?: string
  assignee?: string
}

// 图表数据格式
export interface ChartData {
  labels: string[]
  datasets: {
    label: string
    data: number[]
    backgroundColor?: string[]
    borderColor?: string[]
  }[]
}

// 工序统计数据
export interface ConstructionStats {
  totalTasks: number
  completedTasks: number
  inProgressTasks: number
  delayedTasks: number
  overallProgress: number
  onTimeRate: number
  averageTaskDuration: number
  totalStudents: number
  activeStudents: number
}

// 用户角色权限
export interface UserRole {
  id: string
  role: 'student' | 'teacher' | 'admin'
  permissions: {
    canCreateTasks: boolean
    canEditTasks: boolean
    canDeleteTasks: boolean
    canViewAllProjects: boolean
    canAssignTasks: boolean
    canVerifyProgress: boolean
  }
}

// API 响应格式
export interface ApiResponse<T> {
  success: boolean
  data: T
  message?: string
  error?: string
}

// 筛选和排序选项
export interface TaskFilter {
  status?: ConstructionTask['status'][]
  priority?: ConstructionTask['priority'][]
  category?: ConstructionTask['category'][]
  assignedTo?: string[]
  dateRange?: {
    start: string
    end: string
  }
}

export interface TaskSort {
  field: keyof ConstructionTask
  order: 'asc' | 'desc'
}
