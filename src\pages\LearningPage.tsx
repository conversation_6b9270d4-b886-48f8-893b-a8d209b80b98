import React, { useState, useEffect } from 'react'
import { Card, Row, Col, Typography, Button, Progress, List, Avatar, Tag, Space, Tabs, Statistic } from 'antd'
import { useNavigate } from 'react-router-dom'
import {
  BookOutlined,
  FileTextOutlined,
  EditOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  TrophyOutlined,
  UserOutlined,
  PlayCircleOutlined,
  DownloadOutlined,
} from '@ant-design/icons'
import { useAuthStore } from '@/store/authStore'
import { ROUTES } from '@/utils/constants'

const { Title, Paragraph, Text } = Typography
const { TabPane } = Tabs

interface Course {
  id: string
  title: string
  description: string
  instructor: string
  progress: number
  totalLessons: number
  completedLessons: number
  thumbnail: string
  status: 'not_started' | 'in_progress' | 'completed'
  lastAccessed?: string
}

interface Assignment {
  id: string
  title: string
  courseTitle: string
  dueDate: string
  status: 'pending' | 'submitted' | 'graded'
  score?: number
  maxScore: number
  type: 'theory' | 'practical'
}

interface Exam {
  id: string
  title: string
  courseTitle: string
  startTime: string
  duration: number
  status: 'upcoming' | 'in_progress' | 'completed'
  score?: number
  maxScore: number
}

const LearningPage: React.FC = () => {
  const navigate = useNavigate()
  const { user } = useAuthStore()
  const [activeTab, setActiveTab] = useState('courses')

  // 模拟数据
  const [courses] = useState<Course[]>([
    {
      id: '1',
      title: '会展策划基础',
      description: '学习会展策划的基本理论和实践方法',
      instructor: '张教授',
      progress: 75,
      totalLessons: 12,
      completedLessons: 9,
      thumbnail: '',
      status: 'in_progress',
      lastAccessed: '2024-01-20'
    },
    {
      id: '2',
      title: '展示设计原理',
      description: '掌握展示空间设计的基本原理和方法',
      instructor: '李老师',
      progress: 100,
      totalLessons: 10,
      completedLessons: 10,
      thumbnail: '',
      status: 'completed',
      lastAccessed: '2024-01-18'
    },
    {
      id: '3',
      title: '3D建模技术',
      description: '学习3D建模软件的使用和建模技巧',
      instructor: '王老师',
      progress: 30,
      totalLessons: 15,
      completedLessons: 4,
      thumbnail: '',
      status: 'in_progress',
      lastAccessed: '2024-01-19'
    }
  ])

  const [assignments] = useState<Assignment[]>([
    {
      id: '1',
      title: '会展方案设计作业',
      courseTitle: '会展策划基础',
      dueDate: '2024-01-25',
      status: 'pending',
      maxScore: 100,
      type: 'practical'
    },
    {
      id: '2',
      title: '展示设计理论测试',
      courseTitle: '展示设计原理',
      dueDate: '2024-01-22',
      status: 'graded',
      score: 85,
      maxScore: 100,
      type: 'theory'
    }
  ])

  const [exams] = useState<Exam[]>([
    {
      id: '1',
      title: '期中考试',
      courseTitle: '会展策划基础',
      startTime: '2024-01-30 09:00',
      duration: 120,
      status: 'upcoming',
      maxScore: 100
    },
    {
      id: '2',
      title: '3D建模技能测试',
      courseTitle: '3D建模技术',
      startTime: '2024-01-28 14:00',
      duration: 90,
      status: 'upcoming',
      maxScore: 100
    }
  ])

  const getStatusColor = (status: string) => {
    const colorMap: Record<string, string> = {
      not_started: 'default',
      in_progress: 'processing',
      completed: 'success',
      pending: 'warning',
      submitted: 'processing',
      graded: 'success',
      upcoming: 'blue',
    }
    return colorMap[status] || 'default'
  }

  const getStatusText = (status: string) => {
    const textMap: Record<string, string> = {
      not_started: '未开始',
      in_progress: '学习中',
      completed: '已完成',
      pending: '待提交',
      submitted: '已提交',
      graded: '已批改',
      upcoming: '即将开始',
    }
    return textMap[status] || status
  }

  const handleCourseClick = (courseId: string) => {
    navigate(`${ROUTES.LEARNING_COURSES}/${courseId}`)
  }

  const handleAssignmentClick = (assignmentId: string) => {
    navigate(`${ROUTES.LEARNING_ASSIGNMENTS}/${assignmentId}`)
  }

  const handleExamClick = (examId: string) => {
    navigate(`${ROUTES.LEARNING_EXAMS}/${examId}`)
  }

  return (
    <div style={{ padding: '24px' }}>
      {/* 页面标题 */}
      <div style={{ marginBottom: 24 }}>
        <Title level={2}>教管学考平台</Title>
        <Paragraph>
          在线学习、课程管理、作业考试一体化教学平台
        </Paragraph>
      </div>

      {/* 学习统计 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="在学课程"
              value={courses.filter(c => c.status === 'in_progress').length}
              prefix={<BookOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="已完成课程"
              value={courses.filter(c => c.status === 'completed').length}
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="待提交作业"
              value={assignments.filter(a => a.status === 'pending').length}
              prefix={<FileTextOutlined />}
              valueStyle={{ color: '#fa8c16' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="即将考试"
              value={exams.filter(e => e.status === 'upcoming').length}
              prefix={<EditOutlined />}
              valueStyle={{ color: '#f5222d' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 主要内容区域 */}
      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane tab="我的课程" key="courses">
          <Row gutter={[16, 16]}>
            {courses.map(course => (
              <Col xs={24} sm={12} lg={8} key={course.id}>
                <Card
                  hoverable
                  cover={
                    <div style={{ 
                      height: 120, 
                      background: '#f0f2f5', 
                      display: 'flex', 
                      alignItems: 'center', 
                      justifyContent: 'center' 
                    }}>
                      <BookOutlined style={{ fontSize: 48, color: '#d9d9d9' }} />
                    </div>
                  }
                  actions={[
                    <Button 
                      type="primary" 
                      icon={<PlayCircleOutlined />}
                      onClick={() => handleCourseClick(course.id)}
                    >
                      继续学习
                    </Button>
                  ]}
                >
                  <Card.Meta
                    title={course.title}
                    description={
                      <Space direction="vertical" style={{ width: '100%' }}>
                        <Text type="secondary">{course.description}</Text>
                        <div>
                          <Text strong>讲师：</Text>
                          <Text>{course.instructor}</Text>
                        </div>
                        <div>
                          <Text strong>进度：</Text>
                          <Progress 
                            percent={course.progress} 
                            size="small" 
                            style={{ marginTop: 4 }}
                          />
                          <Text type="secondary">
                            {course.completedLessons}/{course.totalLessons} 课时
                          </Text>
                        </div>
                        <Tag color={getStatusColor(course.status)}>
                          {getStatusText(course.status)}
                        </Tag>
                      </Space>
                    }
                  />
                </Card>
              </Col>
            ))}
          </Row>
        </TabPane>

        <TabPane tab="我的作业" key="assignments">
          <List
            itemLayout="horizontal"
            dataSource={assignments}
            renderItem={assignment => (
              <List.Item
                actions={[
                  <Button 
                    type={assignment.status === 'pending' ? 'primary' : 'default'}
                    onClick={() => handleAssignmentClick(assignment.id)}
                  >
                    {assignment.status === 'pending' ? '提交作业' : '查看详情'}
                  </Button>
                ]}
              >
                <List.Item.Meta
                  avatar={<Avatar icon={<FileTextOutlined />} />}
                  title={
                    <Space>
                      {assignment.title}
                      <Tag color={getStatusColor(assignment.status)}>
                        {getStatusText(assignment.status)}
                      </Tag>
                      <Tag color={assignment.type === 'practical' ? 'blue' : 'green'}>
                        {assignment.type === 'practical' ? '实践作业' : '理论作业'}
                      </Tag>
                    </Space>
                  }
                  description={
                    <Space direction="vertical">
                      <Text>课程：{assignment.courseTitle}</Text>
                      <Text type="secondary">
                        <ClockCircleOutlined /> 截止时间：{assignment.dueDate}
                      </Text>
                      {assignment.score !== undefined && (
                        <Text>
                          <TrophyOutlined /> 得分：{assignment.score}/{assignment.maxScore}
                        </Text>
                      )}
                    </Space>
                  }
                />
              </List.Item>
            )}
          />
        </TabPane>

        <TabPane tab="我的考试" key="exams">
          <List
            itemLayout="horizontal"
            dataSource={exams}
            renderItem={exam => (
              <List.Item
                actions={[
                  <Button 
                    type={exam.status === 'upcoming' ? 'primary' : 'default'}
                    onClick={() => handleExamClick(exam.id)}
                  >
                    {exam.status === 'upcoming' ? '参加考试' : '查看结果'}
                  </Button>
                ]}
              >
                <List.Item.Meta
                  avatar={<Avatar icon={<EditOutlined />} />}
                  title={
                    <Space>
                      {exam.title}
                      <Tag color={getStatusColor(exam.status)}>
                        {getStatusText(exam.status)}
                      </Tag>
                    </Space>
                  }
                  description={
                    <Space direction="vertical">
                      <Text>课程：{exam.courseTitle}</Text>
                      <Text type="secondary">
                        <ClockCircleOutlined /> 考试时间：{exam.startTime}
                      </Text>
                      <Text type="secondary">
                        考试时长：{exam.duration}分钟
                      </Text>
                      {exam.score !== undefined && (
                        <Text>
                          <TrophyOutlined /> 得分：{exam.score}/{exam.maxScore}
                        </Text>
                      )}
                    </Space>
                  }
                />
              </List.Item>
            )}
          />
        </TabPane>
      </Tabs>
    </div>
  )
}

export default LearningPage
