<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>施工工序管理 - 角色权限演示</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
            background-color: #f5f5f5;
            margin: 0;
            padding: 24px;
            line-height: 1.6;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
        }
        .header {
            background: white;
            padding: 24px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 24px;
        }
        .title {
            font-size: 28px;
            color: #1890ff;
            margin-bottom: 8px;
        }
        .subtitle {
            color: #666;
            font-size: 16px;
        }
        .role-switch {
            background: #e6f7ff;
            border: 1px solid #91d5ff;
            border-radius: 6px;
            padding: 16px;
            margin-bottom: 24px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .role-info {
            display: flex;
            align-items: center;
            gap: 16px;
        }
        .role-badge {
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }
        .role-teacher {
            background: #f6ffed;
            color: #52c41a;
            border: 1px solid #b7eb8f;
        }
        .role-student {
            background: #fff7e6;
            color: #fa8c16;
            border: 1px solid #ffd591;
        }
        .btn {
            padding: 8px 16px;
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            background: white;
            cursor: pointer;
            transition: all 0.3s;
        }
        .btn-primary {
            background: #1890ff;
            color: white;
            border-color: #1890ff;
        }
        .btn:hover {
            border-color: #1890ff;
            color: #1890ff;
        }
        .btn-primary:hover {
            background: #40a9ff;
            border-color: #40a9ff;
        }
        .comparison-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 24px;
            margin-bottom: 24px;
        }
        .comparison-card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .card-header {
            padding: 16px 20px;
            border-bottom: 1px solid #f0f0f0;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .card-content {
            padding: 20px;
        }
        .feature-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid #f5f5f5;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .feature-list li:last-child {
            border-bottom: none;
        }
        .icon-check {
            color: #52c41a;
            font-weight: bold;
        }
        .icon-cross {
            color: #f5222d;
            font-weight: bold;
        }
        .demo-section {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            padding: 24px;
            margin-bottom: 24px;
        }
        .demo-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 16px;
            color: #262626;
        }
        .task-item {
            border: 1px solid #f0f0f0;
            border-radius: 6px;
            padding: 16px;
            margin-bottom: 16px;
            background: #fafafa;
        }
        .task-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 12px;
        }
        .task-info {
            flex: 1;
        }
        .task-name {
            font-weight: 600;
            margin-bottom: 4px;
        }
        .task-desc {
            font-size: 14px;
            color: #666;
            margin-bottom: 8px;
        }
        .task-tags {
            display: flex;
            gap: 4px;
        }
        .tag {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
        }
        .tag-blue { background: #e6f7ff; color: #1890ff; }
        .tag-green { background: #f6ffed; color: #52c41a; }
        .tag-orange { background: #fff7e6; color: #fa8c16; }
        .tag-red { background: #fff2f0; color: #f5222d; }
        .task-actions {
            display: flex;
            gap: 8px;
        }
        .btn-small {
            padding: 4px 8px;
            font-size: 12px;
            border-radius: 4px;
        }
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }
        .modal-content {
            background: white;
            border-radius: 8px;
            width: 90%;
            max-width: 800px;
            max-height: 90%;
            overflow-y: auto;
        }
        .modal-header {
            padding: 16px 20px;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .modal-title {
            font-size: 16px;
            font-weight: 600;
            margin: 0;
        }
        .modal-body {
            padding: 20px;
        }
        .modal-footer {
            padding: 16px 20px;
            border-top: 1px solid #f0f0f0;
            display: flex;
            justify-content: flex-end;
            gap: 8px;
        }
        .close-btn {
            background: none;
            border: none;
            font-size: 20px;
            cursor: pointer;
            color: #666;
        }
        .detail-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 16px;
            margin-bottom: 16px;
        }
        .detail-item {
            margin-bottom: 8px;
        }
        .detail-label {
            font-weight: 600;
            margin-right: 8px;
        }
        .progress-bar {
            width: 100%;
            height: 8px;
            background: #f5f5f5;
            border-radius: 4px;
            overflow: hidden;
            margin-top: 4px;
        }
        .progress-fill {
            height: 100%;
            background: #52c41a;
            border-radius: 4px;
            transition: width 0.3s ease;
        }
        .info-box {
            padding: 12px;
            border-radius: 6px;
            margin-top: 8px;
        }
        .info-box-blue {
            background: #e6f7ff;
            border: 1px solid #91d5ff;
        }
        .info-box-green {
            background: #f6ffed;
            border: 1px solid #b7eb8f;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 页面标题 -->
        <div class="header">
            <h1 class="title">🏗️ 施工工序管理 - 角色权限演示</h1>
            <p class="subtitle">
                展示学生端和教师端的不同功能权限，以及完善的任务详情查看页面
            </p>
        </div>

        <!-- 角色切换 -->
        <div class="role-switch">
            <div class="role-info">
                <span>当前角色：</span>
                <span id="current-role" class="role-badge role-teacher">👨‍🏫 教师</span>
                <span>用户：</span>
                <span id="current-user">张老师</span>
            </div>
            <button class="btn btn-primary" onclick="switchRole()">切换角色</button>
        </div>

        <!-- 功能对比 -->
        <div class="comparison-grid">
            <div class="comparison-card">
                <div class="card-header">
                    <span class="role-badge role-teacher">👨‍🏫</span>
                    <span>教师端功能</span>
                </div>
                <div class="card-content">
                    <ul class="feature-list">
                        <li><span class="icon-check">✓</span> 创建新任务</li>
                        <li><span class="icon-check">✓</span> 编辑所有任务</li>
                        <li><span class="icon-check">✓</span> 删除任务</li>
                        <li><span class="icon-check">✓</span> 查看任务详情</li>
                        <li><span class="icon-check">✓</span> 分配任务给学生</li>
                        <li><span class="icon-check">✓</span> 监控项目进度</li>
                        <li><span class="icon-check">✓</span> 查看统计报告</li>
                        <li><span class="icon-check">✓</span> 审核学生提交</li>
                    </ul>
                </div>
            </div>

            <div class="comparison-card">
                <div class="card-header">
                    <span class="role-badge role-student">👨‍🎓</span>
                    <span>学生端功能</span>
                </div>
                <div class="card-content">
                    <ul class="feature-list">
                        <li><span class="icon-cross">✗</span> 创建新任务</li>
                        <li><span class="icon-check">✓</span> 编辑分配给自己的任务</li>
                        <li><span class="icon-cross">✗</span> 删除任务</li>
                        <li><span class="icon-check">✓</span> 查看任务详情</li>
                        <li><span class="icon-cross">✗</span> 分配任务给学生</li>
                        <li><span class="icon-check">✓</span> 更新任务进度</li>
                        <li><span class="icon-check">✓</span> 查看项目概览</li>
                        <li><span class="icon-check">✓</span> 填写工作日志</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 任务列表演示 -->
        <div class="demo-section">
            <div class="demo-title">任务列表 - <span id="role-display">教师视图</span></div>
            
            <div style="margin-bottom: 16px;">
                <button id="new-task-btn" class="btn btn-primary" onclick="showMessage('新建任务功能')">+ 新建任务</button>
                <span style="margin-left: 16px; color: #666; font-size: 14px;">
                    <span id="permission-note">教师可以创建、编辑、删除所有任务</span>
                </span>
            </div>

            <!-- 任务1 -->
            <div class="task-item">
                <div class="task-header">
                    <div class="task-info">
                        <div class="task-name">场地清理与准备</div>
                        <div class="task-desc">清理施工现场，设置安全围挡，准备施工材料</div>
                        <div class="task-tags">
                            <span class="tag tag-red">高优先级</span>
                            <span class="tag tag-blue">准备工序</span>
                            <span class="tag tag-green">已完成</span>
                        </div>
                    </div>
                    <div class="task-actions" id="task1-actions">
                        <button class="btn btn-small" onclick="showTaskDetail('task1')">查看</button>
                        <button class="btn btn-small" onclick="showMessage('编辑任务功能')">编辑</button>
                        <button class="btn btn-small" style="color: #f5222d;" onclick="showMessage('删除任务功能')">删除</button>
                    </div>
                </div>
                <div style="font-size: 12px; color: #666;">
                    分配给：学生1, 学生2 | 进度：100% | 状态：已完成
                </div>
            </div>

            <!-- 任务2 -->
            <div class="task-item">
                <div class="task-header">
                    <div class="task-info">
                        <div class="task-name">基础结构搭建</div>
                        <div class="task-desc">搭建展台主体框架结构</div>
                        <div class="task-tags">
                            <span class="tag tag-red">高优先级</span>
                            <span class="tag tag-green">结构工序</span>
                            <span class="tag tag-orange">进行中</span>
                        </div>
                    </div>
                    <div class="task-actions" id="task2-actions">
                        <button class="btn btn-small" onclick="showTaskDetail('task2')">查看</button>
                        <button class="btn btn-small" onclick="showMessage('更新进度功能')">更新进度</button>
                    </div>
                </div>
                <div style="font-size: 12px; color: #666;">
                    分配给：<span style="color: #1890ff; font-weight: 500;">学生1(我)</span>, 学生3 | 进度：75% | 状态：进行中
                </div>
            </div>

            <!-- 任务3 -->
            <div class="task-item">
                <div class="task-header">
                    <div class="task-info">
                        <div class="task-name">电路安装</div>
                        <div class="task-desc">安装展台照明和电源系统</div>
                        <div class="task-tags">
                            <span class="tag tag-orange">中优先级</span>
                            <span class="tag tag-red">安装工序</span>
                            <span class="tag" style="background: #f5f5f5; color: #666;">待开始</span>
                        </div>
                    </div>
                    <div class="task-actions" id="task3-actions">
                        <button class="btn btn-small" onclick="showTaskDetail('task3')">查看</button>
                    </div>
                </div>
                <div style="font-size: 12px; color: #666;">
                    分配给：学生4, 学生5 | 进度：0% | 状态：待开始
                </div>
            </div>
        </div>

        <!-- 任务详情模态框 -->
        <div id="task-detail-modal" class="modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 class="modal-title">任务详情</h3>
                    <button class="close-btn" onclick="closeModal()">&times;</button>
                </div>
                <div class="modal-body" id="task-detail-content">
                    <!-- 动态内容 -->
                </div>
                <div class="modal-footer">
                    <button class="btn" onclick="closeModal()">关闭</button>
                    <button id="update-progress-btn" class="btn btn-primary" onclick="showMessage('更新进度功能')" style="display: none;">更新进度</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentRole = 'teacher';
        let currentUser = '张老师';

        function switchRole() {
            if (currentRole === 'teacher') {
                currentRole = 'student';
                currentUser = '学生1';
                document.getElementById('current-role').className = 'role-badge role-student';
                document.getElementById('current-role').textContent = '👨‍🎓 学生';
                document.getElementById('current-user').textContent = currentUser;
                document.getElementById('role-display').textContent = '学生视图';
                document.getElementById('permission-note').textContent = '学生只能查看和更新分配给自己的任务';
                
                // 隐藏新建任务按钮
                document.getElementById('new-task-btn').style.display = 'none';
                
                // 更新任务操作按钮
                updateTaskActions();
            } else {
                currentRole = 'teacher';
                currentUser = '张老师';
                document.getElementById('current-role').className = 'role-badge role-teacher';
                document.getElementById('current-role').textContent = '👨‍🏫 教师';
                document.getElementById('current-user').textContent = currentUser;
                document.getElementById('role-display').textContent = '教师视图';
                document.getElementById('permission-note').textContent = '教师可以创建、编辑、删除所有任务';
                
                // 显示新建任务按钮
                document.getElementById('new-task-btn').style.display = 'inline-block';
                
                // 更新任务操作按钮
                updateTaskActions();
            }
        }

        function updateTaskActions() {
            if (currentRole === 'student') {
                // 任务1：已完成，学生1参与 - 只能查看
                document.getElementById('task1-actions').innerHTML = `
                    <button class="btn btn-small" onclick="showTaskDetail('task1')">查看</button>
                `;
                
                // 任务2：进行中，学生1参与 - 可以查看和更新进度
                document.getElementById('task2-actions').innerHTML = `
                    <button class="btn btn-small" onclick="showTaskDetail('task2')">查看</button>
                    <button class="btn btn-small btn-primary" onclick="showMessage('更新进度功能')">更新进度</button>
                `;
                
                // 任务3：待开始，学生1不参与 - 只能查看
                document.getElementById('task3-actions').innerHTML = `
                    <button class="btn btn-small" onclick="showTaskDetail('task3')">查看</button>
                `;
            } else {
                // 教师：所有任务都可以编辑和删除
                document.getElementById('task1-actions').innerHTML = `
                    <button class="btn btn-small" onclick="showTaskDetail('task1')">查看</button>
                    <button class="btn btn-small" onclick="showMessage('编辑任务功能')">编辑</button>
                    <button class="btn btn-small" style="color: #f5222d;" onclick="showMessage('删除任务功能')">删除</button>
                `;
                
                document.getElementById('task2-actions').innerHTML = `
                    <button class="btn btn-small" onclick="showTaskDetail('task2')">查看</button>
                    <button class="btn btn-small" onclick="showMessage('编辑任务功能')">编辑</button>
                    <button class="btn btn-small" style="color: #f5222d;" onclick="showMessage('删除任务功能')">删除</button>
                `;
                
                document.getElementById('task3-actions').innerHTML = `
                    <button class="btn btn-small" onclick="showTaskDetail('task3')">查看</button>
                    <button class="btn btn-small" onclick="showMessage('编辑任务功能')">编辑</button>
                    <button class="btn btn-small" style="color: #f5222d;" onclick="showMessage('删除任务功能')">删除</button>
                `;
            }
        }

        function showTaskDetail(taskId) {
            const taskDetails = {
                task1: {
                    name: '场地清理与准备',
                    description: '清理施工现场，设置安全围挡，准备施工材料。确保施工区域安全，为后续工作做好准备。',
                    category: '准备工序',
                    priority: '高优先级',
                    status: '已完成',
                    progress: 100,
                    startDate: '2024-01-15',
                    endDate: '2024-01-17',
                    actualStartDate: '2024-01-15',
                    actualEndDate: '2024-01-17',
                    estimatedDuration: 2,
                    actualDuration: 2,
                    assignedTo: ['学生1', '学生2'],
                    notes: '按时完成，质量良好。学生配合度高，安全措施到位。',
                    canUpdate: false
                },
                task2: {
                    name: '基础结构搭建',
                    description: '搭建展台主体框架结构，包括立柱、横梁和连接件的安装。确保结构稳固可靠。',
                    category: '结构工序',
                    priority: '高优先级',
                    status: '进行中',
                    progress: 75,
                    startDate: '2024-01-18',
                    endDate: '2024-01-22',
                    actualStartDate: '2024-01-18',
                    estimatedDuration: 4,
                    assignedTo: ['学生1', '学生3'],
                    notes: '进展顺利，预计按时完成。学生1负责立柱安装，学生3负责横梁连接。',
                    canUpdate: currentRole === 'student'
                },
                task3: {
                    name: '电路安装',
                    description: '安装展台照明和电源系统，包括线路布置、开关插座安装和照明设备调试。',
                    category: '安装工序',
                    priority: '中优先级',
                    status: '待开始',
                    progress: 0,
                    startDate: '2024-01-23',
                    endDate: '2024-01-25',
                    estimatedDuration: 2,
                    assignedTo: ['学生4', '学生5'],
                    notes: '等待前置任务完成后开始。需要专业电工指导。',
                    canUpdate: false
                }
            };

            const task = taskDetails[taskId];
            const isAssignedToMe = task.assignedTo.includes('学生1') && currentRole === 'student';

            document.getElementById('task-detail-content').innerHTML = `
                <div class="detail-grid">
                    <div>
                        <div class="detail-item">
                            <span class="detail-label">任务名称：</span>
                            <span>${task.name}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">工序类型：</span>
                            <span class="tag tag-blue">${task.category}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">优先级：</span>
                            <span class="tag tag-red">${task.priority}</span>
                        </div>
                    </div>
                    <div>
                        <div class="detail-item">
                            <span class="detail-label">任务状态：</span>
                            <span class="tag ${task.status === '已完成' ? 'tag-green' : task.status === '进行中' ? 'tag-orange' : ''}">${task.status}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">完成进度：</span>
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: ${task.progress}%;"></div>
                            </div>
                            <div style="font-size: 12px; margin-top: 2px;">${task.progress}%</div>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">预计工期：</span>
                            <span>${task.estimatedDuration}天</span>
                            ${task.actualDuration ? `<span style="color: #666; margin-left: 8px;">(实际${task.actualDuration}天)</span>` : ''}
                        </div>
                    </div>
                </div>

                <div style="margin-bottom: 16px;">
                    <div class="detail-label">任务描述：</div>
                    <div class="info-box info-box-blue">
                        ${task.description}
                    </div>
                </div>

                <div class="detail-grid" style="margin-bottom: 16px;">
                    <div>
                        <div class="detail-label">计划时间：</div>
                        <div style="font-size: 14px; color: #666;">
                            开始：${task.startDate}<br>
                            结束：${task.endDate}
                        </div>
                    </div>
                    <div>
                        ${task.actualStartDate ? `
                            <div class="detail-label">实际时间：</div>
                            <div style="font-size: 14px; color: #666;">
                                开始：${task.actualStartDate}<br>
                                ${task.actualEndDate ? `结束：${task.actualEndDate}` : '进行中'}
                            </div>
                        ` : ''}
                    </div>
                </div>

                <div style="margin-bottom: 16px;">
                    <div class="detail-label">分配人员：</div>
                    <div style="margin-top: 8px;">
                        ${task.assignedTo.map(student => `
                            <span class="tag tag-blue" style="margin-right: 4px;">
                                ${student}${student === '学生1' && currentRole === 'student' ? ' (我)' : ''}
                            </span>
                        `).join('')}
                    </div>
                </div>

                ${task.notes ? `
                    <div style="margin-bottom: 16px;">
                        <div class="detail-label">备注信息：</div>
                        <div class="info-box info-box-green">
                            ${task.notes}
                        </div>
                    </div>
                ` : ''}

                <div style="margin-top: 16px; padding-top: 16px; border-top: 1px solid #f0f0f0; font-size: 12px; color: #999;">
                    <div>创建时间：2024-01-10 09:00:00</div>
                    <div>更新时间：2024-01-20 15:30:00</div>
                </div>
            `;

            // 显示或隐藏更新进度按钮
            const updateBtn = document.getElementById('update-progress-btn');
            if (isAssignedToMe && task.status !== '已完成') {
                updateBtn.style.display = 'inline-block';
            } else {
                updateBtn.style.display = 'none';
            }

            document.getElementById('task-detail-modal').style.display = 'flex';
        }

        function closeModal() {
            document.getElementById('task-detail-modal').style.display = 'none';
        }

        function showMessage(action) {
            alert(`${action}已触发！\n\n当前角色：${currentRole === 'teacher' ? '教师' : '学生'}\n用户：${currentUser}`);
        }

        // 点击模态框外部关闭
        document.getElementById('task-detail-modal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeModal();
            }
        });
    </script>
</body>
</html>
