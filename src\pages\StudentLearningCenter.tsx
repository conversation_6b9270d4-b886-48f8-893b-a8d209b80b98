import React, { useState } from 'react'
import { Card, Row, Col, Typo<PERSON>, Button, List, Avatar, Tag, Progress, Space, Statistic, Tabs, Calendar, Badge } from 'antd'
import { useNavigate } from 'react-router-dom'
import {
  BookOutlined,
  PlayCircleOutlined,
  EditOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  TrophyOutlined,
  CalendarOutlined,
  FileTextOutlined,
  StarOutlined,
  RightOutlined,
} from '@ant-design/icons'
import { useAuthStore } from '@/store/authStore'
import { ROUTES } from '@/utils/constants'

const { Title, Paragraph, Text } = Typography
const { TabPane } = Tabs

interface Course {
  id: string
  title: string
  instructor: string
  progress: number
  totalLessons: number
  completedLessons: number
  nextLesson?: string
  dueDate?: string
  status: 'not_started' | 'in_progress' | 'completed'
}

interface Assignment {
  id: string
  title: string
  courseName: string
  type: 'theory' | 'practice'
  dueDate: string
  status: 'pending' | 'submitted' | 'graded'
  score?: number
  maxScore: number
}

interface Exam {
  id: string
  title: string
  courseName: string
  startTime: string
  duration: number
  status: 'upcoming' | 'ongoing' | 'completed'
  score?: number
  maxScore: number
}

const StudentLearningCenter: React.FC = () => {
  const navigate = useNavigate()
  const { user } = useAuthStore()
  const [activeTab, setActiveTab] = useState('overview')

  // 模拟数据
  const [courses] = useState<Course[]>([
    {
      id: '1',
      title: '会展策划基础',
      instructor: '张教授',
      progress: 75,
      totalLessons: 12,
      completedLessons: 9,
      nextLesson: '第十课：展位设计实践',
      dueDate: '2024-01-25',
      status: 'in_progress'
    },
    {
      id: '2',
      title: '展示设计原理',
      instructor: '李教授',
      progress: 45,
      totalLessons: 10,
      completedLessons: 4,
      nextLesson: '第五课：色彩搭配原理',
      status: 'in_progress'
    },
    {
      id: '3',
      title: '3D建模技术',
      instructor: '王老师',
      progress: 0,
      totalLessons: 8,
      completedLessons: 0,
      status: 'not_started'
    }
  ])

  const [assignments] = useState<Assignment[]>([
    {
      id: '1',
      title: '展位设计方案',
      courseName: '会展策划基础',
      type: 'practice',
      dueDate: '2024-01-25',
      status: 'pending',
      maxScore: 100
    },
    {
      id: '2',
      title: '理论知识测试',
      courseName: '展示设计原理',
      type: 'theory',
      dueDate: '2024-01-22',
      status: 'graded',
      score: 85,
      maxScore: 100
    }
  ])

  const [exams] = useState<Exam[]>([
    {
      id: '1',
      title: '会展策划基础期中考试',
      courseName: '会展策划基础',
      startTime: '2024-01-28 09:00',
      duration: 90,
      status: 'upcoming',
      maxScore: 100
    }
  ])

  const handleCourseClick = (courseId: string) => {
    navigate(`/learning/courses/${courseId}`)
  }

  const handleAssignmentClick = (assignment: Assignment) => {
    if (assignment.type === 'practice') {
      // 跳转到3D编辑器
      navigate('/design-3d')
    } else {
      // 跳转到考试页面
      navigate('/learning/exam/' + assignment.id)
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'not_started': return 'default'
      case 'in_progress': return 'processing'
      case 'completed': return 'success'
      case 'pending': return 'warning'
      case 'submitted': return 'processing'
      case 'graded': return 'success'
      case 'upcoming': return 'default'
      case 'ongoing': return 'processing'
      default: return 'default'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'not_started': return '未开始'
      case 'in_progress': return '学习中'
      case 'completed': return '已完成'
      case 'pending': return '待提交'
      case 'submitted': return '已提交'
      case 'graded': return '已评分'
      case 'upcoming': return '即将开始'
      case 'ongoing': return '进行中'
      default: return status
    }
  }

  return (
    <div style={{ padding: '24px' }}>
      {/* 页面标题 */}
      <div style={{ marginBottom: 24 }}>
        <Title level={2}>学习中心</Title>
        <Paragraph>
          欢迎回来，{user?.username}！继续您的学习之旅
        </Paragraph>
      </div>

      {/* 学习统计 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="进行中课程"
              value={courses.filter(c => c.status === 'in_progress').length}
              prefix={<BookOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="待提交作业"
              value={assignments.filter(a => a.status === 'pending').length}
              prefix={<EditOutlined />}
              valueStyle={{ color: '#fa8c16' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="即将考试"
              value={exams.filter(e => e.status === 'upcoming').length}
              prefix={<ClockCircleOutlined />}
              valueStyle={{ color: '#f5222d' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="平均成绩"
              value={82.5}
              suffix="分"
              prefix={<TrophyOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 主要内容 */}
      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane tab="学习概览" key="overview">
          <Row gutter={24}>
            {/* 我的课程 */}
            <Col span={12}>
              <Card 
                title="我的课程" 
                extra={
                  <Button 
                    type="link" 
                    icon={<RightOutlined />}
                    onClick={() => navigate(ROUTES.LEARNING_COURSES)}
                  >
                    查看全部
                  </Button>
                }
              >
                <List
                  dataSource={courses.slice(0, 3)}
                  renderItem={course => (
                    <List.Item
                      style={{ cursor: 'pointer' }}
                      onClick={() => handleCourseClick(course.id)}
                    >
                      <List.Item.Meta
                        avatar={<Avatar icon={<BookOutlined />} />}
                        title={
                          <Space>
                            {course.title}
                            <Tag color={getStatusColor(course.status)}>
                              {getStatusText(course.status)}
                            </Tag>
                          </Space>
                        }
                        description={
                          <Space direction="vertical" style={{ width: '100%' }}>
                            <Text type="secondary">讲师：{course.instructor}</Text>
                            <div>
                              <Text style={{ fontSize: 12 }}>
                                进度：{course.completedLessons}/{course.totalLessons} 课时
                              </Text>
                              <Progress 
                                percent={course.progress} 
                                size="small" 
                                style={{ marginTop: 4 }}
                              />
                            </div>
                            {course.nextLesson && (
                              <Text type="secondary" style={{ fontSize: 12 }}>
                                下节课：{course.nextLesson}
                              </Text>
                            )}
                          </Space>
                        }
                      />
                    </List.Item>
                  )}
                />
              </Card>
            </Col>

            {/* 待办事项 */}
            <Col span={12}>
              <Card title="待办事项">
                <List
                  dataSource={[
                    ...assignments.filter(a => a.status === 'pending'),
                    ...exams.filter(e => e.status === 'upcoming')
                  ]}
                  renderItem={item => (
                    <List.Item
                      actions={[
                        'type' in item ? (
                          <Button 
                            type="primary" 
                            size="small"
                            onClick={() => handleAssignmentClick(item)}
                          >
                            {item.type === 'practice' ? '开始作业' : '开始答题'}
                          </Button>
                        ) : (
                          <Button type="primary" size="small">
                            进入考试
                          </Button>
                        )
                      ]}
                    >
                      <List.Item.Meta
                        avatar={
                          <Avatar 
                            icon={'type' in item ? <EditOutlined /> : <ClockCircleOutlined />}
                            style={{ 
                              backgroundColor: 'type' in item ? '#52c41a' : '#fa8c16' 
                            }}
                          />
                        }
                        title={item.title}
                        description={
                          <Space direction="vertical" size={0}>
                            <Text type="secondary">
                              {'type' in item ? item.courseName : item.courseName}
                            </Text>
                            <Text type="secondary" style={{ fontSize: 12 }}>
                              {'type' in item ? (
                                <>截止时间：{item.dueDate}</>
                              ) : (
                                <>考试时间：{item.startTime} ({item.duration}分钟)</>
                              )}
                            </Text>
                          </Space>
                        }
                      />
                    </List.Item>
                  )}
                />
              </Card>
            </Col>
          </Row>
        </TabPane>

        <TabPane tab="学习日历" key="calendar">
          <Card>
            <Calendar 
              dateCellRender={(value) => {
                // 这里可以添加日历事件渲染逻辑
                const dateStr = value.format('YYYY-MM-DD')
                if (dateStr === '2024-01-25') {
                  return (
                    <div>
                      <Badge status="warning" text="作业截止" />
                    </div>
                  )
                }
                if (dateStr === '2024-01-28') {
                  return (
                    <div>
                      <Badge status="error" text="期中考试" />
                    </div>
                  )
                }
                return null
              }}
            />
          </Card>
        </TabPane>

        <TabPane tab="学习记录" key="records">
          <Row gutter={16}>
            <Col span={12}>
              <Card title="最近完成的作业">
                <List
                  dataSource={assignments.filter(a => a.status === 'graded')}
                  renderItem={assignment => (
                    <List.Item>
                      <List.Item.Meta
                        avatar={<Avatar icon={<CheckCircleOutlined />} style={{ backgroundColor: '#52c41a' }} />}
                        title={assignment.title}
                        description={
                          <Space direction="vertical" size={0}>
                            <Text type="secondary">{assignment.courseName}</Text>
                            <Text strong style={{ color: '#52c41a' }}>
                              得分：{assignment.score}/{assignment.maxScore}
                            </Text>
                          </Space>
                        }
                      />
                    </List.Item>
                  )}
                />
              </Card>
            </Col>
            <Col span={12}>
              <Card title="学习成就">
                <div style={{ textAlign: 'center', padding: '40px 0' }}>
                  <StarOutlined style={{ fontSize: 48, color: '#faad14' }} />
                  <div style={{ marginTop: 16 }}>
                    <Title level={4}>学习达人</Title>
                    <Text type="secondary">连续学习7天</Text>
                  </div>
                </div>
              </Card>
            </Col>
          </Row>
        </TabPane>
      </Tabs>
    </div>
  )
}

export default StudentLearningCenter
