"""
客户需求预测模块
使用Prophet模型进行时间序列预测，帮助学生掌握需求预测的基本流程
"""

import streamlit as st
import pandas as pd
import numpy as np
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# 尝试导入Prophet，如果没有安装则提供安装提示
try:
    from prophet import Prophet
    PROPHET_AVAILABLE = True
except ImportError:
    PROPHET_AVAILABLE = False

# 页面配置
st.set_page_config(
    page_title="客户需求预测",
    page_icon="📈",
    layout="wide"
)

# 自定义CSS
st.markdown("""
<style>
    .metric-card {
        background-color: #f8f9fa;
        padding: 1rem;
        border-radius: 10px;
        border-left: 4px solid #1f77b4;
        margin: 0.5rem 0;
    }
    .warning-box {
        background-color: #fff3cd;
        padding: 1rem;
        border-radius: 5px;
        border: 1px solid #ffeaa7;
        margin: 1rem 0;
    }
</style>
""", unsafe_allow_html=True)

def generate_sample_data(data_type="default", periods=365):
    """生成示例销售数据"""
    np.random.seed(42)
    
    # 创建日期范围
    start_date = datetime(2022, 1, 1)
    dates = pd.date_range(start=start_date, periods=periods, freq='D')
    
    # 基础趋势
    trend = np.linspace(100, 150, periods)
    
    # 季节性模式
    yearly_seasonality = 20 * np.sin(2 * np.pi * np.arange(periods) / 365.25)
    weekly_seasonality = 10 * np.sin(2 * np.pi * np.arange(periods) / 7)
    
    if data_type == "default":
        # 默认销售数据
        base_sales = trend + yearly_seasonality + weekly_seasonality
        noise = np.random.normal(0, 5, periods)
        sales = base_sales + noise
        
    elif data_type == "seasonal":
        # 旺季销售数据（更强的季节性）
        base_sales = trend + 2 * yearly_seasonality + weekly_seasonality
        # 添加节假日效应
        holiday_effect = np.where(
            (dates.month == 12) | (dates.month == 1) | (dates.month == 2), 
            30, 0
        )
        noise = np.random.normal(0, 8, periods)
        sales = base_sales + holiday_effect + noise
        
    elif data_type == "growth":
        # 增长型销售数据
        growth_trend = np.linspace(100, 200, periods)
        base_sales = growth_trend + yearly_seasonality + weekly_seasonality
        noise = np.random.normal(0, 6, periods)
        sales = base_sales + noise
        
    else:  # stable
        # 稳定型销售数据
        stable_trend = np.full(periods, 120)
        base_sales = stable_trend + 0.5 * yearly_seasonality + weekly_seasonality
        noise = np.random.normal(0, 3, periods)
        sales = base_sales + noise
    
    # 确保销售数据为正数
    sales = np.maximum(sales, 10)
    
    return pd.DataFrame({
        'ds': dates,
        'y': sales
    })

def calculate_metrics(y_true, y_pred):
    """计算预测性能指标"""
    mae = np.mean(np.abs(y_true - y_pred))
    rmse = np.sqrt(np.mean((y_true - y_pred) ** 2))
    mape = np.mean(np.abs((y_true - y_pred) / y_true)) * 100
    return mae, rmse, mape

def create_forecast_plot(df, forecast, periods):
    """创建预测结果图表"""
    fig = go.Figure()
    
    # 历史数据
    fig.add_trace(go.Scatter(
        x=df['ds'],
        y=df['y'],
        mode='lines',
        name='历史销售数据',
        line=dict(color='#1f77b4', width=2)
    ))
    
    # 预测数据
    forecast_data = forecast.tail(periods)
    fig.add_trace(go.Scatter(
        x=forecast_data['ds'],
        y=forecast_data['yhat'],
        mode='lines',
        name='预测值',
        line=dict(color='#ff7f0e', width=2, dash='dash')
    ))
    
    # 置信区间
    fig.add_trace(go.Scatter(
        x=forecast_data['ds'],
        y=forecast_data['yhat_upper'],
        mode='lines',
        line=dict(width=0),
        showlegend=False,
        hoverinfo='skip'
    ))
    
    fig.add_trace(go.Scatter(
        x=forecast_data['ds'],
        y=forecast_data['yhat_lower'],
        mode='lines',
        line=dict(width=0),
        fill='tonexty',
        fillcolor='rgba(255, 127, 14, 0.2)',
        name='置信区间',
        hoverinfo='skip'
    ))
    
    fig.update_layout(
        title='销售数据预测结果',
        xaxis_title='日期',
        yaxis_title='销售量',
        hovermode='x unified',
        height=500,
        showlegend=True
    )
    
    return fig

def create_components_plot(forecast):
    """创建模型成分分解图"""
    fig = make_subplots(
        rows=3, cols=1,
        subplot_titles=['趋势 (Trend)', '年季节性 (Yearly)', '周季节性 (Weekly)'],
        vertical_spacing=0.08
    )
    
    # 趋势
    fig.add_trace(
        go.Scatter(x=forecast['ds'], y=forecast['trend'], 
                  name='趋势', line=dict(color='#1f77b4')),
        row=1, col=1
    )
    
    # 年季节性
    if 'yearly' in forecast.columns:
        fig.add_trace(
            go.Scatter(x=forecast['ds'], y=forecast['yearly'], 
                      name='年季节性', line=dict(color='#ff7f0e')),
            row=2, col=1
        )
    
    # 周季节性
    if 'weekly' in forecast.columns:
        fig.add_trace(
            go.Scatter(x=forecast['ds'], y=forecast['weekly'], 
                      name='周季节性', line=dict(color='#2ca02c')),
            row=3, col=1
        )
    
    fig.update_layout(
        height=600,
        showlegend=False,
        title_text="模型成分分解分析"
    )
    
    return fig

def main():
    st.title("📈 客户需求预测")
    st.markdown("通过时间序列分析技术，掌握需求预测的基本流程和核心概念")
    
    # 检查Prophet是否可用
    if not PROPHET_AVAILABLE:
        st.markdown("""
        <div class="warning-box">
            <h4>⚠️ 缺少依赖包</h4>
            <p>需要安装Prophet包才能使用此功能。请运行以下命令：</p>
            <code>pip install prophet</code>
            <p>安装完成后重新启动应用。</p>
        </div>
        """, unsafe_allow_html=True)
        return
    
    # 侧边栏控制
    st.sidebar.header("📊 数据选择与控制")
    
    # 数据集选择
    data_options = {
        "default": "默认销售数据",
        "seasonal": "旺季销售数据",
        "growth": "增长型销售数据",
        "stable": "稳定型销售数据"
    }
    
    selected_data = st.sidebar.selectbox(
        "选择数据集",
        options=list(data_options.keys()),
        format_func=lambda x: data_options[x],
        help="选择不同的数据集来模拟不同的业务场景"
    )
    
    # 预测周期设置
    forecast_periods = st.sidebar.number_input(
        "预测周期（天）",
        min_value=7,
        max_value=90,
        value=30,
        help="设置需要预测的未来天数"
    )
    
    # 历史数据长度
    history_days = st.sidebar.number_input(
        "历史数据长度（天）",
        min_value=180,
        max_value=730,
        value=365,
        help="用于训练模型的历史数据长度"
    )
    
    # 运行预测按钮
    if st.sidebar.button("🚀 运行预测", type="primary"):
        with st.spinner("正在进行预测分析..."):
            # 生成数据
            df = generate_sample_data(selected_data, history_days)
            
            # 分割训练和测试数据
            train_size = int(len(df) * 0.8)
            train_df = df[:train_size].copy()
            test_df = df[train_size:].copy()
            
            # 创建和训练Prophet模型
            model = Prophet(
                yearly_seasonality=True,
                weekly_seasonality=True,
                daily_seasonality=False,
                interval_width=0.95
            )
            model.fit(train_df)
            
            # 创建未来日期框架
            future = model.make_future_dataframe(periods=forecast_periods)
            forecast = model.predict(future)
            
            # 计算测试集性能
            test_forecast = forecast[train_size:len(df)]
            if len(test_forecast) > 0 and len(test_df) > 0:
                # 确保长度匹配
                min_len = min(len(test_df), len(test_forecast))
                mae, rmse, mape = calculate_metrics(
                    test_df['y'].values[:min_len],
                    test_forecast['yhat'].values[:min_len]
                )
            else:
                mae, rmse, mape = 0, 0, 0
            
            # 存储结果到session state
            st.session_state.forecast_results = {
                'df': df,
                'forecast': forecast,
                'train_size': train_size,
                'test_df': test_df,
                'mae': mae,
                'rmse': rmse,
                'mape': mape,
                'forecast_periods': forecast_periods,
                'data_type': data_options[selected_data]
            }
    
    # 显示结果
    if 'forecast_results' in st.session_state:
        results = st.session_state.forecast_results
        
        # 创建选项卡
        tab1, tab2, tab3 = st.tabs(["📊 预测图表", "🔍 模型成分分解", "📋 性能评估指标"])
        
        with tab1:
            st.subheader("预测结果可视化")
            
            # 显示数据集信息
            col1, col2, col3 = st.columns(3)
            with col1:
                st.metric("数据集类型", results['data_type'])
            with col2:
                st.metric("历史数据点", len(results['df']))
            with col3:
                st.metric("预测周期", f"{results['forecast_periods']} 天")
            
            # 预测图表
            fig = create_forecast_plot(results['df'], results['forecast'], results['forecast_periods'])
            st.plotly_chart(fig, use_container_width=True)
            
            # 预测数据表格
            st.subheader("预测数据详情")
            forecast_data = results['forecast'].tail(results['forecast_periods'])[['ds', 'yhat', 'yhat_lower', 'yhat_upper']]
            forecast_data.columns = ['日期', '预测值', '下限', '上限']
            forecast_data['日期'] = forecast_data['日期'].dt.strftime('%Y-%m-%d')
            st.dataframe(forecast_data, use_container_width=True)
        
        with tab2:
            st.subheader("模型成分分解")
            st.markdown("了解影响销售的关键因素：趋势、年季节性和周季节性")
            
            # 成分分解图
            fig_components = create_components_plot(results['forecast'])
            st.plotly_chart(fig_components, use_container_width=True)
            
            # 成分解释
            st.markdown("""
            **成分解释：**
            - **趋势 (Trend)**: 数据的长期发展方向，反映业务的整体增长或下降趋势
            - **年季节性 (Yearly)**: 一年中不同时期的周期性变化，如节假日效应
            - **周季节性 (Weekly)**: 一周中不同日期的周期性变化，如工作日vs周末
            """)
        
        with tab3:
            st.subheader("模型性能评估")
            st.markdown("基于测试集数据的模型性能指标")
            
            # 性能指标卡片
            col1, col2, col3 = st.columns(3)
            
            with col1:
                st.markdown(f"""
                <div class="metric-card">
                    <h4>平均绝对误差 (MAE)</h4>
                    <h2>{results['mae']:.2f}</h2>
                    <p>预测值与实际值的平均绝对差异</p>
                </div>
                """, unsafe_allow_html=True)
            
            with col2:
                st.markdown(f"""
                <div class="metric-card">
                    <h4>均方根误差 (RMSE)</h4>
                    <h2>{results['rmse']:.2f}</h2>
                    <p>预测误差的标准差，对大误差更敏感</p>
                </div>
                """, unsafe_allow_html=True)
            
            with col3:
                st.markdown(f"""
                <div class="metric-card">
                    <h4>平均绝对百分比误差 (MAPE)</h4>
                    <h2>{results['mape']:.1f}%</h2>
                    <p>预测误差的相对百分比</p>
                </div>
                """, unsafe_allow_html=True)
            
            # 性能解释
            st.markdown("""
            **指标解释：**
            - **MAE**: 值越小表示预测越准确，单位与原数据相同
            - **RMSE**: 值越小表示预测越准确，对异常值更敏感
            - **MAPE**: 百分比形式，便于不同规模数据的比较
            
            **评估标准：**
            - MAPE < 10%: 预测精度很高
            - 10% ≤ MAPE < 20%: 预测精度较好  
            - 20% ≤ MAPE < 50%: 预测精度一般
            - MAPE ≥ 50%: 预测精度较低
            """)
    
    else:
        # 初始状态显示
        st.info("👈 请在左侧设置参数并点击'运行预测'开始分析")
        
        # 功能介绍
        st.markdown("""
        ### 📚 学习目标
        
        通过本模块，您将学会：
        
        1. **时间序列预测基础**: 理解时间序列数据的特点和预测方法
        2. **Prophet模型应用**: 掌握Facebook Prophet模型的使用
        3. **季节性分析**: 识别和分析数据中的季节性模式
        4. **模型评估**: 学会使用MAE、RMSE等指标评估模型性能
        5. **业务应用**: 将预测结果应用到实际业务决策中
        
        ### 🎯 操作步骤
        
        1. 在左侧选择一个数据集类型
        2. 设置预测周期和历史数据长度
        3. 点击"运行预测"按钮
        4. 查看预测图表和分析结果
        5. 探索模型成分分解
        6. 评估模型性能指标
        """)

if __name__ == "__main__":
    main()
