"""
客户需求预测模块
使用Prophet模型进行时间序列预测，帮助学生掌握需求预测的基本流程
"""

import streamlit as st
import pandas as pd
import numpy as np
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# 尝试导入Prophet，如果没有安装则提供安装提示
try:
    from prophet import Prophet
    PROPHET_AVAILABLE = True
except ImportError:
    PROPHET_AVAILABLE = False

# 页面配置
st.set_page_config(
    page_title="客户需求预测",
    page_icon="📈",
    layout="wide"
)

# 自定义CSS
st.markdown("""
<style>
    .metric-card {
        background-color: #f8f9fa;
        padding: 1rem;
        border-radius: 10px;
        border-left: 4px solid #1f77b4;
        margin: 0.5rem 0;
    }
    .warning-box {
        background-color: #fff3cd;
        padding: 1rem;
        border-radius: 5px;
        border: 1px solid #ffeaa7;
        margin: 1rem 0;
    }
</style>
""", unsafe_allow_html=True)

def generate_sample_data(data_type="default", periods=365, country="中国", category="连衣裙"):
    """生成示例销售数据"""
    np.random.seed(42)

    # 创建日期范围
    start_date = datetime(2022, 1, 1)
    dates = pd.date_range(start=start_date, periods=periods, freq='D')

    # 根据国家调整基础趋势
    country_multipliers = {
        "中国": 1.0,
        "美国": 1.2,
        "日本": 0.8,
        "德国": 0.9,
        "法国": 1.1
    }

    # 根据女装品类调整基础值
    category_multipliers = {
        "连衣裙": 1.0,
        "上衣": 0.8,
        "裤装": 0.7,
        "外套": 1.3,
        "配饰": 0.6
    }

    base_value = 100 * country_multipliers.get(country, 1.0) * category_multipliers.get(category, 1.0)
    trend = np.linspace(base_value, base_value * 1.5, periods)

    # 季节性模式（根据品类调整）
    if category in ["外套"]:
        # 外套有明显的冬季销售高峰
        yearly_seasonality = 30 * np.cos(2 * np.pi * np.arange(periods) / 365.25)
    elif category in ["连衣裙"]:
        # 连衣裙春夏销售较好
        yearly_seasonality = 20 * np.sin(2 * np.pi * np.arange(periods) / 365.25)
    else:
        # 其他品类相对平稳
        yearly_seasonality = 15 * np.sin(2 * np.pi * np.arange(periods) / 365.25)

    weekly_seasonality = 10 * np.sin(2 * np.pi * np.arange(periods) / 7)
    
    if data_type == "default":
        # 默认销售数据
        base_sales = trend + yearly_seasonality + weekly_seasonality
        noise = np.random.normal(0, 5, periods)
        sales = base_sales + noise

    elif data_type == "seasonal":
        # 旺季销售数据（更强的季节性）
        base_sales = trend + 2 * yearly_seasonality + weekly_seasonality
        # 添加节假日效应（根据国家调整）
        if country in ["中国"]:
            holiday_effect = np.where(
                (dates.month == 2) | (dates.month == 10) | (dates.month == 11),
                40, 0  # 春节、国庆、双十一
            )
        elif country in ["美国"]:
            holiday_effect = np.where(
                (dates.month == 11) | (dates.month == 12),
                35, 0  # 感恩节、圣诞节
            )
        else:
            holiday_effect = np.where(
                (dates.month == 12) | (dates.month == 1),
                30, 0  # 通用节假日
            )
        noise = np.random.normal(0, 8, periods)
        sales = base_sales + holiday_effect + noise

    elif data_type == "growth":
        # 增长型销售数据
        growth_trend = np.linspace(base_value, base_value * 2, periods)
        base_sales = growth_trend + yearly_seasonality + weekly_seasonality
        noise = np.random.normal(0, 6, periods)
        sales = base_sales + noise

    else:  # stable
        # 稳定型销售数据
        stable_trend = np.full(periods, base_value * 1.2)
        base_sales = stable_trend + 0.5 * yearly_seasonality + weekly_seasonality
        noise = np.random.normal(0, 3, periods)
        sales = base_sales + noise
    
    # 确保销售数据为正数
    sales = np.maximum(sales, 10)
    
    return pd.DataFrame({
        'ds': dates,
        'y': sales
    })

def calculate_metrics(y_true, y_pred):
    """计算预测性能指标"""
    mae = np.mean(np.abs(y_true - y_pred))
    rmse = np.sqrt(np.mean((y_true - y_pred) ** 2))
    mape = np.mean(np.abs((y_true - y_pred) / y_true)) * 100
    return mae, rmse, mape

def create_forecast_plot(df, forecast, periods):
    """创建预测结果图表"""
    fig = go.Figure()
    
    # 历史数据
    fig.add_trace(go.Scatter(
        x=df['ds'],
        y=df['y'],
        mode='lines',
        name='历史销售数据',
        line=dict(color='#1f77b4', width=2)
    ))
    
    # 预测数据
    forecast_data = forecast.tail(periods)
    fig.add_trace(go.Scatter(
        x=forecast_data['ds'],
        y=forecast_data['yhat'],
        mode='lines',
        name='预测值',
        line=dict(color='#ff7f0e', width=2, dash='dash')
    ))
    
    # 置信区间
    fig.add_trace(go.Scatter(
        x=forecast_data['ds'],
        y=forecast_data['yhat_upper'],
        mode='lines',
        line=dict(width=0),
        showlegend=False,
        hoverinfo='skip'
    ))
    
    fig.add_trace(go.Scatter(
        x=forecast_data['ds'],
        y=forecast_data['yhat_lower'],
        mode='lines',
        line=dict(width=0),
        fill='tonexty',
        fillcolor='rgba(255, 127, 14, 0.2)',
        name='置信区间',
        hoverinfo='skip'
    ))
    
    fig.update_layout(
        title='销售数据预测结果',
        xaxis_title='日期',
        yaxis_title='销售量',
        hovermode='x unified',
        height=500,
        showlegend=True
    )
    
    return fig

def create_components_plot(forecast):
    """创建模型成分分解图"""
    fig = make_subplots(
        rows=3, cols=1,
        subplot_titles=['趋势 (Trend)', '年季节性 (Yearly)', '周季节性 (Weekly)'],
        vertical_spacing=0.08
    )
    
    # 趋势
    fig.add_trace(
        go.Scatter(x=forecast['ds'], y=forecast['trend'], 
                  name='趋势', line=dict(color='#1f77b4')),
        row=1, col=1
    )
    
    # 年季节性
    if 'yearly' in forecast.columns:
        fig.add_trace(
            go.Scatter(x=forecast['ds'], y=forecast['yearly'], 
                      name='年季节性', line=dict(color='#ff7f0e')),
            row=2, col=1
        )
    
    # 周季节性
    if 'weekly' in forecast.columns:
        fig.add_trace(
            go.Scatter(x=forecast['ds'], y=forecast['weekly'], 
                      name='周季节性', line=dict(color='#2ca02c')),
            row=3, col=1
        )
    
    fig.update_layout(
        height=600,
        showlegend=False,
        title_text="模型成分分解分析"
    )
    
    return fig

def create_workflow_diagram():
    """创建工作流程图"""
    fig = go.Figure()

    # 定义工作流节点
    workflow_steps = [
        {"name": "数据准备", "x": 1, "y": 2, "color": "#17a2b8"},
        {"name": "参数设置", "x": 2, "y": 2, "color": "#ffc107"},
        {"name": "模型训练", "x": 3, "y": 2, "color": "#28a745"},
        {"name": "预测分析", "x": 4, "y": 2, "color": "#dc3545"},
        {"name": "结果展示", "x": 5, "y": 2, "color": "#6f42c1"}
    ]

    # 添加节点
    for step in workflow_steps:
        fig.add_trace(go.Scatter(
            x=[step["x"]], y=[step["y"]],
            mode='markers+text',
            marker=dict(size=80, color=step["color"]),
            text=step["name"],
            textposition="middle center",
            textfont=dict(color="white", size=12, family="Arial Black"),
            showlegend=False,
            name=step["name"]
        ))

    # 添加连接线
    for i in range(len(workflow_steps) - 1):
        current = workflow_steps[i]
        next_step = workflow_steps[i + 1]

        fig.add_trace(go.Scatter(
            x=[current["x"], next_step["x"]],
            y=[current["y"], next_step["y"]],
            mode='lines',
            line=dict(color='#6c757d', width=4),
            showlegend=False
        ))

        # 添加箭头
        fig.add_annotation(
            x=next_step["x"], y=next_step["y"],
            ax=current["x"], ay=current["y"],
            xref='x', yref='y',
            axref='x', ayref='y',
            arrowhead=3,
            arrowsize=1.5,
            arrowwidth=3,
            arrowcolor='#6c757d'
        )

    fig.update_layout(
        title="需求预测工作流程",
        xaxis=dict(showgrid=False, zeroline=False, showticklabels=False, range=[0.5, 5.5]),
        yaxis=dict(showgrid=False, zeroline=False, showticklabels=False, range=[1.5, 2.5]),
        height=200,
        showlegend=False,
        plot_bgcolor='rgba(0,0,0,0)'
    )

    return fig

def main():
    st.title("📈 客户需求预测")
    st.markdown("通过时间序列分析技术，掌握需求预测的基本流程和核心概念")

    # 检查Prophet是否可用
    if not PROPHET_AVAILABLE:
        st.markdown("""
        <div class="warning-box">
            <h4>⚠️ 缺少依赖包</h4>
            <p>需要安装Prophet包才能使用此功能。请运行以下命令：</p>
            <code>pip install prophet</code>
            <p>安装完成后重新启动应用。</p>
        </div>
        """, unsafe_allow_html=True)
        return

    # 显示工作流程图
    st.subheader("🔄 预测工作流程")
    workflow_fig = create_workflow_diagram()
    st.plotly_chart(workflow_fig, use_container_width=True)

    # 初始化session state
    if 'workflow_step' not in st.session_state:
        st.session_state.workflow_step = 1
    if 'workflow_data' not in st.session_state:
        st.session_state.workflow_data = {}

    # 工作流步骤
    current_step = st.session_state.workflow_step

    # 步骤1：数据准备
    if current_step == 1:
        st.markdown("### 📊 步骤1：数据准备")
        st.markdown("选择数据源和基本参数")

        col1, col2 = st.columns(2)

        with col1:
            # 国家选择
            countries = ["中国", "美国", "日本", "德国", "法国"]
            selected_country = st.selectbox(
                "选择国家/地区",
                options=countries,
                help="不同国家的市场特征和消费习惯会影响销售模式"
            )

            # 女装品类选择
            categories = ["连衣裙", "上衣", "裤装", "外套", "配饰"]
            selected_category = st.selectbox(
                "选择女装品类",
                options=categories,
                help="不同品类的季节性特征和销售模式存在差异"
            )

        with col2:
            # 数据集选择
            data_options = {
                "default": "默认销售数据",
                "seasonal": "旺季销售数据",
                "growth": "增长型销售数据",
                "stable": "稳定型销售数据"
            }

            selected_data = st.selectbox(
                "选择数据模式",
                options=list(data_options.keys()),
                format_func=lambda x: data_options[x],
                help="选择不同的数据模式来模拟不同的业务场景"
            )

            # 历史数据长度
            history_days = st.number_input(
                "历史数据长度（天）",
                min_value=180,
                max_value=730,
                value=365,
                help="用于训练模型的历史数据长度"
            )

        # 数据预览
        st.markdown("#### 📋 数据预览")
        preview_data = generate_sample_data(selected_data, min(100, history_days), selected_country, selected_category)

        col1, col2 = st.columns(2)
        with col1:
            st.dataframe(preview_data.head(10), use_container_width=True)

        with col2:
            # 简单的数据统计
            st.markdown("**数据统计**")
            st.write(f"数据点数量: {len(preview_data)}")
            st.write(f"平均销量: {preview_data['y'].mean():.2f}")
            st.write(f"最大销量: {preview_data['y'].max():.2f}")
            st.write(f"最小销量: {preview_data['y'].min():.2f}")

        if st.button("➡️ 下一步：参数设置", type="primary"):
            st.session_state.workflow_data.update({
                'country': selected_country,
                'category': selected_category,
                'data_type': selected_data,
                'history_days': history_days
            })
            st.session_state.workflow_step = 2
            st.rerun()

    # 步骤2：参数设置
    elif current_step == 2:
        st.markdown("### ⚙️ 步骤2：参数设置")
        st.markdown("配置预测模型的关键参数")

        col1, col2 = st.columns(2)

        with col1:
            # 预测周期设置
            forecast_periods = st.number_input(
                "预测周期（天）",
                min_value=7,
                max_value=90,
                value=30,
                help="设置需要预测的未来天数"
            )

            # 模型参数
            yearly_seasonality = st.checkbox("启用年季节性", value=True)
            weekly_seasonality = st.checkbox("启用周季节性", value=True)

        with col2:
            # 置信区间
            confidence_interval = st.slider(
                "置信区间",
                min_value=0.8,
                max_value=0.99,
                value=0.95,
                step=0.01,
                help="预测结果的置信区间"
            )

            # 训练比例
            train_ratio = st.slider(
                "训练数据比例",
                min_value=0.7,
                max_value=0.9,
                value=0.8,
                step=0.05,
                help="用于训练的数据比例"
            )

        # 参数总结
        st.markdown("#### 📋 参数总结")
        param_summary = f"""
        - **数据源**: {st.session_state.workflow_data.get('country', '')} - {st.session_state.workflow_data.get('category', '')}
        - **数据模式**: {st.session_state.workflow_data.get('data_type', '')}
        - **历史数据**: {st.session_state.workflow_data.get('history_days', '')} 天
        - **预测周期**: {forecast_periods} 天
        - **置信区间**: {confidence_interval*100:.0f}%
        - **训练比例**: {train_ratio*100:.0f}%
        """
        st.markdown(param_summary)

        col1, col2 = st.columns(2)
        with col1:
            if st.button("⬅️ 上一步：数据准备"):
                st.session_state.workflow_step = 1
                st.rerun()

        with col2:
            if st.button("➡️ 下一步：模型训练", type="primary"):
                st.session_state.workflow_data.update({
                    'forecast_periods': forecast_periods,
                    'yearly_seasonality': yearly_seasonality,
                    'weekly_seasonality': weekly_seasonality,
                    'confidence_interval': confidence_interval,
                    'train_ratio': train_ratio
                })
                st.session_state.workflow_step = 3
                st.rerun()

    # 步骤3：模型训练
    elif current_step == 3:
        st.markdown("### 🤖 步骤3：模型训练")
        st.markdown("训练Prophet预测模型")

        if st.button("🚀 开始训练模型", type="primary"):
            with st.spinner("正在进行预测分析..."):
                # 获取工作流数据
                workflow_data = st.session_state.workflow_data

                # 生成数据
                df = generate_sample_data(
                    workflow_data['data_type'],
                    workflow_data['history_days'],
                    workflow_data['country'],
                    workflow_data['category']
                )

                # 分割训练和测试数据
                train_size = int(len(df) * workflow_data['train_ratio'])
                train_df = df[:train_size].copy()
                test_df = df[train_size:].copy()

                # 创建和训练Prophet模型
                model = Prophet(
                    yearly_seasonality=workflow_data['yearly_seasonality'],
                    weekly_seasonality=workflow_data['weekly_seasonality'],
                    daily_seasonality=False,
                    interval_width=workflow_data['confidence_interval']
                )
                model.fit(train_df)

                # 创建未来日期框架
                future = model.make_future_dataframe(periods=workflow_data['forecast_periods'])
                forecast = model.predict(future)

                # 计算测试集性能
                test_forecast = forecast[train_size:len(df)]
                if len(test_forecast) > 0 and len(test_df) > 0:
                    # 确保长度匹配
                    min_len = min(len(test_df), len(test_forecast))
                    mae, rmse, mape = calculate_metrics(
                        test_df['y'].values[:min_len],
                        test_forecast['yhat'].values[:min_len]
                    )
                else:
                    mae, rmse, mape = 0, 0, 0

                # 存储结果到session state
                st.session_state.forecast_results = {
                    'df': df,
                    'forecast': forecast,
                    'train_size': train_size,
                    'test_df': test_df,
                    'mae': mae,
                    'rmse': rmse,
                    'mape': mape,
                    'model': model
                }
                st.session_state.workflow_data.update({
                    'mae': mae,
                    'rmse': rmse,
                    'mape': mape
                })

                st.success("模型训练完成！")
                st.session_state.workflow_step = 4
                st.rerun()

        # 显示训练进度（如果有结果）
        if 'forecast_results' in st.session_state:
            st.success("✅ 模型训练已完成")

            # 显示训练统计
            col1, col2, col3, col4 = st.columns(4)
            with col1:
                st.metric("训练样本", st.session_state.forecast_results['train_size'])
            with col2:
                st.metric("测试样本", len(st.session_state.forecast_results['test_df']))
            with col3:
                st.metric("MAE", f"{st.session_state.forecast_results['mae']:.2f}")
            with col4:
                st.metric("RMSE", f"{st.session_state.forecast_results['rmse']:.2f}")

        col1, col2 = st.columns(2)
        with col1:
            if st.button("⬅️ 上一步：参数设置"):
                st.session_state.workflow_step = 2
                st.rerun()

        with col2:
            if 'forecast_results' in st.session_state:
                if st.button("➡️ 下一步：预测分析", type="primary"):
                    st.session_state.workflow_step = 4
                    st.rerun()

    # 步骤4：预测分析
    elif current_step == 4:
        st.markdown("### 📊 步骤4：预测分析")
        st.markdown("分析预测结果和模型性能")

        if 'forecast_results' not in st.session_state:
            st.warning("请先完成模型训练")
            if st.button("⬅️ 返回模型训练"):
                st.session_state.workflow_step = 3
                st.rerun()
            return

        results = st.session_state.forecast_results
        workflow_data = st.session_state.workflow_data

        # 预测结果概览
        col1, col2, col3, col4, col5 = st.columns(5)
        with col1:
            st.metric("国家/地区", workflow_data['country'])
        with col2:
            st.metric("女装品类", workflow_data['category'])
        with col3:
            st.metric("预测周期", f"{workflow_data['forecast_periods']} 天")
        with col4:
            st.metric("MAE", f"{results['mae']:.2f}")
        with col5:
            st.metric("MAPE", f"{results['mape']:.1f}%")

        # 预测图表
        fig = create_forecast_plot(results['df'], results['forecast'], workflow_data['forecast_periods'])
        st.plotly_chart(fig, use_container_width=True)

        col1, col2 = st.columns(2)
        with col1:
            if st.button("⬅️ 上一步：模型训练"):
                st.session_state.workflow_step = 3
                st.rerun()

        with col2:
            if st.button("➡️ 下一步：结果展示", type="primary"):
                st.session_state.workflow_step = 5
                st.rerun()

    # 步骤5：结果展示
    elif current_step == 5:
        st.markdown("### 📈 步骤5：结果展示")
        st.markdown("查看完整的预测结果和分析报告")

        if 'forecast_results' not in st.session_state:
            st.warning("请先完成预测分析")
            if st.button("⬅️ 返回预测分析"):
                st.session_state.workflow_step = 4
                st.rerun()
            return

        results = st.session_state.forecast_results
        workflow_data = st.session_state.workflow_data

        # 创建选项卡
        tab1, tab2, tab3, tab4 = st.tabs(["📊 预测图表", "🔍 模型成分分解", "📋 性能评估指标", "🎯 参数影响分析"])
        
        with tab1:
            st.subheader("预测结果可视化")

            # 显示数据集信息
            col1, col2, col3, col4, col5 = st.columns(5)
            with col1:
                st.metric("国家/地区", workflow_data['country'])
            with col2:
                st.metric("女装品类", workflow_data['category'])
            with col3:
                st.metric("数据模式", workflow_data['data_type'])
            with col4:
                st.metric("历史数据点", len(results['df']))
            with col5:
                st.metric("预测周期", f"{workflow_data['forecast_periods']} 天")

            # 预测图表
            fig = create_forecast_plot(results['df'], results['forecast'], workflow_data['forecast_periods'])
            st.plotly_chart(fig, use_container_width=True)

            # 预测数据表格
            st.subheader("预测数据详情")
            forecast_data = results['forecast'].tail(workflow_data['forecast_periods'])[['ds', 'yhat', 'yhat_lower', 'yhat_upper']]
            forecast_data.columns = ['日期', '预测值', '下限', '上限']
            forecast_data['日期'] = forecast_data['日期'].dt.strftime('%Y-%m-%d')
            st.dataframe(forecast_data, use_container_width=True)
        
        with tab2:
            st.subheader("模型成分分解")
            st.markdown("了解影响销售的关键因素：趋势、年季节性和周季节性")
            
            # 成分分解图
            fig_components = create_components_plot(results['forecast'])
            st.plotly_chart(fig_components, use_container_width=True)
            
            # 成分解释
            st.markdown("""
            **成分解释：**
            - **趋势 (Trend)**: 数据的长期发展方向，反映业务的整体增长或下降趋势
            - **年季节性 (Yearly)**: 一年中不同时期的周期性变化，如节假日效应
            - **周季节性 (Weekly)**: 一周中不同日期的周期性变化，如工作日vs周末
            """)
        
        with tab3:
            st.subheader("模型性能评估")
            st.markdown("基于测试集数据的模型性能指标")
            
            # 性能指标卡片
            col1, col2, col3 = st.columns(3)
            
            with col1:
                st.markdown(f"""
                <div class="metric-card">
                    <h4>平均绝对误差 (MAE)</h4>
                    <h2>{results['mae']:.2f}</h2>
                    <p>预测值与实际值的平均绝对差异</p>
                </div>
                """, unsafe_allow_html=True)
            
            with col2:
                st.markdown(f"""
                <div class="metric-card">
                    <h4>均方根误差 (RMSE)</h4>
                    <h2>{results['rmse']:.2f}</h2>
                    <p>预测误差的标准差，对大误差更敏感</p>
                </div>
                """, unsafe_allow_html=True)
            
            with col3:
                st.markdown(f"""
                <div class="metric-card">
                    <h4>平均绝对百分比误差 (MAPE)</h4>
                    <h2>{results['mape']:.1f}%</h2>
                    <p>预测误差的相对百分比</p>
                </div>
                """, unsafe_allow_html=True)
            
            # 性能解释
            st.markdown("""
            **指标解释：**
            - **MAE**: 值越小表示预测越准确，单位与原数据相同
            - **RMSE**: 值越小表示预测越准确，对异常值更敏感
            - **MAPE**: 百分比形式，便于不同规模数据的比较
            
            **评估标准：**
            - MAPE < 10%: 预测精度很高
            - 10% ≤ MAPE < 20%: 预测精度较好
            - 20% ≤ MAPE < 50%: 预测精度一般
            - MAPE ≥ 50%: 预测精度较低
            """)

        with tab4:
            st.subheader("参数影响分析")
            st.markdown("了解不同参数对预测结果的影响，帮助理解模型原理")

            # 国家影响分析
            st.markdown("### 🌍 国家/地区影响")
            country_effects = {
                "中国": {"multiplier": 1.0, "holidays": "春节、国庆、双十一", "characteristics": "电商节日效应明显"},
                "美国": {"multiplier": 1.2, "holidays": "感恩节、圣诞节", "characteristics": "节假日消费集中"},
                "日本": {"multiplier": 0.8, "holidays": "新年、黄金周", "characteristics": "消费相对保守"},
                "德国": {"multiplier": 0.9, "holidays": "圣诞节", "characteristics": "消费理性稳定"},
                "法国": {"multiplier": 1.1, "holidays": "圣诞节、夏季假期", "characteristics": "时尚消费活跃"}
            }

            current_country = workflow_data['country']
            country_info = country_effects[current_country]

            col1, col2 = st.columns(2)
            with col1:
                st.markdown(f"""
                **当前选择：{current_country}**
                - 市场规模系数：{country_info['multiplier']}
                - 主要节假日：{country_info['holidays']}
                - 市场特征：{country_info['characteristics']}
                """)

            with col2:
                # 创建国家对比图
                countries_list = list(country_effects.keys())
                multipliers = [country_effects[c]['multiplier'] for c in countries_list]

                fig_country = go.Figure(data=[
                    go.Bar(x=countries_list, y=multipliers,
                          marker_color=['red' if c == current_country else 'lightblue' for c in countries_list])
                ])
                fig_country.update_layout(
                    title="各国市场规模系数对比",
                    xaxis_title="国家/地区",
                    yaxis_title="市场规模系数",
                    height=300
                )
                st.plotly_chart(fig_country, use_container_width=True)

            # 品类影响分析
            st.markdown("### 👗 女装品类影响")
            category_effects = {
                "连衣裙": {"multiplier": 1.0, "seasonality": "春夏销售高峰", "pattern": "正弦波模式"},
                "上衣": {"multiplier": 0.8, "seasonality": "四季相对均衡", "pattern": "平稳模式"},
                "裤装": {"multiplier": 0.7, "seasonality": "秋冬略好", "pattern": "轻微季节性"},
                "外套": {"multiplier": 1.3, "seasonality": "冬季销售高峰", "pattern": "余弦波模式"},
                "配饰": {"multiplier": 0.6, "seasonality": "节假日促销", "pattern": "节点性波动"}
            }

            current_category = workflow_data['category']
            category_info = category_effects[current_category]

            col1, col2 = st.columns(2)
            with col1:
                st.markdown(f"""
                **当前选择：{current_category}**
                - 销售规模系数：{category_info['multiplier']}
                - 季节性特征：{category_info['seasonality']}
                - 波动模式：{category_info['pattern']}
                """)

            with col2:
                # 创建品类对比图
                categories_list = list(category_effects.keys())
                cat_multipliers = [category_effects[c]['multiplier'] for c in categories_list]

                fig_category = go.Figure(data=[
                    go.Bar(x=categories_list, y=cat_multipliers,
                          marker_color=['red' if c == current_category else 'lightgreen' for c in categories_list])
                ])
                fig_category.update_layout(
                    title="各品类销售规模系数对比",
                    xaxis_title="女装品类",
                    yaxis_title="销售规模系数",
                    height=300
                )
                st.plotly_chart(fig_category, use_container_width=True)

            # 模型原理说明
            st.markdown("### 🔬 模型原理说明")
            st.markdown("""
            **数据生成原理：**
            1. **基础趋势**：根据国家和品类系数调整基础销售水平
            2. **季节性模式**：不同品类采用不同的季节性函数
            3. **节假日效应**：根据国家特色添加节假日销售提升
            4. **随机噪声**：模拟真实市场的不确定性

            **Prophet模型特点：**
            - 自动识别趋势变化点
            - 处理多种季节性模式
            - 考虑节假日效应
            - 提供不确定性估计

            **实际应用价值：**
            - 帮助理解不同市场的特征差异
            - 学习如何根据业务场景调整模型
            - 培养对数据模式的敏感性
            """)

        # 工作流导航
        st.markdown("---")
        col1, col2, col3 = st.columns(3)

        with col1:
            if st.button("⬅️ 上一步：预测分析"):
                st.session_state.workflow_step = 4
                st.rerun()

        with col2:
            if st.button("🔄 重新开始工作流", type="secondary"):
                # 清除所有数据
                if 'workflow_data' in st.session_state:
                    del st.session_state.workflow_data
                if 'forecast_results' in st.session_state:
                    del st.session_state.forecast_results
                st.session_state.workflow_step = 1
                st.rerun()

        with col3:
            if st.button("📊 导出结果"):
                # 这里可以添加导出功能
                st.success("结果导出功能开发中...")

    # 工作流状态显示
    st.sidebar.markdown("### 🔄 工作流状态")

    steps = [
        "📊 数据准备",
        "⚙️ 参数设置",
        "🤖 模型训练",
        "📈 预测分析",
        "📋 结果展示"
    ]

    for i, step in enumerate(steps, 1):
        if i < current_step:
            st.sidebar.markdown(f"✅ {step}")
        elif i == current_step:
            st.sidebar.markdown(f"🔄 **{step}**")
        else:
            st.sidebar.markdown(f"⏳ {step}")

    # 快速导航
    st.sidebar.markdown("### 🧭 快速导航")
    if st.sidebar.button("跳转到数据准备"):
        st.session_state.workflow_step = 1
        st.rerun()
    if st.sidebar.button("跳转到参数设置"):
        st.session_state.workflow_step = 2
        st.rerun()
    if st.sidebar.button("跳转到模型训练"):
        st.session_state.workflow_step = 3
        st.rerun()
    if 'forecast_results' in st.session_state:
        if st.sidebar.button("跳转到预测分析"):
            st.session_state.workflow_step = 4
            st.rerun()
        if st.sidebar.button("跳转到结果展示"):
            st.session_state.workflow_step = 5
            st.rerun()

    
    else:
        # 初始状态显示
        st.info("👈 请在左侧设置参数并点击'运行预测'开始分析")
        
        # 功能介绍
        st.markdown("""
        ### 📚 学习目标
        
        通过本模块，您将学会：
        
        1. **时间序列预测基础**: 理解时间序列数据的特点和预测方法
        2. **Prophet模型应用**: 掌握Facebook Prophet模型的使用
        3. **季节性分析**: 识别和分析数据中的季节性模式
        4. **模型评估**: 学会使用MAE、RMSE等指标评估模型性能
        5. **业务应用**: 将预测结果应用到实际业务决策中
        
        ### 🎯 操作步骤
        
        1. 在左侧选择一个数据集类型
        2. 设置预测周期和历史数据长度
        3. 点击"运行预测"按钮
        4. 查看预测图表和分析结果
        5. 探索模型成分分解
        6. 评估模型性能指标
        """)

if __name__ == "__main__":
    main()
