@echo off
echo ========================================
echo 智能教学代理系统启动脚本
echo ========================================
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python，请先安装Python 3.8+
    pause
    exit /b 1
)

echo 检测到Python版本:
python --version

REM 检查是否存在虚拟环境
if exist "venv\Scripts\activate.bat" (
    echo.
    echo 激活虚拟环境...
    call venv\Scripts\activate.bat
) else (
    echo.
    echo 未找到虚拟环境，使用系统Python环境
    echo 建议创建虚拟环境: python -m venv venv
)

REM 检查依赖包
echo.
echo 检查依赖包...
pip show streamlit >nul 2>&1
if errorlevel 1 (
    echo 正在安装依赖包...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo 错误: 依赖包安装失败
        pause
        exit /b 1
    )
) else (
    echo 依赖包已安装
)

REM 启动应用
echo.
echo 启动智能教学代理系统...
echo 应用将在浏览器中自动打开: http://localhost:8501
echo 按 Ctrl+C 停止应用
echo.

streamlit run app.py

echo.
echo 应用已停止
pause
