# MCTT - 会展行业虚实融合实训教学平台

## 项目简介

会展行业虚实融合实训教学平台是一个集成AI技术的综合性教学平台，为学生、教师和企业提供从会展方案设计到施工管理的一站式解决方案。

## 核心功能

- 🤖 **AI+会展方案生成** - 智能生成布局设计、展位分配、流线规划
- 🎨 **AI+2D效果图生成** - 多角度、多场景的效果图渲染
- 🏗️ **3D模型生成** - 虚拟漫游和交互体验
- 📊 **施工管理系统** - 进度跟踪、资源管理、质量控制
- 👨‍🏫 **教学监控系统** - 学生进度监控、项目管理、评估打分
- 🔄 **项目流程管理** - 完整的会展项目生命周期管理

## 技术栈

### 前端
- **React 18** - 用户界面框架
- **TypeScript** - 类型安全的JavaScript
- **Vite** - 现代化构建工具
- **Ant Design** - 企业级UI组件库
- **React Router** - 路由管理
- **Zustand** - 状态管理
- **Three.js** - 3D图形渲染
- **Fabric.js** - 2D图形编辑

### 后端（计划）
- **Node.js + Express** - 服务端框架
- **Python FastAPI** - AI服务
- **PostgreSQL** - 主数据库
- **Redis** - 缓存系统
- **MongoDB** - 文件存储

## 项目结构

```
MCTT/
├── src/
│   ├── components/          # 可复用组件
│   │   └── layout/         # 布局组件
│   ├── pages/              # 页面组件
│   ├── hooks/              # 自定义Hook
│   ├── store/              # 状态管理
│   ├── services/           # API服务
│   ├── utils/              # 工具函数
│   ├── types/              # TypeScript类型定义
│   ├── App.tsx             # 主应用组件
│   ├── main.tsx            # 应用入口
│   └── index.css           # 全局样式
├── public/                 # 静态资源
├── package.json            # 项目配置
├── tsconfig.json           # TypeScript配置
├── vite.config.ts          # Vite配置
└── README.md               # 项目说明
```

## 安装和运行

### 前置要求
- Node.js >= 18.0.0
- npm >= 9.0.0

### 安装步骤

1. **安装依赖**
   ```bash
   npm install
   ```

2. **启动开发服务器**
   ```bash
   npm run dev
   ```

3. **构建生产版本**
   ```bash
   npm run build
   ```

4. **运行测试**
   ```bash
   npm run test
   ```

### 环境变量配置

创建 `.env` 文件并配置以下变量：

```env
VITE_API_BASE_URL=http://localhost:3001/api
VITE_AI_SERVICE_URL=http://localhost:8000
```

## 开发规范

### 代码规范
- 使用TypeScript进行类型检查
- 遵循ESLint规则
- 组件使用函数式组件 + Hooks
- 文件命名使用PascalCase（组件）或camelCase（工具函数）

### 目录规范
- 组件文件放在 `src/components/` 目录下
- 页面文件放在 `src/pages/` 目录下
- 工具函数放在 `src/utils/` 目录下
- 类型定义放在 `src/types/` 目录下

### Git提交规范
- feat: 新功能
- fix: 修复bug
- docs: 文档更新
- style: 代码格式调整
- refactor: 代码重构
- test: 测试相关
- chore: 构建过程或辅助工具的变动

## 用户角色

- **学生** - 体验完整的会展项目流程
- **教师** - 监控学生进度，管理教学内容
- **企业** - 使用专业的会展管理工具
- **管理员** - 系统管理和维护

## 开发计划

### 第1周：基础架构
- [x] 项目初始化和环境搭建
- [ ] 用户权限管理系统
- [ ] 基础UI组件开发

### 第2周：AI功能
- [ ] AI方案生成模块
- [ ] 2D效果图生成模块
- [ ] AI服务集成

### 第3周：3D和管理
- [ ] 3D模型生成和展示
- [ ] 施工管理系统
- [ ] 数据管理优化

### 第4周：完善和部署
- [ ] 教学监控系统
- [ ] 系统测试和优化
- [ ] 部署和文档完善

## 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 联系我们

- 项目主页：[GitHub Repository]
- 问题反馈：[Issues]
- 邮箱：<EMAIL>
