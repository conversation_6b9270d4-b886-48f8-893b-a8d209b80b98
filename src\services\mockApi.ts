import { DEMO_DATA } from '@/utils/constants'
import type { User, Project, ExhibitionPlan, ApiResponse, UserRole } from '@/types'

// 模拟延迟
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms))

// 模拟当前用户
let currentUser: User | null = null

export const mockApi = {
  // 用户认证
  async login(username: string, password: string): Promise<ApiResponse<{ user: User; token: string }>> {
    await delay(1000)
    
    const user = DEMO_DATA.users.find(u => u.username === username)
    if (user && password === '123456') {
      currentUser = {
        ...user,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
      return {
        success: true,
        data: {
          user: currentUser,
          token: 'mock-jwt-token-' + user.id
        }
      }
    }
    
    return {
      success: false,
      error: '用户名或密码错误'
    }
  },

  async logout(): Promise<ApiResponse> {
    await delay(500)
    currentUser = null
    return { success: true }
  },

  async getCurrentUser(): Promise<ApiResponse<User>> {
    await delay(300)
    if (currentUser) {
      return { success: true, data: currentUser }
    }
    return { success: false, error: '未登录' }
  },

  // 项目管理
  async getProjects(): Promise<ApiResponse<Project[]>> {
    await delay(800)
    const projects = DEMO_DATA.projects.map(p => ({
      ...p,
      createdAt: new Date(p.createdAt).toISOString(),
      updatedAt: new Date(p.updatedAt).toISOString()
    }))
    return { success: true, data: projects }
  },

  async createProject(projectData: Partial<Project>): Promise<ApiResponse<Project>> {
    await delay(1200)
    const newProject: Project = {
      id: Date.now().toString(),
      name: projectData.name || '新项目',
      description: projectData.description || '',
      type: projectData.type || 'exhibition',
      status: 'draft',
      createdBy: currentUser?.id || '1',
      participants: [currentUser?.id || '1'],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
    return { success: true, data: newProject }
  },

  // AI方案生成
  async generatePlan(projectId: string, requirements: any): Promise<ApiResponse<ExhibitionPlan>> {
    await delay(3000) // 模拟AI生成时间
    
    const plan: ExhibitionPlan = {
      id: Date.now().toString(),
      projectId,
      name: `AI生成方案 - ${new Date().toLocaleString()}`,
      layout: {
        width: 1200,
        height: 800,
        scale: 1,
        areas: [
          {
            id: 'main-area',
            name: '主展区',
            type: 'exhibition',
            coordinates: [
              { x: 100, y: 100 },
              { x: 1100, y: 100 },
              { x: 1100, y: 700 },
              { x: 100, y: 700 }
            ],
            properties: { capacity: 300 }
          },
          {
            id: 'entrance',
            name: '入口区',
            type: 'entrance',
            coordinates: [
              { x: 500, y: 50 },
              { x: 700, y: 50 },
              { x: 700, y: 100 },
              { x: 500, y: 100 }
            ],
            properties: {}
          }
        ]
      },
      booths: [
        {
          id: 'booth-1',
          name: 'A01',
          size: { width: 120, height: 80 },
          position: { x: 150, y: 150 },
          type: 'standard',
          exhibitor: '参展商A'
        },
        {
          id: 'booth-2',
          name: 'A02',
          size: { width: 120, height: 80 },
          position: { x: 300, y: 150 },
          type: 'corner',
          exhibitor: '参展商B'
        },
        {
          id: 'booth-3',
          name: 'B01',
          size: { width: 150, height: 100 },
          position: { x: 500, y: 200 },
          type: 'island',
          exhibitor: '参展商C'
        }
      ],
      flowPaths: [
        {
          id: 'main-path',
          name: '主通道',
          points: [
            { x: 600, y: 100 },
            { x: 600, y: 700 }
          ],
          width: 60,
          type: 'main'
        },
        {
          id: 'side-path',
          name: '侧通道',
          points: [
            { x: 100, y: 400 },
            { x: 1100, y: 400 }
          ],
          width: 40,
          type: 'secondary'
        }
      ],
      generatedAt: new Date().toISOString()
    }
    
    return { success: true, data: plan }
  },

  // 2D渲染
  async render2D(planId: string, options: any): Promise<ApiResponse<{ imageUrl: string; renderTime: number }>> {
    await delay(2500) // 模拟渲染时间
    
    // 模拟生成的图片URL
    const imageUrl = `https://picsum.photos/800/600?random=${Date.now()}`
    
    return {
      success: true,
      data: {
        imageUrl,
        renderTime: 2.5
      }
    }
  },

  // 3D模型
  async generate3D(planId: string): Promise<ApiResponse<{ modelUrl: string; previewUrl: string }>> {
    await delay(4000) // 模拟3D生成时间
    
    return {
      success: true,
      data: {
        modelUrl: '/models/demo-exhibition.glb',
        previewUrl: `https://picsum.photos/400/300?random=${Date.now()}`
      }
    }
  },

  // 统计数据
  async getStatistics(): Promise<ApiResponse<any>> {
    await delay(500)
    
    return {
      success: true,
      data: {
        activeProjects: 12,
        generatedPlans: 48,
        renderedImages: 156,
        onlineUsers: 24,
        totalUsers: 89,
        completedProjects: 35
      }
    }
  }
}

// 导出用户角色检查函数
export const hasPermission = (permission: string): boolean => {
  if (!currentUser) return false
  
  const userRole = currentUser.role as keyof typeof import('@/utils/constants').ROLE_PERMISSIONS
  const permissions = (DEMO_DATA as any).ROLE_PERMISSIONS?.[userRole] || []
  
  return permissions.includes(permission) || permissions.includes('all_permissions')
}
