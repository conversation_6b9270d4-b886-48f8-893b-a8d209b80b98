import React, { useState } from 'react'
import { Card, Table, Tag, Space, Button, Modal, Typography, Progress, Tabs, List, Avatar, Rate, message } from 'antd'
import { useNavigate } from 'react-router-dom'
import {
  EditOutlined,
  EyeOutlined,
  PlayCircleOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  FileTextOutlined,
  TrophyOutlined,
  CommentOutlined,
} from '@ant-design/icons'
import { useAuthStore } from '@/store/authStore'
import type { ColumnsType } from 'antd/es/table'

const { Title, Paragraph, Text } = Typography
const { TabPane } = Tabs

interface Assignment {
  id: string
  title: string
  courseName: string
  instructor: string
  type: 'theory' | 'practice'
  description: string
  dueDate: string
  submittedAt?: string
  status: 'pending' | 'submitted' | 'graded' | 'overdue'
  score?: number
  maxScore: number
  feedback?: string
  rating?: number
  attachments?: string[]
}

interface Exam {
  id: string
  title: string
  courseName: string
  instructor: string
  startTime: string
  endTime: string
  duration: number
  totalQuestions: number
  status: 'upcoming' | 'ongoing' | 'completed' | 'missed'
  score?: number
  maxScore: number
  attempts: number
  maxAttempts: number
}

const StudentAssignmentCenter: React.FC = () => {
  const navigate = useNavigate()
  const { user } = useAuthStore()
  const [activeTab, setActiveTab] = useState('assignments')
  const [selectedAssignment, setSelectedAssignment] = useState<Assignment | null>(null)
  const [isDetailModalVisible, setIsDetailModalVisible] = useState(false)

  // 模拟作业数据
  const [assignments] = useState<Assignment[]>([
    {
      id: '1',
      title: '展位设计方案',
      courseName: '会展策划基础',
      instructor: '张教授',
      type: 'practice',
      description: '根据课程内容，设计一个汽车展的展位方案，要求包含平面布局和3D效果图',
      dueDate: '2024-01-25 23:59',
      status: 'pending',
      maxScore: 100
    },
    {
      id: '2',
      title: '理论知识测试',
      courseName: '展示设计原理',
      instructor: '李教授',
      type: 'theory',
      description: '测试对展示设计基本原理的理解',
      dueDate: '2024-01-22 23:59',
      submittedAt: '2024-01-21 15:30',
      status: 'graded',
      score: 85,
      maxScore: 100,
      feedback: '对基本概念掌握较好，但在实际应用方面还需加强练习。',
      rating: 4
    },
    {
      id: '3',
      title: '3D建模练习',
      courseName: '3D建模技术',
      instructor: '王老师',
      type: 'practice',
      description: '使用3D软件制作一个简单的展示道具模型',
      dueDate: '2024-01-20 23:59',
      submittedAt: '2024-01-19 18:45',
      status: 'submitted',
      maxScore: 100
    }
  ])

  // 模拟考试数据
  const [exams] = useState<Exam[]>([
    {
      id: '1',
      title: '会展策划基础期中考试',
      courseName: '会展策划基础',
      instructor: '张教授',
      startTime: '2024-01-28 09:00',
      endTime: '2024-01-28 10:30',
      duration: 90,
      totalQuestions: 50,
      status: 'upcoming',
      maxScore: 100,
      attempts: 0,
      maxAttempts: 1
    },
    {
      id: '2',
      title: '展示设计原理测试',
      courseName: '展示设计原理',
      instructor: '李教授',
      startTime: '2024-01-15 14:00',
      endTime: '2024-01-15 15:00',
      duration: 60,
      totalQuestions: 30,
      status: 'completed',
      score: 88,
      maxScore: 100,
      attempts: 1,
      maxAttempts: 1
    }
  ])

  const assignmentColumns: ColumnsType<Assignment> = [
    {
      title: '作业信息',
      key: 'assignmentInfo',
      render: (_, record) => (
        <Space direction="vertical" size={0}>
          <Text strong style={{ cursor: 'pointer' }} onClick={() => handleViewDetail(record)}>
            {record.title}
          </Text>
          <Text type="secondary" style={{ fontSize: 12 }}>
            {record.courseName} - {record.instructor}
          </Text>
          <Tag color={record.type === 'practice' ? 'blue' : 'green'}>
            {record.type === 'practice' ? '实操作业' : '理论测试'}
          </Tag>
        </Space>
      )
    },
    {
      title: '截止时间',
      dataIndex: 'dueDate',
      key: 'dueDate',
      render: (dueDate) => (
        <Space direction="vertical" size={0}>
          <Text>{dueDate}</Text>
          <Text type="secondary" style={{ fontSize: 12 }}>
            {new Date(dueDate) > new Date() ? '还有时间' : '已截止'}
          </Text>
        </Space>
      )
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <Tag color={
          status === 'pending' ? 'orange' :
          status === 'submitted' ? 'blue' :
          status === 'graded' ? 'green' : 'red'
        }>
          {status === 'pending' ? '待提交' :
           status === 'submitted' ? '已提交' :
           status === 'graded' ? '已评分' : '已逾期'}
        </Tag>
      )
    },
    {
      title: '成绩',
      key: 'score',
      render: (_, record) => (
        record.score !== undefined ? (
          <Space direction="vertical" size={0}>
            <Text strong>{record.score}/{record.maxScore}</Text>
            {record.rating && <Rate disabled defaultValue={record.rating} size="small" />}
          </Space>
        ) : (
          <Text type="secondary">-</Text>
        )
      )
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space>
          <Button 
            type="link" 
            icon={<EyeOutlined />}
            onClick={() => handleViewDetail(record)}
          >
            查看
          </Button>
          {record.status === 'pending' && (
            <Button 
              type="primary" 
              size="small"
              icon={record.type === 'practice' ? <EditOutlined /> : <PlayCircleOutlined />}
              onClick={() => handleStartAssignment(record)}
            >
              {record.type === 'practice' ? '开始作业' : '开始答题'}
            </Button>
          )}
        </Space>
      )
    }
  ]

  const examColumns: ColumnsType<Exam> = [
    {
      title: '考试信息',
      key: 'examInfo',
      render: (_, record) => (
        <Space direction="vertical" size={0}>
          <Text strong>{record.title}</Text>
          <Text type="secondary" style={{ fontSize: 12 }}>
            {record.courseName} - {record.instructor}
          </Text>
          <Text style={{ fontSize: 12 }}>
            {record.totalQuestions}题 | {record.duration}分钟
          </Text>
        </Space>
      )
    },
    {
      title: '考试时间',
      key: 'examTime',
      render: (_, record) => (
        <Space direction="vertical" size={0}>
          <Text>{record.startTime}</Text>
          <Text type="secondary" style={{ fontSize: 12 }}>
            至 {record.endTime}
          </Text>
        </Space>
      )
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <Tag color={
          status === 'upcoming' ? 'orange' :
          status === 'ongoing' ? 'blue' :
          status === 'completed' ? 'green' : 'red'
        }>
          {status === 'upcoming' ? '即将开始' :
           status === 'ongoing' ? '进行中' :
           status === 'completed' ? '已完成' : '已错过'}
        </Tag>
      )
    },
    {
      title: '成绩',
      key: 'score',
      render: (_, record) => (
        record.score !== undefined ? (
          <Text strong>{record.score}/{record.maxScore}</Text>
        ) : (
          <Text type="secondary">-</Text>
        )
      )
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space>
          {record.status === 'upcoming' && (
            <Button type="primary" size="small">
              进入考试
            </Button>
          )}
          {record.status === 'completed' && (
            <Button type="link" icon={<EyeOutlined />}>
              查看结果
            </Button>
          )}
        </Space>
      )
    }
  ]

  const handleViewDetail = (assignment: Assignment) => {
    setSelectedAssignment(assignment)
    setIsDetailModalVisible(true)
  }

  const handleStartAssignment = (assignment: Assignment) => {
    if (assignment.type === 'practice') {
      // 启动3D编辑器
      message.info('正在启动3D编辑器...')
      window.open('/design-3d', '_blank')
    } else {
      // 启动理论测试
      message.info('正在进入答题页面...')
      navigate(`/learning/exam/${assignment.id}`)
    }
  }

  return (
    <div style={{ padding: '24px' }}>
      {/* 页面标题 */}
      <div style={{ marginBottom: 24 }}>
        <Title level={2}>作业中心</Title>
        <Paragraph>
          管理您的作业和考试，跟踪学习进度
        </Paragraph>
      </div>

      {/* 主要内容 */}
      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane tab="我的作业" key="assignments">
          <Card>
            <Table
              columns={assignmentColumns}
              dataSource={assignments}
              rowKey="id"
              pagination={{ pageSize: 10 }}
            />
          </Card>
        </TabPane>

        <TabPane tab="考试安排" key="exams">
          <Card>
            <Table
              columns={examColumns}
              dataSource={exams}
              rowKey="id"
              pagination={{ pageSize: 10 }}
            />
          </Card>
        </TabPane>

        <TabPane tab="成绩统计" key="grades">
          <Card title="成绩概览">
            <List
              dataSource={assignments.filter(a => a.status === 'graded')}
              renderItem={assignment => (
                <List.Item>
                  <List.Item.Meta
                    avatar={
                      <Avatar 
                        icon={<TrophyOutlined />}
                        style={{ 
                          backgroundColor: assignment.score! >= 90 ? '#52c41a' :
                                          assignment.score! >= 80 ? '#1890ff' :
                                          assignment.score! >= 70 ? '#faad14' : '#f5222d'
                        }}
                      />
                    }
                    title={assignment.title}
                    description={
                      <Space direction="vertical" size={0}>
                        <Text type="secondary">{assignment.courseName}</Text>
                        <Text strong style={{ color: '#52c41a' }}>
                          得分：{assignment.score}/{assignment.maxScore}
                        </Text>
                        {assignment.feedback && (
                          <Text type="secondary" style={{ fontSize: 12 }}>
                            <CommentOutlined /> {assignment.feedback}
                          </Text>
                        )}
                      </Space>
                    }
                  />
                  <div>
                    <Progress 
                      type="circle" 
                      percent={Math.round((assignment.score! / assignment.maxScore) * 100)}
                      width={60}
                      strokeColor={
                        assignment.score! >= 90 ? '#52c41a' :
                        assignment.score! >= 80 ? '#1890ff' :
                        assignment.score! >= 70 ? '#faad14' : '#f5222d'
                      }
                    />
                  </div>
                </List.Item>
              )}
            />
          </Card>
        </TabPane>
      </Tabs>

      {/* 作业详情模态框 */}
      <Modal
        title="作业详情"
        open={isDetailModalVisible}
        onCancel={() => setIsDetailModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setIsDetailModalVisible(false)}>
            关闭
          </Button>,
          selectedAssignment?.status === 'pending' && (
            <Button 
              key="start" 
              type="primary"
              onClick={() => {
                handleStartAssignment(selectedAssignment!)
                setIsDetailModalVisible(false)
              }}
            >
              {selectedAssignment?.type === 'practice' ? '开始作业' : '开始答题'}
            </Button>
          )
        ]}
        width={600}
      >
        {selectedAssignment && (
          <Space direction="vertical" style={{ width: '100%' }}>
            <div>
              <Text strong>作业标题：</Text>
              <Text>{selectedAssignment.title}</Text>
            </div>
            <div>
              <Text strong>所属课程：</Text>
              <Text>{selectedAssignment.courseName}</Text>
            </div>
            <div>
              <Text strong>任课教师：</Text>
              <Text>{selectedAssignment.instructor}</Text>
            </div>
            <div>
              <Text strong>作业类型：</Text>
              <Tag color={selectedAssignment.type === 'practice' ? 'blue' : 'green'}>
                {selectedAssignment.type === 'practice' ? '实操作业' : '理论测试'}
              </Tag>
            </div>
            <div>
              <Text strong>截止时间：</Text>
              <Text>{selectedAssignment.dueDate}</Text>
            </div>
            <div>
              <Text strong>作业要求：</Text>
              <Paragraph style={{ marginTop: 8 }}>
                {selectedAssignment.description}
              </Paragraph>
            </div>
            {selectedAssignment.status === 'graded' && (
              <>
                <div>
                  <Text strong>得分：</Text>
                  <Text style={{ color: '#52c41a', fontSize: 16 }}>
                    {selectedAssignment.score}/{selectedAssignment.maxScore}
                  </Text>
                </div>
                {selectedAssignment.rating && (
                  <div>
                    <Text strong>评级：</Text>
                    <Rate disabled defaultValue={selectedAssignment.rating} />
                  </div>
                )}
                {selectedAssignment.feedback && (
                  <div>
                    <Text strong>教师反馈：</Text>
                    <Paragraph style={{ marginTop: 8, padding: 12, background: '#f5f5f5', borderRadius: 6 }}>
                      {selectedAssignment.feedback}
                    </Paragraph>
                  </div>
                )}
              </>
            )}
          </Space>
        )}
      </Modal>
    </div>
  )
}

export default StudentAssignmentCenter
