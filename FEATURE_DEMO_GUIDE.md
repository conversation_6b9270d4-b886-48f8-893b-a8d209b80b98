# 会展行业虚实融合实训教学平台 - 功能演示指南

## 🚀 快速开始

### 启动项目
```bash
# 进入项目目录
cd c:\Users\<USER>\Documents\augment-projects\MCTT

# 安装依赖（如果还没安装）
npm install

# 启动开发服务器
npm run dev
```

访问 `http://localhost:5173` 查看平台

## 📋 核心功能演示路径

### 1. 统一门户体验
**路径：** `/portal`
**演示内容：**
- 登录后的统一入口界面
- 各平台入口卡片（根据用户角色显示）
- 个人统计数据展示
- 快速操作按钮

**操作步骤：**
1. 点击不同平台卡片进入相应功能
2. 查看个人统计数据
3. 使用快速操作按钮

### 2. 项目管理平台完整流程
**路径：** `/projects`

#### 2.1 项目列表页
**演示内容：**
- 项目信息集中展示
- 搜索和筛选功能
- 新增项目按钮
- 项目状态可视化

**操作步骤：**
1. 使用搜索框搜索项目
2. 使用状态筛选器过滤项目
3. 点击"新增项目"创建项目
4. 点击项目名称进入详情

#### 2.2 项目详情页
**演示内容：**
- 标签页布局（基础信息、方案详情、项目成员、材料清单、项目文件）
- 成员管理功能
- 文件上传下载
- 材料清单管理

**操作步骤：**
1. 切换不同标签页查看信息
2. 在"项目成员"中查看团队
3. 在"项目文件"中上传/下载文件
4. 在"材料清单"中查看所需材料

#### 2.3 方案审批页
**路径：** `/projects/proposal`
**演示内容：**
- 待审批方案列表
- 方案详情预览
- 审批流程可视化
- 批准/驳回操作

**操作步骤：**
1. 查看待审批方案列表
2. 点击"审阅"进入方案详情
3. 查看方案附件和描述
4. 进行批准或驳回操作

### 3. 教管学考平台

#### 3.1 学生端课程学习
**路径：** `/learning/courses`
**演示内容：**
- 左侧导航栏功能
- 视频学习播放器
- 实时笔记功能
- 作业中心

**操作步骤：**
1. 点击"学习"标签进入视频学习
2. 使用视频控制栏（播放/暂停/进度）
3. 在"实时笔记"中添加学习笔记
4. 切换到"作业"标签查看作业
5. 点击"开始作业"启动3D编辑器

#### 3.2 教师端管理
**路径：** `/learning/classes`
**演示内容：**
- 班级管理
- 课程管理
- 作业管理
- 学情分析

**操作步骤：**
1. 在"我的班级"中创建和管理班级
2. 在"我的课程"中创建课程
3. 在"作业管理"中布置作业
4. 查看学情分析数据

### 4. 3D编辑器体验
**路径：** `/design-3d`
**演示内容：**
- 专业级3D编辑界面
- 工具栏和属性面板
- 素材库和材质库
- 场景状态切换

**操作步骤：**
1. 使用左侧工具栏选择编辑工具
2. 从底部素材库拖拽模型到视口
3. 在右侧属性面板调整参数
4. 切换日间/夜间场景状态
5. 使用保存和导出功能

### 5. VR编辑器
**路径：** `/vr-editor`
**演示内容：**
- VR场景管理
- 模板库
- 多人协同功能
- VR设备检测

**操作步骤：**
1. 查看VR场景列表
2. 创建新的VR场景
3. 使用模板库快速创建
4. 预览VR场景效果

### 6. 内容空间
**路径：** `/content-space`
**演示内容：**
- 作品展示和浏览
- 作品发布功能
- 互动评价系统
- 个人作品集

**操作步骤：**
1. 浏览精选作品
2. 点击"发布作品"上传自己的作品
3. 查看作品详情和评论
4. 切换到"我的作品"查看个人作品集

### 7. 管理员后台
**路径：** `/admin`
**演示内容：**
- 用户管理
- 角色权限配置
- 系统统计
- 批量操作

**操作步骤：**
1. 在用户管理中添加/编辑用户
2. 配置角色权限
3. 查看系统统计数据
4. 使用批量导入功能

## 🎯 关键交互流程演示

### 完整的项目实训流程
1. **教师创建项目** → 项目管理平台创建新项目
2. **学生申请立项** → 提交方案到审批系统
3. **教师审批方案** → 方案审批页面进行评审
4. **学生进行设计** → 3D编辑器中完成设计
5. **提交作业成果** → 上传到项目文件库
6. **教师评分反馈** → 教学管理系统中批改
7. **发布优秀作品** → 内容空间展示分享

### 完整的教学流程
1. **教师创建班级和课程** → 教师管理页面
2. **学生在线学习** → 学生课程页面观看视频
3. **教师布置作业** → 区分理论和实操作业
4. **学生完成作业** → 自动启动相应工具
5. **教师批改评分** → 查看统计分析
6. **学生查看反馈** → 学习进度和成绩

## 🔧 技术特性展示

### 响应式设计
- 在不同屏幕尺寸下测试界面适配
- 移动端和桌面端的体验差异

### 权限控制
- 使用不同角色账号登录查看功能差异
- 学生/教师/管理员的界面和操作权限

### 数据可视化
- 统计图表和进度条
- 实时数据更新
- 交互式数据展示

### 文件管理
- 文件上传下载功能
- 图片预览和文档查看
- 文件类型识别和图标显示

## 🎨 UI/UX 亮点

### 现代化设计
- Ant Design组件库的专业外观
- 一致的设计语言和交互模式
- 清晰的信息层级和布局

### 用户体验优化
- 直观的导航和面包屑
- 实时反馈和状态提示
- 快捷操作和批量处理

### 可访问性
- 键盘导航支持
- 屏幕阅读器友好
- 色彩对比度优化

## 📊 数据展示

### 统计仪表板
- 项目进度统计
- 学习数据分析
- 用户活跃度指标
- 系统使用情况

### 可视化图表
- 进度条和环形图
- 时间线和流程图
- 数据表格和列表
- 标签和状态指示

## 🔄 集成演示

### 平台间跳转
- 统一门户到各子平台
- 3D编辑器与项目管理集成
- 教学系统与作业系统联动

### 数据同步
- 用户信息跨平台同步
- 项目数据实时更新
- 学习进度自动记录

## 💡 创新功能

### AI集成
- AI方案生成功能
- 智能推荐系统
- 自动化工作流程

### VR/AR支持
- VR设备检测
- 沉浸式体验
- 多人协同编辑

### 实时协作
- 多用户同时编辑
- 实时评论和反馈
- 版本控制和历史记录

---

## 🎯 演示建议

1. **按角色演示**：分别以学生、教师、管理员身份登录演示
2. **按流程演示**：展示完整的教学或项目管理流程
3. **按功能演示**：重点展示核心功能和创新特性
4. **交互演示**：强调用户体验和界面交互的流畅性

通过以上演示路径，可以全面展示平台的功能完整性、技术先进性和用户体验优化。
