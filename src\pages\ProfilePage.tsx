import React, { useState, useEffect } from 'react'
import { Card, Row, Col, Typography, Button, Form, Input, Upload, Avatar, Tabs, Progress, List, Tag, Space, Statistic, Timeline } from 'antd'
import { useNavigate } from 'react-router-dom'
import {
  UserOutlined,
  EditOutlined,
  CameraOutlined,
  BookOutlined,
  ProjectOutlined,
  TrophyOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  StarOutlined,
  UploadOutlined,
  DownloadOutlined,
  EyeOutlined,
} from '@ant-design/icons'
import { useAuthStore } from '@/store/authStore'
import { ROUTES } from '@/utils/constants'

const { Title, Paragraph, Text } = Typography
const { TabPane } = Tabs

interface LearningRecord {
  id: string
  courseTitle: string
  completedAt: string
  score: number
  duration: number
  type: 'course' | 'assignment' | 'exam'
}

interface Achievement {
  id: string
  title: string
  description: string
  earnedAt: string
  icon: string
  level: 'bronze' | 'silver' | 'gold'
}

interface LearningReport {
  totalCourses: number
  completedCourses: number
  totalAssignments: number
  completedAssignments: number
  averageScore: number
  totalStudyTime: number
  currentStreak: number
  achievements: Achievement[]
  recentActivities: LearningRecord[]
}

const ProfilePage: React.FC = () => {
  const navigate = useNavigate()
  const { user, updateProfile } = useAuthStore()
  const [activeTab, setActiveTab] = useState('profile')
  const [isEditing, setIsEditing] = useState(false)
  const [form] = Form.useForm()

  // 模拟学习报告数据
  const [learningReport] = useState<LearningReport>({
    totalCourses: 8,
    completedCourses: 5,
    totalAssignments: 15,
    completedAssignments: 12,
    averageScore: 85.6,
    totalStudyTime: 120,
    currentStreak: 7,
    achievements: [
      {
        id: '1',
        title: '学习新手',
        description: '完成第一门课程',
        earnedAt: '2024-01-15',
        icon: '🎓',
        level: 'bronze'
      },
      {
        id: '2',
        title: '勤奋学者',
        description: '连续学习7天',
        earnedAt: '2024-01-20',
        icon: '📚',
        level: 'silver'
      },
      {
        id: '3',
        title: '项目达人',
        description: '完成5个项目',
        earnedAt: '2024-01-18',
        icon: '🏆',
        level: 'gold'
      }
    ],
    recentActivities: [
      {
        id: '1',
        courseTitle: '会展策划基础',
        completedAt: '2024-01-20',
        score: 88,
        duration: 45,
        type: 'course'
      },
      {
        id: '2',
        courseTitle: '展示设计原理',
        completedAt: '2024-01-19',
        score: 92,
        duration: 30,
        type: 'assignment'
      },
      {
        id: '3',
        courseTitle: '3D建模技术',
        completedAt: '2024-01-18',
        score: 78,
        duration: 60,
        type: 'exam'
      }
    ]
  })

  useEffect(() => {
    if (user) {
      form.setFieldsValue({
        username: user.username,
        email: user.email,
        realName: user.realName,
        phone: user.phone,
        department: user.department,
        bio: user.bio
      })
    }
  }, [user, form])

  const handleSaveProfile = async (values: any) => {
    try {
      await updateProfile(values)
      setIsEditing(false)
      console.log('个人资料更新成功:', values)
    } catch (error) {
      console.error('更新失败:', error)
    }
  }

  const handleAvatarChange = (info: any) => {
    if (info.file.status === 'done') {
      console.log('头像上传成功')
    }
  }

  const getAchievementColor = (level: string) => {
    const colorMap: Record<string, string> = {
      bronze: '#cd7f32',
      silver: '#c0c0c0',
      gold: '#ffd700'
    }
    return colorMap[level] || '#d9d9d9'
  }

  const getActivityIcon = (type: string) => {
    const iconMap: Record<string, React.ReactNode> = {
      course: <BookOutlined style={{ color: '#1890ff' }} />,
      assignment: <EditOutlined style={{ color: '#52c41a' }} />,
      exam: <TrophyOutlined style={{ color: '#fa8c16' }} />
    }
    return iconMap[type] || <BookOutlined />
  }

  const getActivityTypeText = (type: string) => {
    const textMap: Record<string, string> = {
      course: '课程学习',
      assignment: '作业提交',
      exam: '考试完成'
    }
    return textMap[type] || type
  }

  return (
    <div style={{ padding: '24px' }}>
      {/* 页面标题 */}
      <div style={{ marginBottom: 24 }}>
        <Title level={2}>个人中心</Title>
        <Paragraph>
          管理个人信息，查看学习进度和成就
        </Paragraph>
      </div>

      <Row gutter={24}>
        {/* 左侧个人信息卡片 */}
        <Col span={8}>
          <Card>
            <div style={{ textAlign: 'center', marginBottom: 24 }}>
              <Upload
                showUploadList={false}
                onChange={handleAvatarChange}
                disabled={!isEditing}
              >
                <div style={{ position: 'relative', display: 'inline-block' }}>
                  <Avatar 
                    size={100} 
                    src={user?.avatar} 
                    icon={<UserOutlined />}
                    style={{ cursor: isEditing ? 'pointer' : 'default' }}
                  />
                  {isEditing && (
                    <div style={{
                      position: 'absolute',
                      bottom: 0,
                      right: 0,
                      background: '#1890ff',
                      borderRadius: '50%',
                      width: 24,
                      height: 24,
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center'
                    }}>
                      <CameraOutlined style={{ color: 'white', fontSize: 12 }} />
                    </div>
                  )}
                </div>
              </Upload>
              
              <div style={{ marginTop: 16 }}>
                <Title level={4} style={{ margin: 0 }}>
                  {user?.realName || user?.username}
                </Title>
                <Text type="secondary">
                  {user?.role === 'student' ? '学生' : 
                   user?.role === 'teacher' ? '教师' : 
                   user?.role === 'enterprise' ? '企业用户' : '管理员'}
                </Text>
              </div>
            </div>

            <div style={{ marginBottom: 16 }}>
              <Text strong>邮箱：</Text>
              <Text>{user?.email}</Text>
            </div>
            
            <div style={{ marginBottom: 16 }}>
              <Text strong>部门：</Text>
              <Text>{user?.department || '未设置'}</Text>
            </div>
            
            <div style={{ marginBottom: 16 }}>
              <Text strong>注册时间：</Text>
              <Text>{user?.createdAt || '2024-01-01'}</Text>
            </div>

            <Button 
              type="primary" 
              block 
              icon={<EditOutlined />}
              onClick={() => setIsEditing(!isEditing)}
            >
              {isEditing ? '取消编辑' : '编辑资料'}
            </Button>
          </Card>
        </Col>

        {/* 右侧主要内容 */}
        <Col span={16}>
          <Tabs activeKey={activeTab} onChange={setActiveTab}>
            <TabPane tab="个人资料" key="profile">
              <Card title="基本信息">
                <Form
                  form={form}
                  layout="vertical"
                  onFinish={handleSaveProfile}
                >
                  <Row gutter={16}>
                    <Col span={12}>
                      <Form.Item
                        name="username"
                        label="用户名"
                        rules={[{ required: true, message: '请输入用户名' }]}
                      >
                        <Input disabled={!isEditing} />
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item
                        name="realName"
                        label="真实姓名"
                        rules={[{ required: true, message: '请输入真实姓名' }]}
                      >
                        <Input disabled={!isEditing} />
                      </Form.Item>
                    </Col>
                  </Row>
                  
                  <Row gutter={16}>
                    <Col span={12}>
                      <Form.Item
                        name="email"
                        label="邮箱"
                        rules={[
                          { required: true, message: '请输入邮箱' },
                          { type: 'email', message: '请输入有效的邮箱地址' }
                        ]}
                      >
                        <Input disabled={!isEditing} />
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item
                        name="phone"
                        label="手机号"
                      >
                        <Input disabled={!isEditing} />
                      </Form.Item>
                    </Col>
                  </Row>
                  
                  <Form.Item
                    name="department"
                    label="部门/院系"
                  >
                    <Input disabled={!isEditing} />
                  </Form.Item>
                  
                  <Form.Item
                    name="bio"
                    label="个人简介"
                  >
                    <Input.TextArea 
                      rows={4} 
                      disabled={!isEditing}
                      placeholder="介绍一下自己吧..."
                    />
                  </Form.Item>
                  
                  {isEditing && (
                    <Form.Item>
                      <Space>
                        <Button type="primary" htmlType="submit">
                          保存修改
                        </Button>
                        <Button onClick={() => setIsEditing(false)}>
                          取消
                        </Button>
                      </Space>
                    </Form.Item>
                  )}
                </Form>
              </Card>
            </TabPane>

            <TabPane tab="学习报告" key="learning">
              {/* 学习统计 */}
              <Row gutter={16} style={{ marginBottom: 24 }}>
                <Col span={6}>
                  <Card>
                    <Statistic
                      title="完成课程"
                      value={learningReport.completedCourses}
                      suffix={`/ ${learningReport.totalCourses}`}
                      valueStyle={{ color: '#3f8600' }}
                    />
                  </Card>
                </Col>
                <Col span={6}>
                  <Card>
                    <Statistic
                      title="平均分数"
                      value={learningReport.averageScore}
                      precision={1}
                      suffix="分"
                      valueStyle={{ color: '#1890ff' }}
                    />
                  </Card>
                </Col>
                <Col span={6}>
                  <Card>
                    <Statistic
                      title="学习时长"
                      value={learningReport.totalStudyTime}
                      suffix="小时"
                      valueStyle={{ color: '#722ed1' }}
                    />
                  </Card>
                </Col>
                <Col span={6}>
                  <Card>
                    <Statistic
                      title="连续学习"
                      value={learningReport.currentStreak}
                      suffix="天"
                      valueStyle={{ color: '#fa8c16' }}
                    />
                  </Card>
                </Col>
              </Row>

              {/* 学习进度 */}
              <Card title="学习进度" style={{ marginBottom: 24 }}>
                <div style={{ marginBottom: 16 }}>
                  <Text strong>课程完成度</Text>
                  <Progress 
                    percent={Math.round((learningReport.completedCourses / learningReport.totalCourses) * 100)}
                    strokeColor="#52c41a"
                    style={{ marginTop: 8 }}
                  />
                </div>
                <div>
                  <Text strong>作业完成度</Text>
                  <Progress 
                    percent={Math.round((learningReport.completedAssignments / learningReport.totalAssignments) * 100)}
                    strokeColor="#1890ff"
                    style={{ marginTop: 8 }}
                  />
                </div>
              </Card>

              {/* 最近活动 */}
              <Card title="最近活动">
                <Timeline>
                  {learningReport.recentActivities.map(activity => (
                    <Timeline.Item
                      key={activity.id}
                      dot={getActivityIcon(activity.type)}
                    >
                      <div>
                        <Text strong>{activity.courseTitle}</Text>
                        <Tag color="blue" style={{ marginLeft: 8 }}>
                          {getActivityTypeText(activity.type)}
                        </Tag>
                      </div>
                      <div style={{ marginTop: 4 }}>
                        <Text type="secondary">
                          {activity.completedAt} • 得分: {activity.score}分 • 用时: {activity.duration}分钟
                        </Text>
                      </div>
                    </Timeline.Item>
                  ))}
                </Timeline>
              </Card>
            </TabPane>

            <TabPane tab="成就徽章" key="achievements">
              <Card title="我的成就">
                <Row gutter={[16, 16]}>
                  {learningReport.achievements.map(achievement => (
                    <Col span={8} key={achievement.id}>
                      <Card
                        size="small"
                        style={{ 
                          textAlign: 'center',
                          borderColor: getAchievementColor(achievement.level)
                        }}
                      >
                        <div style={{ fontSize: 32, marginBottom: 8 }}>
                          {achievement.icon}
                        </div>
                        <Title level={5} style={{ margin: 0 }}>
                          {achievement.title}
                        </Title>
                        <Text type="secondary" style={{ fontSize: 12 }}>
                          {achievement.description}
                        </Text>
                        <div style={{ marginTop: 8 }}>
                          <Tag color={getAchievementColor(achievement.level)}>
                            {achievement.level.toUpperCase()}
                          </Tag>
                        </div>
                        <Text type="secondary" style={{ fontSize: 12 }}>
                          获得时间: {achievement.earnedAt}
                        </Text>
                      </Card>
                    </Col>
                  ))}
                </Row>
              </Card>
            </TabPane>
          </Tabs>
        </Col>
      </Row>
    </div>
  )
}

export default ProfilePage
