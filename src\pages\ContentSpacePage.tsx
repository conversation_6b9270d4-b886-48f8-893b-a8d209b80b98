import React, { useState, useEffect } from 'react'
import { Card, Row, Col, Typo<PERSON>, Button, Tag, Space, Modal, Form, Input, Upload, Rate, Avatar, Tabs, Statistic, Image } from 'antd'
import { useNavigate } from 'react-router-dom'
import {
  CloudOutlined,
  PlusOutlined,
  EyeOutlined,
  LikeOutlined,
  MessageOutlined,
  ShareAltOutlined,
  UploadOutlined,
  StarOutlined,
  UserOutlined,
  CalendarOutlined,
  TagOutlined,
} from '@ant-design/icons'
import { useAuthStore } from '@/store/authStore'
import { ROUTES } from '@/utils/constants'

const { Title, Paragraph, Text } = Typography
const { TextArea } = Input
const { TabPane } = Tabs

interface Work {
  id: string
  title: string
  description: string
  author: string
  authorAvatar?: string
  category: 'exhibition' | 'conference' | '3d_model' | 'design'
  tags: string[]
  images: string[]
  likes: number
  comments: number
  views: number
  rating: number
  publishedAt: string
  isLiked?: boolean
  isFeatured?: boolean
}

interface Comment {
  id: string
  workId: string
  author: string
  authorAvatar?: string
  content: string
  createdAt: string
  likes: number
}

const ContentSpacePage: React.FC = () => {
  const navigate = useNavigate()
  const { user } = useAuthStore()
  const [activeTab, setActiveTab] = useState('gallery')
  const [isPublishModalVisible, setIsPublishModalVisible] = useState(false)
  const [selectedWork, setSelectedWork] = useState<Work | null>(null)
  const [isDetailModalVisible, setIsDetailModalVisible] = useState(false)
  const [publishForm] = Form.useForm()

  // 模拟作品数据
  const [works] = useState<Work[]>([
    {
      id: '1',
      title: '2024春季汽车展设计方案',
      description: '这是一个现代化的汽车展览设计方案，采用了流线型的展台设计和智能化的展示系统。',
      author: '张同学',
      authorAvatar: '',
      category: 'exhibition',
      tags: ['汽车展', '现代设计', '智能展示'],
      images: ['/api/placeholder/400/300', '/api/placeholder/400/300'],
      likes: 25,
      comments: 8,
      views: 156,
      rating: 4.5,
      publishedAt: '2024-01-20',
      isLiked: false,
      isFeatured: true
    },
    {
      id: '2',
      title: '科技创新大会3D场景',
      description: '运用最新的3D建模技术，打造了一个充满科技感的会议场景。',
      author: '李同学',
      authorAvatar: '',
      category: '3d_model',
      tags: ['3D建模', '科技风', '会议设计'],
      images: ['/api/placeholder/400/300'],
      likes: 18,
      comments: 5,
      views: 89,
      rating: 4.2,
      publishedAt: '2024-01-18',
      isLiked: true,
      isFeatured: false
    },
    {
      id: '3',
      title: '绿色环保主题展览',
      description: '以环保为主题的展览设计，体现了可持续发展的理念。',
      author: '王同学',
      authorAvatar: '',
      category: 'design',
      tags: ['环保主题', '可持续', '绿色设计'],
      images: ['/api/placeholder/400/300', '/api/placeholder/400/300', '/api/placeholder/400/300'],
      likes: 32,
      comments: 12,
      views: 203,
      rating: 4.8,
      publishedAt: '2024-01-15',
      isLiked: false,
      isFeatured: true
    }
  ])

  const [comments] = useState<Comment[]>([
    {
      id: '1',
      workId: '1',
      author: '赵老师',
      authorAvatar: '',
      content: '设计理念很新颖，展台的流线型设计很有创意！',
      createdAt: '2024-01-21',
      likes: 3
    },
    {
      id: '2',
      workId: '1',
      author: '钱同学',
      authorAvatar: '',
      content: '学习了，这个智能展示系统的想法很棒！',
      createdAt: '2024-01-21',
      likes: 1
    }
  ])

  const getCategoryText = (category: string) => {
    const categoryMap: Record<string, string> = {
      exhibition: '展览设计',
      conference: '会议设计',
      '3d_model': '3D模型',
      design: '设计方案'
    }
    return categoryMap[category] || category
  }

  const getCategoryColor = (category: string) => {
    const colorMap: Record<string, string> = {
      exhibition: 'blue',
      conference: 'green',
      '3d_model': 'purple',
      design: 'orange'
    }
    return colorMap[category] || 'default'
  }

  const handleLike = (workId: string) => {
    console.log('点赞作品:', workId)
  }

  const handleViewWork = (work: Work) => {
    setSelectedWork(work)
    setIsDetailModalVisible(true)
  }

  const handlePublishWork = async (values: any) => {
    console.log('发布作品:', values)
    setIsPublishModalVisible(false)
    publishForm.resetFields()
  }

  const handleShare = (work: Work) => {
    console.log('分享作品:', work.id)
  }

  return (
    <div style={{ padding: '24px' }}>
      {/* 页面标题 */}
      <div style={{ marginBottom: 24 }}>
        <Title level={2}>内容空间</Title>
        <Paragraph>
          作品展示与分享平台，构建学习社区
        </Paragraph>
      </div>

      {/* 统计数据 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="总作品数"
              value={works.length}
              prefix={<CloudOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="我的作品"
              value={works.filter(w => w.author === user?.username).length}
              prefix={<UserOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="总点赞数"
              value={works.reduce((sum, work) => sum + work.likes, 0)}
              prefix={<LikeOutlined />}
              valueStyle={{ color: '#fa8c16' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="总浏览量"
              value={works.reduce((sum, work) => sum + work.views, 0)}
              prefix={<EyeOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 主要内容 */}
      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane tab="作品展示" key="gallery">
          <Card
            title="精选作品"
            extra={
              <Button 
                type="primary" 
                icon={<PlusOutlined />}
                onClick={() => setIsPublishModalVisible(true)}
              >
                发布作品
              </Button>
            }
          >
            <Row gutter={[16, 16]}>
              {works.map(work => (
                <Col xs={24} sm={12} lg={8} key={work.id}>
                  <Card
                    hoverable
                    cover={
                      <div style={{ position: 'relative' }}>
                        <Image
                          alt={work.title}
                          src={work.images[0]}
                          height={200}
                          style={{ objectFit: 'cover' }}
                          preview={false}
                        />
                        {work.isFeatured && (
                          <Tag 
                            color="gold" 
                            style={{ 
                              position: 'absolute', 
                              top: 8, 
                              right: 8 
                            }}
                          >
                            <StarOutlined /> 精选
                          </Tag>
                        )}
                      </div>
                    }
                    actions={[
                      <Button 
                        type="text" 
                        icon={<LikeOutlined style={{ color: work.isLiked ? '#ff4d4f' : undefined }} />}
                        onClick={() => handleLike(work.id)}
                      >
                        {work.likes}
                      </Button>,
                      <Button 
                        type="text" 
                        icon={<MessageOutlined />}
                        onClick={() => handleViewWork(work)}
                      >
                        {work.comments}
                      </Button>,
                      <Button 
                        type="text" 
                        icon={<ShareAltOutlined />}
                        onClick={() => handleShare(work)}
                      >
                        分享
                      </Button>
                    ]}
                  >
                    <Card.Meta
                      title={
                        <div style={{ cursor: 'pointer' }} onClick={() => handleViewWork(work)}>
                          {work.title}
                        </div>
                      }
                      description={
                        <Space direction="vertical" style={{ width: '100%' }}>
                          <Text type="secondary" ellipsis>
                            {work.description}
                          </Text>
                          <div>
                            <Space>
                              <Avatar size="small" icon={<UserOutlined />} src={work.authorAvatar} />
                              <Text strong>{work.author}</Text>
                            </Space>
                          </div>
                          <div>
                            <Tag color={getCategoryColor(work.category)}>
                              {getCategoryText(work.category)}
                            </Tag>
                            {work.tags.slice(0, 2).map(tag => (
                              <Tag key={tag} style={{ fontSize: 12 }}>
                                {tag}
                              </Tag>
                            ))}
                          </div>
                          <div>
                            <Space>
                              <Rate disabled defaultValue={work.rating} size="small" />
                              <Text type="secondary" style={{ fontSize: 12 }}>
                                <EyeOutlined /> {work.views}
                              </Text>
                              <Text type="secondary" style={{ fontSize: 12 }}>
                                <CalendarOutlined /> {work.publishedAt}
                              </Text>
                            </Space>
                          </div>
                        </Space>
                      }
                    />
                  </Card>
                </Col>
              ))}
            </Row>
          </Card>
        </TabPane>

        <TabPane tab="我的作品" key="my-works">
          <Card title="我的作品集">
            <Row gutter={[16, 16]}>
              {works
                .filter(work => work.author === user?.username)
                .map(work => (
                  <Col xs={24} sm={12} lg={8} key={work.id}>
                    <Card
                      hoverable
                      cover={
                        <Image
                          alt={work.title}
                          src={work.images[0]}
                          height={200}
                          style={{ objectFit: 'cover' }}
                          preview={false}
                        />
                      }
                      actions={[
                        <Button type="text" icon={<EyeOutlined />}>
                          查看
                        </Button>,
                        <Button type="text">
                          编辑
                        </Button>,
                        <Button type="text" danger>
                          删除
                        </Button>
                      ]}
                    >
                      <Card.Meta
                        title={work.title}
                        description={
                          <Space direction="vertical" style={{ width: '100%' }}>
                            <Text type="secondary" ellipsis>
                              {work.description}
                            </Text>
                            <div>
                              <Space>
                                <Text><LikeOutlined /> {work.likes}</Text>
                                <Text><MessageOutlined /> {work.comments}</Text>
                                <Text><EyeOutlined /> {work.views}</Text>
                              </Space>
                            </div>
                          </Space>
                        }
                      />
                    </Card>
                  </Col>
                ))}
            </Row>
          </Card>
        </TabPane>
      </Tabs>

      {/* 发布作品模态框 */}
      <Modal
        title="发布作品"
        open={isPublishModalVisible}
        onOk={() => publishForm.submit()}
        onCancel={() => setIsPublishModalVisible(false)}
        width={600}
      >
        <Form
          form={publishForm}
          layout="vertical"
          onFinish={handlePublishWork}
        >
          <Form.Item
            name="title"
            label="作品标题"
            rules={[{ required: true, message: '请输入作品标题' }]}
          >
            <Input placeholder="请输入作品标题" />
          </Form.Item>
          
          <Form.Item
            name="description"
            label="作品描述"
            rules={[{ required: true, message: '请输入作品描述' }]}
          >
            <TextArea rows={4} placeholder="请详细描述您的作品..." />
          </Form.Item>
          
          <Form.Item
            name="category"
            label="作品分类"
            rules={[{ required: true, message: '请选择作品分类' }]}
          >
            <Input placeholder="请选择作品分类" />
          </Form.Item>
          
          <Form.Item
            name="tags"
            label="标签"
          >
            <Input placeholder="请输入标签，用逗号分隔" />
          </Form.Item>
          
          <Form.Item
            name="images"
            label="作品图片"
            rules={[{ required: true, message: '请上传作品图片' }]}
          >
            <Upload
              listType="picture-card"
              multiple
              beforeUpload={() => false}
            >
              <div>
                <UploadOutlined />
                <div style={{ marginTop: 8 }}>上传图片</div>
              </div>
            </Upload>
          </Form.Item>
        </Form>
      </Modal>

      {/* 作品详情模态框 */}
      <Modal
        title={selectedWork?.title}
        open={isDetailModalVisible}
        onCancel={() => setIsDetailModalVisible(false)}
        footer={null}
        width={800}
      >
        {selectedWork && (
          <div>
            <Image.PreviewGroup>
              <Row gutter={[8, 8]}>
                {selectedWork.images.map((image, index) => (
                  <Col span={8} key={index}>
                    <Image
                      src={image}
                      alt={`${selectedWork.title} - ${index + 1}`}
                      style={{ width: '100%', height: 150, objectFit: 'cover' }}
                    />
                  </Col>
                ))}
              </Row>
            </Image.PreviewGroup>
            
            <div style={{ marginTop: 16 }}>
              <Space direction="vertical" style={{ width: '100%' }}>
                <div>
                  <Space>
                    <Avatar icon={<UserOutlined />} src={selectedWork.authorAvatar} />
                    <Text strong>{selectedWork.author}</Text>
                    <Text type="secondary">{selectedWork.publishedAt}</Text>
                  </Space>
                </div>
                
                <Paragraph>{selectedWork.description}</Paragraph>
                
                <div>
                  <Space wrap>
                    <Tag color={getCategoryColor(selectedWork.category)}>
                      {getCategoryText(selectedWork.category)}
                    </Tag>
                    {selectedWork.tags.map(tag => (
                      <Tag key={tag}>{tag}</Tag>
                    ))}
                  </Space>
                </div>
                
                <div>
                  <Space>
                    <Rate disabled defaultValue={selectedWork.rating} />
                    <Text>({selectedWork.rating}分)</Text>
                    <Text><LikeOutlined /> {selectedWork.likes}</Text>
                    <Text><EyeOutlined /> {selectedWork.views}</Text>
                  </Space>
                </div>
              </Space>
            </div>
          </div>
        )}
      </Modal>
    </div>
  )
}

export default ContentSpacePage
