import React, { useState, useEffect } from 'react'
import { Card, Row, Col, Typography, Button, Progress, List, Tree, Space, Tag, Divider, Avatar } from 'antd'
import { useParams, useNavigate } from 'react-router-dom'
import {
  BookOutlined,
  PlayCircleOutlined,
  DownloadOutlined,
  FileTextOutlined,
  VideoCameraOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  ArrowLeftOutlined,
} from '@ant-design/icons'
import { ROUTES } from '@/utils/constants'

const { Title, Paragraph, Text } = Typography

interface Lesson {
  id: string
  title: string
  type: 'video' | 'document' | 'quiz'
  duration: number
  completed: boolean
  resources: Resource[]
}

interface Chapter {
  id: string
  title: string
  lessons: Lesson[]
}

interface Resource {
  id: string
  name: string
  type: 'pdf' | 'ppt' | 'doc' | 'video'
  size: string
  url: string
}

interface CourseDetail {
  id: string
  title: string
  description: string
  instructor: string
  instructorAvatar: string
  totalLessons: number
  completedLessons: number
  progress: number
  chapters: Chapter[]
  objectives: string[]
  requirements: string[]
}

const CourseDetailPage: React.FC = () => {
  const { courseId } = useParams<{ courseId: string }>()
  const navigate = useNavigate()
  const [currentLesson, setCurrentLesson] = useState<string | null>(null)

  // 模拟课程数据
  const [courseDetail] = useState<CourseDetail>({
    id: '1',
    title: '会展策划基础',
    description: '本课程系统介绍会展策划的基本理论、方法和实践技能，帮助学生掌握会展项目从立项到执行的全流程管理。',
    instructor: '张教授',
    instructorAvatar: '',
    totalLessons: 12,
    completedLessons: 9,
    progress: 75,
    objectives: [
      '掌握会展策划的基本理论和方法',
      '了解会展行业的发展趋势和特点',
      '学会制定完整的会展策划方案',
      '具备会展项目管理的基本能力'
    ],
    requirements: [
      '具备基本的计算机操作能力',
      '对会展行业有基本了解',
      '具备良好的沟通协调能力'
    ],
    chapters: [
      {
        id: 'chapter1',
        title: '第一章 会展概述',
        lessons: [
          {
            id: 'lesson1',
            title: '1.1 会展的定义与分类',
            type: 'video',
            duration: 30,
            completed: true,
            resources: [
              { id: 'res1', name: '会展概述.pdf', type: 'pdf', size: '2.5MB', url: '#' },
              { id: 'res2', name: '课程PPT.ppt', type: 'ppt', size: '5.2MB', url: '#' }
            ]
          },
          {
            id: 'lesson2',
            title: '1.2 会展行业发展历程',
            type: 'video',
            duration: 25,
            completed: true,
            resources: [
              { id: 'res3', name: '行业发展报告.pdf', type: 'pdf', size: '3.1MB', url: '#' }
            ]
          },
          {
            id: 'lesson3',
            title: '1.3 随堂测验',
            type: 'quiz',
            duration: 15,
            completed: true,
            resources: []
          }
        ]
      },
      {
        id: 'chapter2',
        title: '第二章 会展策划理论',
        lessons: [
          {
            id: 'lesson4',
            title: '2.1 策划理论基础',
            type: 'video',
            duration: 35,
            completed: true,
            resources: [
              { id: 'res4', name: '策划理论.pdf', type: 'pdf', size: '4.2MB', url: '#' }
            ]
          },
          {
            id: 'lesson5',
            title: '2.2 市场调研方法',
            type: 'video',
            duration: 40,
            completed: false,
            resources: [
              { id: 'res5', name: '调研方法.ppt', type: 'ppt', size: '6.8MB', url: '#' }
            ]
          }
        ]
      }
    ]
  })

  const getResourceIcon = (type: string) => {
    const iconMap: Record<string, React.ReactNode> = {
      pdf: <FileTextOutlined style={{ color: '#f5222d' }} />,
      ppt: <FileTextOutlined style={{ color: '#fa8c16' }} />,
      doc: <FileTextOutlined style={{ color: '#1890ff' }} />,
      video: <VideoCameraOutlined style={{ color: '#722ed1' }} />
    }
    return iconMap[type] || <FileTextOutlined />
  }

  const getLessonIcon = (type: string, completed: boolean) => {
    if (completed) {
      return <CheckCircleOutlined style={{ color: '#52c41a' }} />
    }
    
    const iconMap: Record<string, React.ReactNode> = {
      video: <VideoCameraOutlined style={{ color: '#1890ff' }} />,
      document: <FileTextOutlined style={{ color: '#fa8c16' }} />,
      quiz: <EditOutlined style={{ color: '#722ed1' }} />
    }
    return iconMap[type] || <PlayCircleOutlined />
  }

  const handleLessonClick = (lessonId: string) => {
    setCurrentLesson(lessonId)
    // 这里可以实现课程学习逻辑
  }

  const handleResourceDownload = (resource: Resource) => {
    // 实现资源下载逻辑
    console.log('下载资源:', resource.name)
  }

  return (
    <div style={{ padding: '24px' }}>
      {/* 返回按钮 */}
      <Button 
        icon={<ArrowLeftOutlined />} 
        onClick={() => navigate(ROUTES.LEARNING)}
        style={{ marginBottom: 16 }}
      >
        返回课程列表
      </Button>

      <Row gutter={24}>
        {/* 左侧课程目录 */}
        <Col span={8}>
          <Card title="课程目录" style={{ height: 'fit-content' }}>
            <div style={{ marginBottom: 16 }}>
              <Progress 
                percent={courseDetail.progress} 
                strokeColor="#52c41a"
                style={{ marginBottom: 8 }}
              />
              <Text type="secondary">
                已完成 {courseDetail.completedLessons}/{courseDetail.totalLessons} 课时
              </Text>
            </div>
            
            <Divider />
            
            {courseDetail.chapters.map(chapter => (
              <div key={chapter.id} style={{ marginBottom: 16 }}>
                <Title level={5}>{chapter.title}</Title>
                <List
                  size="small"
                  dataSource={chapter.lessons}
                  renderItem={lesson => (
                    <List.Item
                      style={{ 
                        cursor: 'pointer',
                        padding: '8px 0',
                        background: currentLesson === lesson.id ? '#f0f2f5' : 'transparent'
                      }}
                      onClick={() => handleLessonClick(lesson.id)}
                    >
                      <List.Item.Meta
                        avatar={getLessonIcon(lesson.type, lesson.completed)}
                        title={
                          <Space>
                            <Text style={{ fontSize: 14 }}>{lesson.title}</Text>
                            {lesson.completed && (
                              <Tag color="success" size="small">已完成</Tag>
                            )}
                          </Space>
                        }
                        description={
                          <Text type="secondary" style={{ fontSize: 12 }}>
                            <ClockCircleOutlined /> {lesson.duration}分钟
                          </Text>
                        }
                      />
                    </List.Item>
                  )}
                />
              </div>
            ))}
          </Card>
        </Col>

        {/* 右侧课程信息 */}
        <Col span={16}>
          <Card>
            <div style={{ marginBottom: 24 }}>
              <Title level={2}>{courseDetail.title}</Title>
              <Paragraph style={{ fontSize: 16 }}>
                {courseDetail.description}
              </Paragraph>
              
              <Space size="large" style={{ marginTop: 16 }}>
                <div>
                  <Avatar src={courseDetail.instructorAvatar} icon={<UserOutlined />} />
                  <Text style={{ marginLeft: 8 }}>讲师：{courseDetail.instructor}</Text>
                </div>
                <Text type="secondary">
                  <ClockCircleOutlined /> 总课时：{courseDetail.totalLessons}
                </Text>
              </Space>
            </div>

            <Divider />

            <Row gutter={24}>
              <Col span={12}>
                <Title level={4}>学习目标</Title>
                <List
                  size="small"
                  dataSource={courseDetail.objectives}
                  renderItem={objective => (
                    <List.Item>
                      <CheckCircleOutlined style={{ color: '#52c41a', marginRight: 8 }} />
                      {objective}
                    </List.Item>
                  )}
                />
              </Col>
              
              <Col span={12}>
                <Title level={4}>课程要求</Title>
                <List
                  size="small"
                  dataSource={courseDetail.requirements}
                  renderItem={requirement => (
                    <List.Item>
                      <Text type="secondary">• {requirement}</Text>
                    </List.Item>
                  )}
                />
              </Col>
            </Row>

            <Divider />

            {/* 当前课时的资源 */}
            {currentLesson && (
              <div>
                <Title level={4}>课程资源</Title>
                {courseDetail.chapters
                  .flatMap(chapter => chapter.lessons)
                  .find(lesson => lesson.id === currentLesson)
                  ?.resources.map(resource => (
                    <Card 
                      key={resource.id}
                      size="small" 
                      style={{ marginBottom: 8 }}
                      actions={[
                        <Button 
                          type="link" 
                          icon={<DownloadOutlined />}
                          onClick={() => handleResourceDownload(resource)}
                        >
                          下载
                        </Button>
                      ]}
                    >
                      <Card.Meta
                        avatar={getResourceIcon(resource.type)}
                        title={resource.name}
                        description={`文件大小：${resource.size}`}
                      />
                    </Card>
                  ))
                }
              </div>
            )}

            {/* 操作按钮 */}
            <div style={{ marginTop: 24, textAlign: 'center' }}>
              <Space size="large">
                <Button type="primary" size="large" icon={<PlayCircleOutlined />}>
                  开始学习
                </Button>
                <Button size="large">
                  课程讨论
                </Button>
                <Button size="large">
                  学习笔记
                </Button>
              </Space>
            </div>
          </Card>
        </Col>
      </Row>
    </div>
  )
}

export default CourseDetailPage
