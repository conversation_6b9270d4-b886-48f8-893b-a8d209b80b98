<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>施工工序管理 - 会展平台</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
            background-color: #f5f5f5;
            margin: 0;
            padding: 24px;
            line-height: 1.6;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
        }
        .header {
            background: white;
            padding: 24px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 24px;
        }
        .title {
            font-size: 28px;
            color: #1890ff;
            margin-bottom: 8px;
        }
        .subtitle {
            color: #666;
            font-size: 16px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 16px;
            margin-bottom: 24px;
        }
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            text-align: center;
        }
        .stat-number {
            font-size: 32px;
            font-weight: bold;
            margin-bottom: 8px;
        }
        .stat-label {
            color: #666;
            font-size: 14px;
        }
        .main-content {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .tabs {
            display: flex;
            border-bottom: 1px solid #f0f0f0;
        }
        .tab {
            padding: 16px 24px;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            transition: all 0.3s;
        }
        .tab.active {
            color: #1890ff;
            border-bottom-color: #1890ff;
        }
        .tab-content {
            padding: 24px;
            min-height: 500px;
        }
        .task-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 16px;
        }
        .task-table th,
        .task-table td {
            padding: 12px 16px;
            text-align: left;
            border-bottom: 1px solid #f0f0f0;
        }
        .task-table th {
            background: #fafafa;
            font-weight: 600;
            color: #262626;
        }
        .task-table tr:hover {
            background: #f5f5f5;
        }
        .tag {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
            margin-right: 4px;
        }
        .tag-blue { background: #e6f7ff; color: #1890ff; }
        .tag-green { background: #f6ffed; color: #52c41a; }
        .tag-orange { background: #fff7e6; color: #fa8c16; }
        .tag-red { background: #fff2f0; color: #f5222d; }
        .tag-purple { background: #f9f0ff; color: #722ed1; }
        .progress-bar {
            width: 100%;
            height: 8px;
            background: #f5f5f5;
            border-radius: 4px;
            overflow: hidden;
            margin-top: 4px;
        }
        .progress-fill {
            height: 100%;
            background: #52c41a;
            border-radius: 4px;
            transition: width 0.3s ease;
        }
        .gantt-container {
            overflow-x: auto;
            margin-top: 16px;
        }
        .gantt-chart {
            min-width: 800px;
            position: relative;
        }
        .gantt-timeline {
            height: 40px;
            border-bottom: 1px solid #f0f0f0;
            position: relative;
            margin-bottom: 16px;
        }
        .gantt-task {
            margin-bottom: 16px;
            position: relative;
            display: flex;
            align-items: center;
        }
        .gantt-task-name {
            width: 200px;
            padding-right: 16px;
            font-weight: 500;
        }
        .gantt-bar-container {
            flex: 1;
            height: 24px;
            background: #f5f5f5;
            border-radius: 4px;
            position: relative;
            overflow: hidden;
        }
        .gantt-bar {
            height: 100%;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 12px;
            font-weight: 500;
        }
        .charts-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 24px;
            margin-top: 16px;
        }
        .chart-card {
            background: white;
            border: 1px solid #f0f0f0;
            border-radius: 8px;
            padding: 20px;
        }
        .chart-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 16px;
            color: #262626;
        }
        .pie-chart {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            margin: 0 auto 16px;
            position: relative;
        }
        .legend {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        .legend-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        .legend-color {
            width: 12px;
            height: 12px;
            border-radius: 2px;
            margin-right: 8px;
        }
        .filters {
            display: flex;
            gap: 16px;
            margin-bottom: 16px;
            flex-wrap: wrap;
        }
        .filter-select {
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            background: white;
            min-width: 120px;
        }
        .btn {
            padding: 8px 16px;
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            background: white;
            cursor: pointer;
            transition: all 0.3s;
        }
        .btn-primary {
            background: #1890ff;
            color: white;
            border-color: #1890ff;
        }
        .btn:hover {
            border-color: #1890ff;
            color: #1890ff;
        }
        .btn-primary:hover {
            background: #40a9ff;
            border-color: #40a9ff;
        }
        .feature-highlight {
            background: #e6f7ff;
            border: 1px solid #91d5ff;
            border-radius: 6px;
            padding: 16px;
            margin: 16px 0;
        }
        .feature-title {
            font-weight: 600;
            color: #0958d9;
            margin-bottom: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 页面标题 -->
        <div class="header">
            <h1 class="title">🏗️ 施工工序管理</h1>
            <p class="subtitle">
                管理项目施工进度，学生填写工序进展，老师监控项目状态
            </p>
        </div>

        <!-- 功能亮点 -->
        <div class="feature-highlight">
            <div class="feature-title">✨ 新功能亮点</div>
            <p>为项目管理模块新增施工工序管理功能，支持甘特图展示、统计图表分析和详细的任务列表管理，专为职业院校实训教学设计。</p>
        </div>

        <!-- 统计数据卡片 -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number" style="color: #1890ff;">24</div>
                <div class="stat-label">总任务数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" style="color: #52c41a;">65%</div>
                <div class="stat-label">整体进度</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" style="color: #fa8c16;">83%</div>
                <div class="stat-label">按时完成率</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" style="color: #722ed1;">12/15</div>
                <div class="stat-label">参与学生</div>
            </div>
        </div>

        <!-- 主要内容区域 -->
        <div class="main-content">
            <div class="tabs">
                <div class="tab active" onclick="showTab('tasks')">任务列表</div>
                <div class="tab" onclick="showTab('gantt')">甘特图</div>
                <div class="tab" onclick="showTab('charts')">统计图表</div>
            </div>

            <!-- 任务列表标签页 -->
            <div id="tasks-content" class="tab-content">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
                    <h3 style="margin: 0;">任务管理</h3>
                    <button class="btn btn-primary">+ 新建任务</button>
                </div>

                <!-- 筛选条件 -->
                <div class="filters">
                    <select class="filter-select">
                        <option>状态筛选</option>
                        <option>待开始</option>
                        <option>进行中</option>
                        <option>已完成</option>
                        <option>延期</option>
                    </select>
                    <select class="filter-select">
                        <option>优先级筛选</option>
                        <option>紧急</option>
                        <option>高</option>
                        <option>中</option>
                        <option>低</option>
                    </select>
                    <select class="filter-select">
                        <option>工序类型</option>
                        <option>准备</option>
                        <option>结构</option>
                        <option>装饰</option>
                        <option>安装</option>
                        <option>收尾</option>
                        <option>检验</option>
                    </select>
                    <input type="text" class="filter-select" placeholder="搜索任务名称" style="min-width: 200px;">
                </div>

                <!-- 任务表格 -->
                <table class="task-table">
                    <thead>
                        <tr>
                            <th>任务信息</th>
                            <th>状态</th>
                            <th>时间安排</th>
                            <th>分配人员</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>
                                <div style="font-weight: 600; margin-bottom: 4px;">场地清理与准备</div>
                                <div style="font-size: 12px; color: #666; margin-bottom: 4px;">
                                    清理施工现场，设置安全围挡，准备施工材料
                                </div>
                                <span class="tag tag-red">高</span>
                                <span class="tag tag-blue">准备</span>
                            </td>
                            <td>
                                <div style="margin-bottom: 4px;">
                                    <span class="tag tag-green">已完成</span>
                                </div>
                                <div class="progress-bar">
                                    <div class="progress-fill" style="width: 100%;"></div>
                                </div>
                                <div style="font-size: 12px; margin-top: 2px;">100%</div>
                            </td>
                            <td>
                                <div style="font-size: 12px;">计划：2024-01-15 ~ 2024-01-17</div>
                                <div style="font-size: 12px; color: #666;">实际：2024-01-15 ~ 2024-01-17</div>
                                <div style="font-size: 12px; color: #666;">工期：2天 (实际2天)</div>
                            </td>
                            <td>
                                <span class="tag tag-blue">学生1</span>
                                <span class="tag tag-blue">学生2</span>
                            </td>
                            <td>
                                <button class="btn" style="margin-right: 8px;">查看</button>
                                <button class="btn">编辑</button>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <div style="font-weight: 600; margin-bottom: 4px;">基础结构搭建</div>
                                <div style="font-size: 12px; color: #666; margin-bottom: 4px;">
                                    搭建展台主体框架结构
                                </div>
                                <span class="tag tag-red">高</span>
                                <span class="tag tag-green">结构</span>
                            </td>
                            <td>
                                <div style="margin-bottom: 4px;">
                                    <span class="tag tag-orange">进行中</span>
                                </div>
                                <div class="progress-bar">
                                    <div class="progress-fill" style="width: 75%; background: #fa8c16;"></div>
                                </div>
                                <div style="font-size: 12px; margin-top: 2px;">75%</div>
                            </td>
                            <td>
                                <div style="font-size: 12px;">计划：2024-01-18 ~ 2024-01-22</div>
                                <div style="font-size: 12px; color: #666;">实际：2024-01-18 ~ 进行中</div>
                                <div style="font-size: 12px; color: #666;">工期：4天</div>
                            </td>
                            <td>
                                <span class="tag tag-blue">学生3</span>
                                <span class="tag tag-blue">学生4</span>
                                <span class="tag tag-blue">学生5</span>
                            </td>
                            <td>
                                <button class="btn" style="margin-right: 8px;">查看</button>
                                <button class="btn">编辑</button>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <div style="font-weight: 600; margin-bottom: 4px;">电路安装</div>
                                <div style="font-size: 12px; color: #666; margin-bottom: 4px;">
                                    安装展台照明和电源系统
                                </div>
                                <span class="tag tag-orange">中</span>
                                <span class="tag tag-purple">安装</span>
                            </td>
                            <td>
                                <div style="margin-bottom: 4px;">
                                    <span class="tag" style="background: #f5f5f5; color: #666;">待开始</span>
                                </div>
                                <div class="progress-bar">
                                    <div class="progress-fill" style="width: 0%;"></div>
                                </div>
                                <div style="font-size: 12px; margin-top: 2px;">0%</div>
                            </td>
                            <td>
                                <div style="font-size: 12px;">计划：2024-01-23 ~ 2024-01-25</div>
                                <div style="font-size: 12px; color: #666;">等待前置任务完成</div>
                                <div style="font-size: 12px; color: #666;">工期：2天</div>
                            </td>
                            <td>
                                <span class="tag tag-blue">学生6</span>
                                <span class="tag tag-blue">学生7</span>
                            </td>
                            <td>
                                <button class="btn" style="margin-right: 8px;">查看</button>
                                <button class="btn">编辑</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- 甘特图标签页 -->
            <div id="gantt-content" class="tab-content" style="display: none;">
                <h3 style="margin-bottom: 16px;">项目甘特图</h3>

                <div class="gantt-container">
                    <div class="gantt-chart">
                        <!-- 时间轴 -->
                        <div class="gantt-timeline">
                            <div style="position: absolute; left: 0%; top: 0; height: 100%; border-left: 1px solid #f0f0f0; padding-left: 4px; font-size: 12px; color: #666; display: flex; align-items: center;">1月15日</div>
                            <div style="position: absolute; left: 20%; top: 0; height: 100%; border-left: 1px solid #f0f0f0; padding-left: 4px; font-size: 12px; color: #666; display: flex; align-items: center;">1月20日</div>
                            <div style="position: absolute; left: 40%; top: 0; height: 100%; border-left: 1px solid #f0f0f0; padding-left: 4px; font-size: 12px; color: #666; display: flex; align-items: center;">1月25日</div>
                            <div style="position: absolute; left: 60%; top: 0; height: 100%; border-left: 1px solid #f0f0f0; padding-left: 4px; font-size: 12px; color: #666; display: flex; align-items: center;">1月30日</div>
                            <div style="position: absolute; left: 80%; top: 0; height: 100%; border-left: 1px solid #f0f0f0; padding-left: 4px; font-size: 12px; color: #666; display: flex; align-items: center;">2月4日</div>
                        </div>

                        <!-- 甘特条 -->
                        <div class="gantt-task">
                            <div class="gantt-task-name">
                                <div style="font-weight: 500;">场地清理与准备</div>
                                <div style="font-size: 12px; color: #666;">
                                    <span class="tag tag-blue">准备</span>
                                    <span class="tag tag-green">已完成</span>
                                </div>
                            </div>
                            <div class="gantt-bar-container">
                                <div class="gantt-bar" style="position: absolute; left: 0%; width: 10%; background: #52c41a;">
                                    100%
                                </div>
                            </div>
                            <div style="width: 60px; text-align: center; padding-left: 8px;">
                                <div style="width: 32px; height: 32px; border-radius: 50%; background: #52c41a; color: white; display: flex; align-items: center; justify-content: center; font-size: 10px; margin: 0 auto;">100%</div>
                            </div>
                        </div>

                        <div class="gantt-task">
                            <div class="gantt-task-name">
                                <div style="font-weight: 500;">基础结构搭建</div>
                                <div style="font-size: 12px; color: #666;">
                                    <span class="tag tag-green">结构</span>
                                    <span class="tag tag-orange">进行中</span>
                                </div>
                            </div>
                            <div class="gantt-bar-container">
                                <div class="gantt-bar" style="position: absolute; left: 15%; width: 20%; background: #fa8c16;">
                                    75%
                                </div>
                                <div style="position: absolute; left: 15%; width: 15%; height: 100%; background: #52c41a; border-radius: 4px; opacity: 0.8;"></div>
                            </div>
                            <div style="width: 60px; text-align: center; padding-left: 8px;">
                                <div style="width: 32px; height: 32px; border-radius: 50%; background: #fa8c16; color: white; display: flex; align-items: center; justify-content: center; font-size: 10px; margin: 0 auto;">75%</div>
                            </div>
                        </div>

                        <div class="gantt-task">
                            <div class="gantt-task-name">
                                <div style="font-weight: 500;">电路安装</div>
                                <div style="font-size: 12px; color: #666;">
                                    <span class="tag tag-purple">安装</span>
                                    <span class="tag" style="background: #f5f5f5; color: #666;">待开始</span>
                                </div>
                            </div>
                            <div class="gantt-bar-container">
                                <div class="gantt-bar" style="position: absolute; left: 40%; width: 10%; background: #d9d9d9; color: #666;">
                                    0%
                                </div>
                            </div>
                            <div style="width: 60px; text-align: center; padding-left: 8px;">
                                <div style="width: 32px; height: 32px; border-radius: 50%; background: #d9d9d9; color: #666; display: flex; align-items: center; justify-content: center; font-size: 10px; margin: 0 auto;">0%</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 图例 -->
                <div style="margin-top: 24px; padding: 16px; background: #fafafa; border-radius: 6px;">
                    <div style="margin-bottom: 8px; font-weight: 500;">图例说明：</div>
                    <div style="display: flex; flex-wrap: wrap; gap: 16px;">
                        <div style="display: flex; align-items: center;">
                            <div style="width: 16px; height: 16px; background: #d9d9d9; margin-right: 8px; border-radius: 2px;"></div>
                            <span style="font-size: 12px;">待开始</span>
                        </div>
                        <div style="display: flex; align-items: center;">
                            <div style="width: 16px; height: 16px; background: #fa8c16; margin-right: 8px; border-radius: 2px;"></div>
                            <span style="font-size: 12px;">进行中</span>
                        </div>
                        <div style="display: flex; align-items: center;">
                            <div style="width: 16px; height: 16px; background: #52c41a; margin-right: 8px; border-radius: 2px;"></div>
                            <span style="font-size: 12px;">已完成</span>
                        </div>
                        <div style="display: flex; align-items: center;">
                            <div style="width: 16px; height: 16px; background: #f5222d; margin-right: 8px; border-radius: 2px;"></div>
                            <span style="font-size: 12px;">延期</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 统计图表标签页 -->
            <div id="charts-content" class="tab-content" style="display: none;">
                <h3 style="margin-bottom: 16px;">项目统计图表</h3>

                <div class="charts-grid">
                    <!-- 任务状态分布 -->
                    <div class="chart-card">
                        <div class="chart-title">任务状态分布</div>
                        <div class="pie-chart" style="background: conic-gradient(#52c41a 0deg 120deg, #fa8c16 120deg 240deg, #d9d9d9 240deg 300deg, #f5222d 300deg 360deg);"></div>
                        <div class="legend">
                            <div class="legend-item">
                                <div style="display: flex; align-items: center;">
                                    <div class="legend-color" style="background: #52c41a;"></div>
                                    <span style="font-size: 12px;">已完成</span>
                                </div>
                                <div style="font-size: 12px; color: #666;">8 (33.3%)</div>
                            </div>
                            <div class="legend-item">
                                <div style="display: flex; align-items: center;">
                                    <div class="legend-color" style="background: #fa8c16;"></div>
                                    <span style="font-size: 12px;">进行中</span>
                                </div>
                                <div style="font-size: 12px; color: #666;">12 (50.0%)</div>
                            </div>
                            <div class="legend-item">
                                <div style="display: flex; align-items: center;">
                                    <div class="legend-color" style="background: #d9d9d9;"></div>
                                    <span style="font-size: 12px;">待开始</span>
                                </div>
                                <div style="font-size: 12px; color: #666;">3 (12.5%)</div>
                            </div>
                            <div class="legend-item">
                                <div style="display: flex; align-items: center;">
                                    <div class="legend-color" style="background: #f5222d;"></div>
                                    <span style="font-size: 12px;">延期</span>
                                </div>
                                <div style="font-size: 12px; color: #666;">1 (4.2%)</div>
                            </div>
                        </div>
                    </div>

                    <!-- 工序类型分布 -->
                    <div class="chart-card">
                        <div class="chart-title">工序类型分布</div>
                        <div class="pie-chart" style="background: conic-gradient(#1890ff 0deg 60deg, #52c41a 60deg 120deg, #fa8c16 120deg 180deg, #722ed1 180deg 240deg, #13c2c2 240deg 300deg, #eb2f96 300deg 360deg);"></div>
                        <div class="legend">
                            <div class="legend-item">
                                <div style="display: flex; align-items: center;">
                                    <div class="legend-color" style="background: #1890ff;"></div>
                                    <span style="font-size: 12px;">准备</span>
                                </div>
                                <div style="font-size: 12px; color: #666;">4 (16.7%)</div>
                            </div>
                            <div class="legend-item">
                                <div style="display: flex; align-items: center;">
                                    <div class="legend-color" style="background: #52c41a;"></div>
                                    <span style="font-size: 12px;">结构</span>
                                </div>
                                <div style="font-size: 12px; color: #666;">6 (25.0%)</div>
                            </div>
                            <div class="legend-item">
                                <div style="display: flex; align-items: center;">
                                    <div class="legend-color" style="background: #fa8c16;"></div>
                                    <span style="font-size: 12px;">装饰</span>
                                </div>
                                <div style="font-size: 12px; color: #666;">5 (20.8%)</div>
                            </div>
                            <div class="legend-item">
                                <div style="display: flex; align-items: center;">
                                    <div class="legend-color" style="background: #722ed1;"></div>
                                    <span style="font-size: 12px;">安装</span>
                                </div>
                                <div style="font-size: 12px; color: #666;">4 (16.7%)</div>
                            </div>
                        </div>
                    </div>

                    <!-- 优先级分布 -->
                    <div class="chart-card">
                        <div class="chart-title">优先级分布</div>
                        <div style="padding: 16px 0;">
                            <div style="margin-bottom: 16px;">
                                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 4px;">
                                    <span style="font-size: 12px; font-weight: 500;">紧急</span>
                                    <span style="font-size: 12px; color: #666;">2 (8.3%)</span>
                                </div>
                                <div style="width: 100%; height: 8px; background: #f5f5f5; border-radius: 4px; overflow: hidden;">
                                    <div style="width: 20%; height: 100%; background: #722ed1; border-radius: 4px;"></div>
                                </div>
                            </div>
                            <div style="margin-bottom: 16px;">
                                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 4px;">
                                    <span style="font-size: 12px; font-weight: 500;">高</span>
                                    <span style="font-size: 12px; color: #666;">8 (33.3%)</span>
                                </div>
                                <div style="width: 100%; height: 8px; background: #f5f5f5; border-radius: 4px; overflow: hidden;">
                                    <div style="width: 80%; height: 100%; background: #f5222d; border-radius: 4px;"></div>
                                </div>
                            </div>
                            <div style="margin-bottom: 16px;">
                                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 4px;">
                                    <span style="font-size: 12px; font-weight: 500;">中</span>
                                    <span style="font-size: 12px; color: #666;">10 (41.7%)</span>
                                </div>
                                <div style="width: 100%; height: 8px; background: #f5f5f5; border-radius: 4px; overflow: hidden;">
                                    <div style="width: 100%; height: 100%; background: #fa8c16; border-radius: 4px;"></div>
                                </div>
                            </div>
                            <div>
                                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 4px;">
                                    <span style="font-size: 12px; font-weight: 500;">低</span>
                                    <span style="font-size: 12px; color: #666;">4 (16.7%)</span>
                                </div>
                                <div style="width: 100%; height: 8px; background: #f5f5f5; border-radius: 4px; overflow: hidden;">
                                    <div style="width: 40%; height: 100%; background: #52c41a; border-radius: 4px;"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 进度分布 -->
                    <div class="chart-card">
                        <div class="chart-title">进度分布</div>
                        <div style="padding: 16px 0;">
                            <div style="margin-bottom: 16px;">
                                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 4px;">
                                    <span style="font-size: 12px; font-weight: 500;">0-20%</span>
                                    <span style="font-size: 12px; color: #666;">3 (12.5%)</span>
                                </div>
                                <div style="width: 100%; height: 8px; background: #f5f5f5; border-radius: 4px; overflow: hidden;">
                                    <div style="width: 25%; height: 100%; background: #f5222d; border-radius: 4px;"></div>
                                </div>
                            </div>
                            <div style="margin-bottom: 16px;">
                                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 4px;">
                                    <span style="font-size: 12px; font-weight: 500;">21-40%</span>
                                    <span style="font-size: 12px; color: #666;">4 (16.7%)</span>
                                </div>
                                <div style="width: 100%; height: 8px; background: #f5f5f5; border-radius: 4px; overflow: hidden;">
                                    <div style="width: 33%; height: 100%; background: #fa8c16; border-radius: 4px;"></div>
                                </div>
                            </div>
                            <div style="margin-bottom: 16px;">
                                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 4px;">
                                    <span style="font-size: 12px; font-weight: 500;">41-60%</span>
                                    <span style="font-size: 12px; color: #666;">5 (20.8%)</span>
                                </div>
                                <div style="width: 100%; height: 8px; background: #f5f5f5; border-radius: 4px; overflow: hidden;">
                                    <div style="width: 42%; height: 100%; background: #fadb14; border-radius: 4px;"></div>
                                </div>
                            </div>
                            <div style="margin-bottom: 16px;">
                                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 4px;">
                                    <span style="font-size: 12px; font-weight: 500;">61-80%</span>
                                    <span style="font-size: 12px; color: #666;">4 (16.7%)</span>
                                </div>
                                <div style="width: 100%; height: 8px; background: #f5f5f5; border-radius: 4px; overflow: hidden;">
                                    <div style="width: 33%; height: 100%; background: #a0d911; border-radius: 4px;"></div>
                                </div>
                            </div>
                            <div>
                                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 4px;">
                                    <span style="font-size: 12px; font-weight: 500;">81-100%</span>
                                    <span style="font-size: 12px; color: #666;">8 (33.3%)</span>
                                </div>
                                <div style="width: 100%; height: 8px; background: #f5f5f5; border-radius: 4px; overflow: hidden;">
                                    <div style="width: 67%; height: 100%; background: #52c41a; border-radius: 4px;"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 项目概览 -->
                <div style="margin-top: 24px;">
                    <div class="chart-card">
                        <div class="chart-title">项目概览</div>
                        <div style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 16px; text-align: center;">
                            <div>
                                <div style="font-size: 24px; font-weight: bold; color: #1890ff;">24</div>
                                <div style="color: #666;">总任务数</div>
                            </div>
                            <div>
                                <div style="font-size: 24px; font-weight: bold; color: #52c41a;">65%</div>
                                <div style="color: #666;">整体进度</div>
                                <div class="progress-bar" style="margin-top: 8px;">
                                    <div class="progress-fill" style="width: 65%;"></div>
                                </div>
                            </div>
                            <div>
                                <div style="font-size: 24px; font-weight: bold; color: #fa8c16;">83%</div>
                                <div style="color: #666;">按时完成率</div>
                            </div>
                            <div>
                                <div style="font-size: 24px; font-weight: bold; color: #722ed1;">12/15</div>
                                <div style="color: #666;">参与学生</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 功能说明 -->
        <div style="background: white; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); padding: 24px; margin-top: 24px;">
            <h3 style="color: #1890ff; margin-bottom: 16px;">🎓 教学应用场景</h3>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">
                <div>
                    <h4 style="color: #52c41a;">👥 学生端功能</h4>
                    <ul style="color: #666; margin: 0; padding-left: 20px;">
                        <li>查看分配给自己的任务</li>
                        <li>更新任务进度和状态</li>
                        <li>填写工作日志和问题反馈</li>
                        <li>上传施工过程照片</li>
                        <li>查看项目整体进度</li>
                    </ul>
                </div>
                <div>
                    <h4 style="color: #fa8c16;">👨‍🏫 教师端功能</h4>
                    <ul style="color: #666; margin: 0; padding-left: 20px;">
                        <li>创建和分配施工任务</li>
                        <li>监控项目整体进度</li>
                        <li>审核学生提交的进度</li>
                        <li>查看统计分析报告</li>
                        <li>调整任务安排和时间线</li>
                    </ul>
                </div>
                <div>
                    <h4 style="color: #722ed1;">📊 数据分析</h4>
                    <ul style="color: #666; margin: 0; padding-left: 20px;">
                        <li>甘特图展示时间线</li>
                        <li>任务状态分布统计</li>
                        <li>工序类型分析</li>
                        <li>学生参与度统计</li>
                        <li>项目完成率分析</li>
                    </ul>
                </div>
                <div>
                    <h4 style="color: #13c2c2;">🎯 教学价值</h4>
                    <ul style="color: #666; margin: 0; padding-left: 20px;">
                        <li>培养项目管理能力</li>
                        <li>提升团队协作意识</li>
                        <li>学习施工工序规范</li>
                        <li>掌握进度控制方法</li>
                        <li>增强责任心和执行力</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 总结 -->
        <div style="background: #f6ffed; border: 1px solid #b7eb8f; border-radius: 6px; padding: 20px; margin-top: 24px; text-align: center;">
            <h3 style="color: #389e0d; margin-bottom: 12px;">✅ 施工工序管理功能开发完成</h3>
            <p style="margin: 0; color: #666;">
                成功为项目管理模块增加了施工工序管理功能，包括任务列表管理、甘特图展示、统计图表分析等完整功能。
                支持学生填写进度、老师监控项目，完全适合职业院校实训教学使用。
            </p>
        </div>
    </div>

    <script>
        function showTab(tabName) {
            // 隐藏所有标签页内容
            document.getElementById('tasks-content').style.display = 'none';
            document.getElementById('gantt-content').style.display = 'none';
            document.getElementById('charts-content').style.display = 'none';

            // 移除所有标签的active类
            const tabs = document.querySelectorAll('.tab');
            tabs.forEach(tab => tab.classList.remove('active'));

            // 显示选中的标签页内容
            document.getElementById(tabName + '-content').style.display = 'block';

            // 添加active类到选中的标签
            event.target.classList.add('active');
        }
    </script>
</body>
</html>