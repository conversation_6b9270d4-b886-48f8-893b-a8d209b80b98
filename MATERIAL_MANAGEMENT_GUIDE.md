# 🏗️ 材料管理系统 - 与3D编辑器联动成本计算

## 🎯 功能概述

材料管理系统是项目管理平台的重要组成部分，主要用于：
- 维护材料库和价格信息
- 与3D编辑器联动实现自动成本计算
- 提供实训项目的材料报价和成本分析
- 支持材料使用记录和成本追踪

## 📋 核心功能

### 1. 材料库管理
- **材料信息维护**：名称、分类、品牌、型号、规格
- **价格管理**：单价、单位、最小起订量
- **库存管理**：库存数量、交货周期
- **供应商信息**：供应商名称、联系方式
- **使用统计**：使用次数、总成本计算

### 2. 3D编辑器联动
- **素材关联**：3D素材与材料库关联
- **自动计算**：使用素材时自动计算成本
- **实时统计**：实时显示材料使用和成本
- **成本面板**：专门的成本计算界面

### 3. 成本分析
- **使用记录**：详细的材料使用历史
- **成本报告**：可导出的成本分析报告
- **趋势分析**：材料使用和成本趋势

## 🚀 使用流程演示

### 步骤1：材料库维护
**访问路径：** `/projects/materials`

**操作步骤：**
1. 点击"添加材料"按钮
2. 填写材料基本信息：
   - 材料名称：铝合金展台桁架
   - 分类：结构件
   - 品牌：展通
   - 型号：ZT-300
   - 规格：300x300x3000mm
   - 单价：¥280/件
   - 库存：150件
   - 供应商：北京展通展具有限公司
3. 保存材料信息

**预期结果：**
- ✅ 材料成功添加到材料库
- ✅ 材料信息在表格中显示
- ✅ 统计数据自动更新

### 步骤2：3D编辑器中使用材料
**访问路径：** `/design-3d`

**操作步骤：**
1. 打开3D编辑器
2. 查看底部素材库
3. 找到带有绿色💰标识的素材（已关联材料库）
4. 点击"展台桁架"素材
5. 观察成本计算按钮变化

**预期结果：**
- ✅ 素材添加到3D场景
- ✅ 成本计算按钮显示总成本
- ✅ 自动记录材料使用

### 步骤3：查看成本计算
**在3D编辑器中：**

**操作步骤：**
1. 点击顶部"成本计算"按钮
2. 查看右侧成本面板
3. 查看材料明细表格
4. 点击"导出报告"按钮

**预期结果：**
- ✅ 显示材料总数和总成本
- ✅ 详细的材料使用明细
- ✅ 可以删除不需要的材料
- ✅ 导出成本报告

### 步骤4：项目中查看材料成本
**访问路径：** `/projects` → 选择项目 → 材料清单标签页

**预期结果：**
- ✅ 显示项目材料统计
- ✅ 材料列表包含3D编辑器使用的材料
- ✅ 显示预估总成本
- ✅ 标识材料来源（3D编辑器）

## 🎨 界面功能详解

### 材料管理页面功能
**路径：** `/projects/materials`

#### 📊 统计仪表板
- **材料总数**：当前材料库中的材料种类
- **总库存价值**：所有材料的库存总价值
- **本月使用成本**：当月通过3D编辑器使用的材料成本
- **低库存材料**：库存不足的材料数量

#### 📋 材料库表格
- **材料信息**：名称、品牌、型号、规格
- **分类标签**：结构件、装饰材料、照明设备等
- **价格信息**：单价、最小起订量
- **库存状态**：库存数量、交货周期（颜色标识）
- **使用情况**：使用次数、总成本
- **供应商信息**：供应商名称、联系方式
- **状态标识**：启用/停用

#### 🔍 搜索和筛选
- **搜索框**：支持材料名称、品牌、型号搜索
- **分类筛选**：按材料分类过滤
- **导入导出**：批量导入材料、导出材料库

### 3D编辑器成本功能
**路径：** `/design-3d`

#### 💰 素材价格标识
- **绿色边框**：已关联材料库的素材
- **💰图标**：价格标识
- **价格显示**：单价和单位
- **点击添加**：点击素材自动计算成本

#### 📊 成本计算面板
- **统计卡片**：材料总数、总成本
- **材料明细表格**：
  - 材料名称
  - 使用数量
  - 单价
  - 小计
  - 删除操作
- **导出功能**：导出详细成本报告
- **说明信息**：使用指南和功能说明

## 🔄 数据流转

### 材料库 → 3D编辑器
1. 材料库维护材料信息和价格
2. 3D素材关联材料库ID
3. 素材显示价格标识
4. 点击素材触发成本计算

### 3D编辑器 → 项目管理
1. 3D编辑器记录材料使用
2. 生成材料使用记录
3. 同步到项目材料清单
4. 更新项目成本统计

### 成本计算逻辑
```
使用材料时：
1. 获取材料ID和单价
2. 计算数量 × 单价 = 小计
3. 累加所有材料小计 = 总成本
4. 记录使用时间和项目信息
5. 同步到项目管理平台
```

## 📈 应用场景

### 实训教学场景
1. **教师准备**：
   - 维护常用材料库
   - 设置材料价格和供应商信息
   - 创建实训项目

2. **学生设计**：
   - 在3D编辑器中进行展位设计
   - 选择合适的材料和素材
   - 实时查看设计成本

3. **成本控制**：
   - 根据预算限制调整设计
   - 比较不同材料的成本
   - 优化设计方案

4. **项目管理**：
   - 查看项目总成本
   - 分析材料使用情况
   - 生成成本报告

### 企业项目场景
1. **方案报价**：
   - 基于材料库快速生成报价
   - 准确计算项目成本
   - 提供详细的材料清单

2. **成本控制**：
   - 实时监控项目成本
   - 预警超预算情况
   - 优化材料选择

## ✅ 验证清单

### 材料管理功能
- [ ] 材料库页面正常显示
- [ ] 添加/编辑材料功能正常
- [ ] 搜索和筛选功能正常
- [ ] 统计数据正确计算
- [ ] 导入导出功能正常

### 3D编辑器联动
- [ ] 素材显示价格标识
- [ ] 点击素材自动计算成本
- [ ] 成本面板正常显示
- [ ] 材料明细表格正确
- [ ] 导出报告功能正常

### 项目管理集成
- [ ] 项目材料清单显示3D使用的材料
- [ ] 成本统计正确
- [ ] 材料来源标识正确
- [ ] 跳转到材料库管理正常

## 🎉 预期效果

通过材料管理系统的实施，实现：

1. **教学效果提升**：
   - 学生了解真实的材料成本
   - 培养成本控制意识
   - 提高设计的实用性

2. **管理效率提升**：
   - 自动化成本计算
   - 减少人工统计错误
   - 提高项目管理效率

3. **决策支持**：
   - 基于真实数据的设计决策
   - 成本对比和优化建议
   - 项目可行性分析

---

**下一步扩展：**
- 集成真实的材料供应商API
- 添加材料价格波动监控
- 实现智能材料推荐
- 支持多币种和汇率转换
