import React, { useState, useEffect } from 'react'
import { Card, Row, Col, <PERSON>po<PERSON>, Button, Table, Tag, Space, Modal, Form, Input, Select, DatePicker, Tabs, Statistic, Divider, Avatar, Upload, message } from 'antd'
import { useNavigate, useParams } from 'react-router-dom'
import {
  ProjectOutlined,
  PlusOutlined,
  EditOutlined,
  EyeOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  ExclamationCircleOutlined,
  UserOutlined,
  CalendarOutlined,
  FileTextOutlined,
  SearchOutlined,
  FilterOutlined,
  TeamOutlined,
  FolderOutlined,
  UploadOutlined,
  DownloadOutlined,
  AuditOutlined,
} from '@ant-design/icons'
import { useAuthStore } from '@/store/authStore'
import { ROUTES } from '@/utils/constants'
import type { ColumnsType } from 'antd/es/table'

const { Title, Paragraph, Text } = Typography
const { Option } = Select
const { TextArea } = Input

interface ProjectItem {
  id: string
  projectNumber: string
  name: string
  description: string
  type: 'exhibition' | 'conference' | 'trade_show'
  status: 'planning' | 'design' | 'construction' | 'completed' | 'cancelled'
  priority: 'high' | 'medium' | 'low'
  createdBy: string
  assignedTo: string[]
  startDate: string
  endDate: string
  progress: number
  budget?: number
  location?: string
  members: ProjectMember[]
  proposals: Proposal[]
  materials: Material[]
  files: ProjectFile[]
}

interface ProjectMember {
  id: string
  name: string
  role: string
  avatar?: string
  joinDate: string
}

interface Proposal {
  id: string
  projectId: string
  projectName: string
  submittedBy: string
  submittedAt: string
  status: 'pending' | 'approved' | 'rejected'
  description: string
  estimatedBudget: number
  estimatedDuration: number
  reviewComments?: string
  reviewedBy?: string
  reviewedAt?: string
  attachments: string[]
}

interface Material {
  id: string
  name: string
  category: string
  quantity: number
  unit: string
  estimatedCost: number
  supplier?: string
}

interface ProjectFile {
  id: string
  name: string
  type: string
  size: number
  uploadedBy: string
  uploadedAt: string
  url: string
}

const ProjectManagementPage: React.FC = () => {
  const navigate = useNavigate()
  const { user } = useAuthStore()
  const { projectId } = useParams()
  const [currentView, setCurrentView] = useState<'list' | 'detail' | 'approval'>('list')
  const [selectedProject, setSelectedProject] = useState<ProjectItem | null>(null)
  const [selectedProposal, setSelectedProposal] = useState<Proposal | null>(null)
  const [isProposalDetailVisible, setIsProposalDetailVisible] = useState(false)
  const [activeTab, setActiveTab] = useState('overview')
  const [isCreateModalVisible, setIsCreateModalVisible] = useState(false)
  const [isProposalModalVisible, setIsProposalModalVisible] = useState(false)
  const [searchText, setSearchText] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('')
  const [form] = Form.useForm()
  const [proposalForm] = Form.useForm()

  // 模拟项目数据
  const [projects] = useState<ProjectItem[]>([
    {
      id: '1',
      projectNumber: 'PROJ-2024-001',
      name: '2024春季汽车展',
      description: '大型汽车展览会，展示最新汽车技术和产品',
      type: 'exhibition',
      status: 'design',
      priority: 'high',
      createdBy: '张老师',
      assignedTo: ['学生A', '学生B'],
      startDate: '2024-02-01',
      endDate: '2024-02-15',
      progress: 65,
      budget: 500000,
      location: '国际会展中心',
      members: [
        { id: '1', name: '张老师', role: '项目经理', joinDate: '2024-01-15' },
        { id: '2', name: '学生A', role: '主设计师', joinDate: '2024-01-16' },
        { id: '3', name: '学生B', role: '助理设计师', joinDate: '2024-01-16' }
      ],
      proposals: [
        {
          id: '1',
          projectId: '1',
          projectName: '汽车展主展区设计方案',
          submittedBy: '学生A',
          submittedAt: '2024-01-20',
          status: 'approved',
          description: '主展区采用现代化设计理念，突出科技感',
          estimatedBudget: 200000,
          estimatedDuration: 10,
          reviewComments: '设计理念很好，建议增加互动元素',
          reviewedBy: '张老师',
          reviewedAt: '2024-01-21',
          attachments: ['design_v1.pdf', 'render_01.jpg']
        }
      ],
      materials: [
        { id: '1', name: '展台桁架', category: '结构件', quantity: 20, unit: '套', estimatedCost: 50000, supplier: 'ABC展具公司' },
        { id: '2', name: 'LED显示屏', category: '电子设备', quantity: 5, unit: '块', estimatedCost: 80000, supplier: 'XYZ科技' }
      ],
      files: [
        { id: '1', name: '项目合同.pdf', type: 'pdf', size: 2048, uploadedBy: '张老师', uploadedAt: '2024-01-15', url: '/files/contract.pdf' },
        { id: '2', name: '需求文档.docx', type: 'docx', size: 1024, uploadedBy: '张老师', uploadedAt: '2024-01-16', url: '/files/requirements.docx' }
      ]
    },
    {
      id: '2',
      projectNumber: 'PROJ-2024-002',
      name: '科技创新大会',
      description: '展示最新科技创新成果的会议展览',
      type: 'conference',
      status: 'planning',
      priority: 'medium',
      createdBy: '李老师',
      assignedTo: ['学生C', '学生D'],
      startDate: '2024-03-01',
      endDate: '2024-03-10',
      progress: 30,
      budget: 300000,
      location: '科技园会议中心',
      members: [
        { id: '4', name: '李老师', role: '项目经理', joinDate: '2024-01-10' },
        { id: '5', name: '学生C', role: '设计师', joinDate: '2024-01-12' },
        { id: '6', name: '学生D', role: '设计师', joinDate: '2024-01-12' }
      ],
      proposals: [],
      materials: [],
      files: []
    }
  ])

  const [proposals] = useState<Proposal[]>([
    {
      id: '1',
      projectName: '绿色环保主题展',
      submittedBy: '学生E',
      submittedAt: '2024-01-20',
      status: 'pending',
      description: '以环保为主题的展览活动，展示绿色科技和环保产品',
      estimatedBudget: 200000,
      estimatedDuration: 10
    },
    {
      id: '2',
      projectName: '文化艺术节',
      submittedBy: '学生F',
      submittedAt: '2024-01-18',
      status: 'approved',
      description: '展示传统文化和现代艺术的综合性文化活动',
      estimatedBudget: 150000,
      estimatedDuration: 7
    }
  ])

  const getStatusColor = (status: string) => {
    const colorMap: Record<string, string> = {
      planning: 'blue',
      design: 'orange',
      construction: 'purple',
      completed: 'green',
      cancelled: 'red',
      pending: 'gold',
      approved: 'green',
      rejected: 'red'
    }
    return colorMap[status] || 'default'
  }

  const getStatusText = (status: string) => {
    const textMap: Record<string, string> = {
      planning: '规划中',
      design: '设计中',
      construction: '施工中',
      completed: '已完成',
      cancelled: '已取消',
      pending: '待审批',
      approved: '已通过',
      rejected: '已驳回'
    }
    return textMap[status] || status
  }

  const getPriorityColor = (priority: string) => {
    const colorMap: Record<string, string> = {
      high: 'red',
      medium: 'orange',
      low: 'green'
    }
    return colorMap[priority] || 'default'
  }

  const getPriorityText = (priority: string) => {
    const textMap: Record<string, string> = {
      high: '高',
      medium: '中',
      low: '低'
    }
    return textMap[priority] || priority
  }

  const projectColumns: ColumnsType<ProjectItem> = [
    {
      title: '项目信息',
      key: 'projectInfo',
      render: (_, record) => (
        <Space direction="vertical" size={0}>
          <Text strong style={{ cursor: 'pointer' }} onClick={() => handleViewProject(record)}>
            {record.name}
          </Text>
          <Text type="secondary" style={{ fontSize: 12 }}>
            {record.projectNumber}
          </Text>
          <Text type="secondary" style={{ fontSize: 12 }} ellipsis>
            {record.description}
          </Text>
        </Space>
      ),
      width: 250
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <Tag color={getStatusColor(status)}>
          {getStatusText(status)}
        </Tag>
      ),
      filters: [
        { text: '规划中', value: 'planning' },
        { text: '设计中', value: 'design' },
        { text: '施工中', value: 'construction' },
        { text: '已完成', value: 'completed' },
        { text: '已取消', value: 'cancelled' }
      ],
      onFilter: (value, record) => record.status === value
    },
    {
      title: '负责人',
      dataIndex: 'assignedTo',
      key: 'assignedTo',
      render: (assignedTo) => (
        <Avatar.Group maxCount={3} size="small">
          {assignedTo.map((person, index) => (
            <Avatar key={index} size="small">
              {person.charAt(0)}
            </Avatar>
          ))}
        </Avatar.Group>
      )
    },
    {
      title: '关键日期',
      key: 'dates',
      render: (_, record) => (
        <Space direction="vertical" size={0}>
          <Text style={{ fontSize: 12 }}>
            <CalendarOutlined /> 开始：{record.startDate}
          </Text>
          <Text style={{ fontSize: 12 }}>
            <CalendarOutlined /> 结束：{record.endDate}
          </Text>
        </Space>
      )
    },
    {
      title: '进度',
      dataIndex: 'progress',
      key: 'progress',
      render: (progress) => (
        <div style={{ width: 80 }}>
          <div style={{ fontSize: 12, marginBottom: 4 }}>{progress}%</div>
          <div style={{
            width: '100%',
            height: 6,
            backgroundColor: '#f0f0f0',
            borderRadius: 3,
            overflow: 'hidden'
          }}>
            <div style={{
              width: `${progress}%`,
              height: '100%',
              backgroundColor: progress >= 80 ? '#52c41a' : progress >= 50 ? '#1890ff' : '#faad14',
              transition: 'width 0.3s'
            }} />
          </div>
        </div>
      )
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space>
          <Button
            type="link"
            icon={<EyeOutlined />}
            onClick={() => handleViewProject(record)}
          >
            查看
          </Button>
          {(user?.role === 'teacher' || user?.role === 'admin') && (
            <Button
              type="link"
              icon={<EditOutlined />}
              onClick={() => handleEditProject(record)}
            >
              编辑
            </Button>
          )}
        </Space>
      )
    }
  ]

  const proposalColumns: ColumnsType<Proposal> = [
    {
      title: '项目名称',
      dataIndex: 'projectName',
      key: 'projectName'
    },
    {
      title: '申请人',
      dataIndex: 'submittedBy',
      key: 'submittedBy'
    },
    {
      title: '申请时间',
      dataIndex: 'submittedAt',
      key: 'submittedAt'
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <Tag color={getStatusColor(status)}>
          {getStatusText(status)}
        </Tag>
      )
    },
    {
      title: '预算',
      dataIndex: 'estimatedBudget',
      key: 'estimatedBudget',
      render: (budget) => `¥${budget.toLocaleString()}`
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space>
          <Button
            type="link"
            icon={<EyeOutlined />}
            onClick={() => handleViewProposal(record)}
          >
            查看详情
          </Button>
          {user?.role === 'teacher' && record.status === 'pending' && (
            <>
              <Button
                type="link"
                style={{ color: '#52c41a' }}
                onClick={() => handleApproveProposal(record.id)}
              >
                通过
              </Button>
              <Button
                type="link"
                danger
                onClick={() => handleRejectProposal(record.id, '需要进一步完善')}
              >
                驳回
              </Button>
            </>
          )}
        </Space>
      )
    }
  ]

  const handleViewProject = (project: ProjectItem) => {
    setSelectedProject(project)
    setCurrentView('detail')
  }

  const handleEditProject = (project: ProjectItem) => {
    setSelectedProject(project)
    form.setFieldsValue(project)
    setIsCreateModalVisible(true)
  }

  const handleCreateProject = async (values: any) => {
    console.log('创建项目:', values)
    setIsCreateModalVisible(false)
    form.resetFields()
    message.success('项目创建成功')
  }

  const handleSubmitProposal = async (values: any) => {
    console.log('提交方案:', values)
    setIsProposalModalVisible(false)
    proposalForm.resetFields()
    message.success('方案提交成功')
  }

  const handleViewProposal = (proposal: Proposal) => {
    setSelectedProposal(proposal)
    setIsProposalDetailVisible(true)
  }

  const handleApproveProposal = async (proposalId: string) => {
    try {
      // 这里应该调用API更新方案状态
      console.log('批准方案:', proposalId)
      message.success('方案已批准')
      // 刷新数据
    } catch (error) {
      message.error('批准失败')
    }
  }

  const handleRejectProposal = async (proposalId: string, reason: string) => {
    try {
      // 这里应该调用API更新方案状态
      console.log('驳回方案:', proposalId, '原因:', reason)
      message.success('方案已驳回')
      // 刷新数据
    } catch (error) {
      message.error('驳回失败')
    }
  }

  const handleBackToList = () => {
    setCurrentView('list')
    setSelectedProject(null)
  }

  // 过滤项目
  const filteredProjects = projects.filter(project => {
    const matchesSearch = project.name.toLowerCase().includes(searchText.toLowerCase()) ||
                         project.projectNumber.toLowerCase().includes(searchText.toLowerCase())
    const matchesStatus = !statusFilter || project.status === statusFilter
    return matchesSearch && matchesStatus
  })

  // 渲染项目列表页
  const renderProjectList = () => (
    <>
      {/* 页面标题 */}
      <div style={{ marginBottom: 24 }}>
        <Title level={2}>项目管理平台</Title>
        <Paragraph>
          项目全生命周期管理，从立项到交付的完整流程
        </Paragraph>
      </div>

      {/* 顶部操作区 */}
      <Card style={{ marginBottom: 24 }}>
        <Row justify="space-between" align="middle">
          <Col>
            <Space size="middle">
              <Input
                placeholder="搜索项目名称或编号"
                prefix={<SearchOutlined />}
                value={searchText}
                onChange={(e) => setSearchText(e.target.value)}
                style={{ width: 300 }}
                allowClear
              />
              <Select
                placeholder="项目状态"
                value={statusFilter}
                onChange={setStatusFilter}
                style={{ width: 150 }}
                allowClear
              >
                <Select.Option value="planning">规划中</Select.Option>
                <Select.Option value="design">设计中</Select.Option>
                <Select.Option value="construction">施工中</Select.Option>
                <Select.Option value="completed">已完成</Select.Option>
                <Select.Option value="cancelled">已取消</Select.Option>
              </Select>
            </Space>
          </Col>
          <Col>
            <Space>
              {(user?.role === 'teacher' || user?.role === 'admin') && (
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={() => setIsCreateModalVisible(true)}
                >
                  新增项目
                </Button>
              )}
              <Button
                icon={<FileTextOutlined />}
                onClick={() => setIsProposalModalVisible(true)}
              >
                提交方案
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* 统计数据 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="进行中项目"
              value={filteredProjects.filter(p => ['planning', 'design', 'construction'].includes(p.status)).length}
              prefix={<ProjectOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="已完成项目"
              value={filteredProjects.filter(p => p.status === 'completed').length}
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="待审批方案"
              value={proposals.filter(p => p.status === 'pending').length}
              prefix={<ClockCircleOutlined />}
              valueStyle={{ color: '#fa8c16' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="我的项目"
              value={filteredProjects.filter(p =>
                p.createdBy === user?.username ||
                p.assignedTo.includes(user?.username || '')
              ).length}
              prefix={<UserOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 项目列表 */}
      <Card title="项目列表">
        <Table
          columns={projectColumns}
          dataSource={filteredProjects}
          rowKey="id"
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 个项目`
          }}
        />
      </Card>
    </>
  )

  // 渲染项目详情页
  const renderProjectDetail = () => {
    if (!selectedProject) return null

    return (
      <>
        {/* 顶部标题区 */}
        <div style={{ marginBottom: 24 }}>
          <Button
            icon={<EyeOutlined />}
            onClick={handleBackToList}
            style={{ marginBottom: 16 }}
          >
            返回项目列表
          </Button>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <div>
              <Title level={2}>{selectedProject.name}</Title>
              <Space>
                <Text type="secondary">{selectedProject.projectNumber}</Text>
                <Tag color={getStatusColor(selectedProject.status)}>
                  {getStatusText(selectedProject.status)}
                </Tag>
                <Tag color={getPriorityColor(selectedProject.priority)}>
                  {getPriorityText(selectedProject.priority)}
                </Tag>
              </Space>
            </div>
            <Space>
              {(user?.role === 'teacher' || user?.role === 'admin') && (
                <>
                  <Button icon={<TeamOutlined />}>成员管理</Button>
                  <Button icon={<UserOutlined />}>指派负责人</Button>
                  <Button icon={<EditOutlined />}>修改状态</Button>
                </>
              )}
            </Space>
          </div>
        </div>

        {/* 主内容区 - 标签页 */}
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <Tabs.TabPane tab="基础信息" key="basic">
            <Card>
              <Row gutter={24}>
                <Col span={12}>
                  <Space direction="vertical" style={{ width: '100%' }}>
                    <div>
                      <Text strong>项目描述：</Text>
                      <Paragraph>{selectedProject.description}</Paragraph>
                    </div>
                    <div>
                      <Text strong>项目类型：</Text>
                      <Text>{selectedProject.type === 'exhibition' ? '展览' :
                            selectedProject.type === 'conference' ? '会议' : '贸易展'}</Text>
                    </div>
                    <div>
                      <Text strong>项目地点：</Text>
                      <Text>{selectedProject.location}</Text>
                    </div>
                    <div>
                      <Text strong>项目预算：</Text>
                      <Text>¥{selectedProject.budget?.toLocaleString()}</Text>
                    </div>
                  </Space>
                </Col>
                <Col span={12}>
                  <Space direction="vertical" style={{ width: '100%' }}>
                    <div>
                      <Text strong>开始日期：</Text>
                      <Text>{selectedProject.startDate}</Text>
                    </div>
                    <div>
                      <Text strong>结束日期：</Text>
                      <Text>{selectedProject.endDate}</Text>
                    </div>
                    <div>
                      <Text strong>创建人：</Text>
                      <Text>{selectedProject.createdBy}</Text>
                    </div>
                    <div>
                      <Text strong>项目进度：</Text>
                      <div style={{ marginTop: 8 }}>
                        <div style={{ marginBottom: 4 }}>{selectedProject.progress}%</div>
                        <div style={{
                          width: '100%',
                          height: 8,
                          backgroundColor: '#f0f0f0',
                          borderRadius: 4,
                          overflow: 'hidden'
                        }}>
                          <div style={{
                            width: `${selectedProject.progress}%`,
                            height: '100%',
                            backgroundColor: selectedProject.progress >= 80 ? '#52c41a' :
                                           selectedProject.progress >= 50 ? '#1890ff' : '#faad14',
                            transition: 'width 0.3s'
                          }} />
                        </div>
                      </div>
                    </div>
                  </Space>
                </Col>
              </Row>
            </Card>
          </Tabs.TabPane>

          <Tabs.TabPane tab="方案详情" key="proposals">
            <Card
              title="设计方案"
              extra={
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={() => setIsProposalModalVisible(true)}
                >
                  提交新方案
                </Button>
              }
            >
              <Table
                columns={proposalColumns}
                dataSource={selectedProject.proposals}
                rowKey="id"
                pagination={false}
              />
            </Card>
          </Tabs.TabPane>

          <Tabs.TabPane tab="项目成员" key="members">
            <Card title="团队成员">
              <Row gutter={[16, 16]}>
                {selectedProject.members.map(member => (
                  <Col span={8} key={member.id}>
                    <Card size="small">
                      <Card.Meta
                        avatar={<Avatar>{member.name.charAt(0)}</Avatar>}
                        title={member.name}
                        description={
                          <Space direction="vertical" size={0}>
                            <Text type="secondary">{member.role}</Text>
                            <Text type="secondary" style={{ fontSize: 12 }}>
                              加入时间：{member.joinDate}
                            </Text>
                          </Space>
                        }
                      />
                    </Card>
                  </Col>
                ))}
              </Row>
            </Card>
          </Tabs.TabPane>

          <Tabs.TabPane tab="材料清单" key="materials">
            <Card
              title="项目材料"
              extra={
                <Space>
                  <Button
                    icon={<DollarOutlined />}
                    onClick={() => navigate('/projects/materials')}
                  >
                    材料库管理
                  </Button>
                  <Button
                    type="primary"
                    icon={<CalculatorOutlined />}
                  >
                    成本分析
                  </Button>
                </Space>
              }
            >
              <div style={{ marginBottom: 16 }}>
                <Row gutter={16}>
                  <Col span={8}>
                    <Statistic
                      title="材料总数"
                      value={selectedProject.materials.length}
                      suffix="种"
                      valueStyle={{ color: '#1890ff' }}
                    />
                  </Col>
                  <Col span={8}>
                    <Statistic
                      title="预估总成本"
                      value={selectedProject.materials.reduce((sum, m) => sum + m.estimatedCost, 0)}
                      prefix="¥"
                      precision={2}
                      valueStyle={{ color: '#f5222d' }}
                    />
                  </Col>
                  <Col span={8}>
                    <Statistic
                      title="3D编辑器使用"
                      value={selectedProject.materials.filter(m => m.category === '3D素材').length}
                      suffix="种"
                      valueStyle={{ color: '#52c41a' }}
                    />
                  </Col>
                </Row>
              </div>
              <Table
                dataSource={selectedProject.materials}
                rowKey="id"
                pagination={false}
                columns={[
                  { title: '材料名称', dataIndex: 'name', key: 'name' },
                  { title: '类别', dataIndex: 'category', key: 'category' },
                  {
                    title: '数量',
                    key: 'quantity',
                    render: (_, record) => `${record.quantity} ${record.unit}`
                  },
                  {
                    title: '单价',
                    key: 'unitPrice',
                    render: (_, record) => `¥${(record.estimatedCost / record.quantity).toFixed(2)}`
                  },
                  {
                    title: '预估成本',
                    dataIndex: 'estimatedCost',
                    key: 'estimatedCost',
                    render: (cost) => (
                      <Text strong style={{ color: '#f5222d' }}>
                        ¥{cost.toLocaleString()}
                      </Text>
                    )
                  },
                  { title: '供应商', dataIndex: 'supplier', key: 'supplier' },
                  {
                    title: '来源',
                    key: 'source',
                    render: () => (
                      <Tag color="green" size="small">
                        <DollarOutlined /> 3D编辑器
                      </Tag>
                    )
                  }
                ]}
              />
            </Card>
          </Tabs.TabPane>

          <Tabs.TabPane tab="项目文件" key="files">
            <Card
              title="文件库"
              extra={
                <Upload>
                  <Button icon={<UploadOutlined />}>上传文件</Button>
                </Upload>
              }
            >
              <Table
                dataSource={selectedProject.files}
                rowKey="id"
                pagination={false}
                columns={[
                  {
                    title: '文件名',
                    dataIndex: 'name',
                    key: 'name',
                    render: (name, record) => (
                      <Space>
                        <FolderOutlined />
                        <a href={record.url} target="_blank" rel="noopener noreferrer">
                          {name}
                        </a>
                      </Space>
                    )
                  },
                  { title: '类型', dataIndex: 'type', key: 'type' },
                  {
                    title: '大小',
                    dataIndex: 'size',
                    key: 'size',
                    render: (size) => `${(size / 1024).toFixed(1)} KB`
                  },
                  { title: '上传者', dataIndex: 'uploadedBy', key: 'uploadedBy' },
                  { title: '上传时间', dataIndex: 'uploadedAt', key: 'uploadedAt' },
                  {
                    title: '操作',
                    key: 'action',
                    render: (_, record) => (
                      <Space>
                        <Button type="link" icon={<DownloadOutlined />}>下载</Button>
                        <Button type="link" danger>删除</Button>
                      </Space>
                    )
                  }
                ]}
              />
            </Card>
          </Tabs.TabPane>
        </Tabs>
      </>
    )
  }

  return (
    <div style={{ padding: '24px' }}>
      {currentView === 'list' && renderProjectList()}
      {currentView === 'detail' && renderProjectDetail()}

      {/* 创建项目模态框 */}
      <Modal
        title="创建新项目"
        open={isCreateModalVisible}
        onOk={() => form.submit()}
        onCancel={() => setIsCreateModalVisible(false)}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleCreateProject}
        >
          <Form.Item
            name="name"
            label="项目名称"
            rules={[{ required: true, message: '请输入项目名称' }]}
          >
            <Input placeholder="请输入项目名称" />
          </Form.Item>
          
          <Form.Item
            name="description"
            label="项目描述"
            rules={[{ required: true, message: '请输入项目描述' }]}
          >
            <TextArea rows={3} placeholder="请输入项目描述" />
          </Form.Item>
          
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="type"
                label="项目类型"
                rules={[{ required: true, message: '请选择项目类型' }]}
              >
                <Select placeholder="请选择项目类型">
                  <Option value="exhibition">展览</Option>
                  <Option value="conference">会议</Option>
                  <Option value="trade_show">贸易展</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="priority"
                label="优先级"
                rules={[{ required: true, message: '请选择优先级' }]}
              >
                <Select placeholder="请选择优先级">
                  <Option value="high">高</Option>
                  <Option value="medium">中</Option>
                  <Option value="low">低</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>
          
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="startDate"
                label="开始日期"
                rules={[{ required: true, message: '请选择开始日期' }]}
              >
                <DatePicker style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="endDate"
                label="结束日期"
                rules={[{ required: true, message: '请选择结束日期' }]}
              >
                <DatePicker style={{ width: '100%' }} />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>

      {/* 提交方案模态框 */}
      <Modal
        title="提交项目方案"
        open={isProposalModalVisible}
        onOk={() => proposalForm.submit()}
        onCancel={() => setIsProposalModalVisible(false)}
        width={600}
      >
        <Form
          form={proposalForm}
          layout="vertical"
          onFinish={handleSubmitProposal}
        >
          <Form.Item
            name="projectName"
            label="项目名称"
            rules={[{ required: true, message: '请输入项目名称' }]}
          >
            <Input placeholder="请输入项目名称" />
          </Form.Item>
          
          <Form.Item
            name="description"
            label="项目描述"
            rules={[{ required: true, message: '请输入项目描述' }]}
          >
            <TextArea rows={4} placeholder="请详细描述项目内容、目标和预期效果" />
          </Form.Item>
          
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="estimatedBudget"
                label="预估预算（元）"
                rules={[{ required: true, message: '请输入预估预算' }]}
              >
                <Input type="number" placeholder="请输入预估预算" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="estimatedDuration"
                label="预估工期（天）"
                rules={[{ required: true, message: '请输入预估工期' }]}
              >
                <Input type="number" placeholder="请输入预估工期" />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>

      {/* 方案详情查看模态框 */}
      <Modal
        title="方案详情"
        open={isProposalDetailVisible}
        onCancel={() => {
          setIsProposalDetailVisible(false)
          setSelectedProposal(null)
        }}
        footer={[
          <Button key="close" onClick={() => setIsProposalDetailVisible(false)}>
            关闭
          </Button>,
          selectedProposal && user?.role === 'teacher' && selectedProposal.status === 'pending' && (
            <>
              <Button
                key="approve"
                type="primary"
                style={{ backgroundColor: '#52c41a', borderColor: '#52c41a' }}
                onClick={() => {
                  handleApproveProposal(selectedProposal.id)
                  setIsProposalDetailVisible(false)
                }}
              >
                批准方案
              </Button>
              <Button
                key="reject"
                danger
                onClick={() => {
                  handleRejectProposal(selectedProposal.id, '需要进一步完善')
                  setIsProposalDetailVisible(false)
                }}
              >
                驳回方案
              </Button>
            </>
          )
        ]}
        width={800}
      >
        {selectedProposal && (
          <div>
            <Row gutter={16} style={{ marginBottom: 16 }}>
              <Col span={12}>
                <div style={{ marginBottom: 8 }}>
                  <strong>方案名称：</strong>
                  <span>{selectedProposal.projectName}</span>
                </div>
                <div style={{ marginBottom: 8 }}>
                  <strong>提交人：</strong>
                  <span>{selectedProposal.submittedBy}</span>
                </div>
                <div style={{ marginBottom: 8 }}>
                  <strong>提交时间：</strong>
                  <span>{selectedProposal.submittedAt}</span>
                </div>
              </Col>
              <Col span={12}>
                <div style={{ marginBottom: 8 }}>
                  <strong>方案状态：</strong>
                  <Tag color={
                    selectedProposal.status === 'approved' ? 'green' :
                    selectedProposal.status === 'rejected' ? 'red' : 'orange'
                  }>
                    {selectedProposal.status === 'approved' ? '已批准' :
                     selectedProposal.status === 'rejected' ? '已驳回' : '待审核'}
                  </Tag>
                </div>
                <div style={{ marginBottom: 8 }}>
                  <strong>优先级：</strong>
                  <Tag color={
                    selectedProposal.priority === 'high' ? 'red' :
                    selectedProposal.priority === 'medium' ? 'orange' : 'blue'
                  }>
                    {selectedProposal.priority === 'high' ? '高' :
                     selectedProposal.priority === 'medium' ? '中' : '低'}
                  </Tag>
                </div>
                <div style={{ marginBottom: 8 }}>
                  <strong>预估预算：</strong>
                  <span>¥{selectedProposal.estimatedBudget.toLocaleString()}</span>
                </div>
              </Col>
            </Row>

            <div style={{ marginBottom: 16 }}>
              <strong>项目描述：</strong>
              <div style={{
                marginTop: 8,
                padding: 12,
                backgroundColor: '#fafafa',
                borderRadius: 6,
                border: '1px solid #f0f0f0'
              }}>
                {selectedProposal.description}
              </div>
            </div>

            {selectedProposal.designConcept && (
              <div style={{ marginBottom: 16 }}>
                <strong>设计理念：</strong>
                <div style={{
                  marginTop: 8,
                  padding: 12,
                  backgroundColor: '#e6f7ff',
                  borderRadius: 6,
                  border: '1px solid #91d5ff'
                }}>
                  {selectedProposal.designConcept}
                </div>
              </div>
            )}

            {selectedProposal.targetAudience && (
              <div style={{ marginBottom: 16 }}>
                <strong>目标受众：</strong>
                <div style={{
                  marginTop: 8,
                  padding: 12,
                  backgroundColor: '#f6ffed',
                  borderRadius: 6,
                  border: '1px solid #b7eb8f'
                }}>
                  {selectedProposal.targetAudience}
                </div>
              </div>
            )}

            {selectedProposal.expectedOutcome && (
              <div style={{ marginBottom: 16 }}>
                <strong>预期效果：</strong>
                <div style={{
                  marginTop: 8,
                  padding: 12,
                  backgroundColor: '#fff7e6',
                  borderRadius: 6,
                  border: '1px solid #ffd591'
                }}>
                  {selectedProposal.expectedOutcome}
                </div>
              </div>
            )}

            {selectedProposal.timeline && (
              <div style={{ marginBottom: 16 }}>
                <strong>时间安排：</strong>
                <div style={{
                  marginTop: 8,
                  padding: 12,
                  backgroundColor: '#f9f0ff',
                  borderRadius: 6,
                  border: '1px solid #d3adf7'
                }}>
                  {selectedProposal.timeline}
                </div>
              </div>
            )}

            {selectedProposal.reviewComments && (
              <div style={{ marginBottom: 16 }}>
                <strong>审核意见：</strong>
                <div style={{
                  marginTop: 8,
                  padding: 12,
                  backgroundColor: selectedProposal.status === 'approved' ? '#f6ffed' : '#fff2f0',
                  borderRadius: 6,
                  border: `1px solid ${selectedProposal.status === 'approved' ? '#b7eb8f' : '#ffccc7'}`
                }}>
                  {selectedProposal.reviewComments}
                </div>
              </div>
            )}

            <div style={{
              marginTop: 16,
              paddingTop: 16,
              borderTop: '1px solid #f0f0f0',
              fontSize: 12,
              color: '#999'
            }}>
              <div>创建时间：{selectedProposal.submittedAt}</div>
              {selectedProposal.reviewedAt && (
                <div>审核时间：{selectedProposal.reviewedAt}</div>
              )}
            </div>
          </div>
        )}
      </Modal>
    </div>
  )
}

export default ProjectManagementPage
