import React, { useState } from 'react'
import {
  Card,
  Row,
  Col,
  Button,
  Select,
  Typography,
  Space,
  Image,
  Progress,
  Alert,
  Tag,
  Divider,
  Radio
} from 'antd'
import {
  PictureOutlined,
  EyeOutlined,
  DownloadOutlined,
  ReloadOutlined,
  CameraOutlined
} from '@ant-design/icons'
import { mockApi } from '@/services/mockApi'

const { Title, Paragraph } = Typography
const { Option } = Select

const Render2DPage: React.FC = () => {
  const [rendering, setRendering] = useState(false)
  const [progress, setProgress] = useState(0)
  const [renderedImages, setRenderedImages] = useState<any[]>([])
  const [selectedAngle, setSelectedAngle] = useState('perspective')
  const [selectedScene, setSelectedScene] = useState('exhibition')
  const [selectedStyle, setSelectedStyle] = useState('realistic')

  const viewAngles = [
    { value: 'top', label: '俯视图', description: '从上方俯瞰整个展馆布局' },
    { value: 'perspective', label: '透视图', description: '45度角透视效果，最佳观感' },
    { value: 'front', label: '正视图', description: '从正面观看展馆入口' },
    { value: 'side', label: '侧视图', description: '从侧面观看展馆结构' }
  ]

  const sceneTypes = [
    { value: 'empty', label: '空场景', description: '仅显示场馆结构和布局' },
    { value: 'setup', label: '搭建中', description: '展示搭建过程中的场景' },
    { value: 'exhibition', label: '展览中', description: '完整的展览场景，包含人流' },
    { value: 'breakdown', label: '撤展中', description: '展览结束后的撤展场景' }
  ]

  const renderStyles = [
    { value: 'realistic', label: '写实风格', description: '真实感强，细节丰富' },
    { value: 'architectural', label: '建筑风格', description: '专业建筑图纸风格' },
    { value: 'conceptual', label: '概念风格', description: '艺术化概念表现' }
  ]

  const handleRender = async () => {
    setRendering(true)
    setProgress(0)

    // 模拟渲染进度
    const progressInterval = setInterval(() => {
      setProgress(prev => {
        if (prev >= 90) {
          clearInterval(progressInterval)
          return 90
        }
        return prev + 15
      })
    }, 400)

    try {
      const response = await mockApi.render2D('plan-1', {
        angle: selectedAngle,
        scene: selectedScene,
        style: selectedStyle
      })

      if (response.success && response.data) {
        setProgress(100)
        const newImage = {
          id: Date.now(),
          url: response.data.imageUrl,
          angle: selectedAngle,
          scene: selectedScene,
          style: selectedStyle,
          renderTime: response.data.renderTime,
          createdAt: new Date().toLocaleString()
        }
        setRenderedImages(prev => [newImage, ...prev])
      }
    } catch (error) {
      console.error('渲染失败:', error)
    } finally {
      setRendering(false)
      clearInterval(progressInterval)
    }
  }

  return (
    <div>
      <div style={{ marginBottom: 24 }}>
        <Title level={2}>
          <PictureOutlined style={{ marginRight: 8 }} />
          AI 2D效果图生成
        </Title>
        <Paragraph>
          基于AI技术生成多角度、多场景的2D效果图，支持不同风格和视角的渲染
        </Paragraph>
      </div>

      <Row gutter={24}>
        <Col span={8}>
          <Card title="渲染参数配置">
            <Space direction="vertical" style={{ width: '100%' }} size="large">
              <div>
                <Title level={5}>视角选择</Title>
                <Radio.Group
                  value={selectedAngle}
                  onChange={(e) => setSelectedAngle(e.target.value)}
                  style={{ width: '100%' }}
                >
                  <Space direction="vertical" style={{ width: '100%' }}>
                    {viewAngles.map(angle => (
                      <Radio key={angle.value} value={angle.value} style={{ width: '100%' }}>
                        <div>
                          <div style={{ fontWeight: 'bold' }}>{angle.label}</div>
                          <div style={{ fontSize: 12, color: '#666' }}>{angle.description}</div>
                        </div>
                      </Radio>
                    ))}
                  </Space>
                </Radio.Group>
              </div>

              <Divider />

              <div>
                <Title level={5}>场景类型</Title>
                <Select
                  value={selectedScene}
                  onChange={setSelectedScene}
                  style={{ width: '100%' }}
                  size="large"
                >
                  {sceneTypes.map(scene => (
                    <Option key={scene.value} value={scene.value}>
                      <div>
                        <div>{scene.label}</div>
                        <div style={{ fontSize: 12, color: '#666' }}>{scene.description}</div>
                      </div>
                    </Option>
                  ))}
                </Select>
              </div>

              <div>
                <Title level={5}>渲染风格</Title>
                <Select
                  value={selectedStyle}
                  onChange={setSelectedStyle}
                  style={{ width: '100%' }}
                  size="large"
                >
                  {renderStyles.map(style => (
                    <Option key={style.value} value={style.value}>
                      <div>
                        <div>{style.label}</div>
                        <div style={{ fontSize: 12, color: '#666' }}>{style.description}</div>
                      </div>
                    </Option>
                  ))}
                </Select>
              </div>

              <Button
                type="primary"
                size="large"
                icon={rendering ? <ReloadOutlined spin /> : <CameraOutlined />}
                loading={rendering}
                onClick={handleRender}
                style={{ width: '100%', height: 48 }}
              >
                {rendering ? '正在渲染...' : '开始渲染'}
              </Button>

              {rendering && (
                <div>
                  <Progress
                    percent={progress}
                    status={progress === 100 ? 'success' : 'active'}
                    strokeColor={{
                      '0%': '#108ee9',
                      '100%': '#87d068',
                    }}
                  />
                  <div style={{ textAlign: 'center', marginTop: 8, fontSize: 12, color: '#666' }}>
                    预计渲染时间：2-3分钟
                  </div>
                </div>
              )}
            </Space>
          </Card>
        </Col>

        <Col span={16}>
          {rendering && (
            <Card>
              <div style={{ textAlign: 'center', padding: '60px 0' }}>
                <ReloadOutlined spin style={{ fontSize: 48, color: '#1890ff', marginBottom: 24 }} />
                <Title level={3}>AI正在渲染效果图...</Title>
                <Paragraph>
                  正在根据您选择的参数生成高质量2D效果图，请稍候
                </Paragraph>
              </div>
            </Card>
          )}

          {!rendering && renderedImages.length === 0 && (
            <Card>
              <div style={{ textAlign: 'center', padding: '80px 0' }}>
                <PictureOutlined style={{ fontSize: 64, color: '#d9d9d9', marginBottom: 24 }} />
                <Title level={3} style={{ color: '#999' }}>
                  配置参数并开始渲染
                </Title>
                <Paragraph style={{ color: '#666' }}>
                  选择视角、场景和风格，点击开始渲染按钮生成2D效果图
                </Paragraph>
              </div>
            </Card>
          )}

          {renderedImages.length > 0 && (
            <div>
              <Alert
                message="渲染完成！"
                description="AI已成功生成2D效果图，您可以预览、下载或继续渲染其他角度"
                type="success"
                showIcon
                style={{ marginBottom: 24 }}
              />

              <Card title="渲染结果">
                <Row gutter={[16, 16]}>
                  {renderedImages.map((image) => (
                    <Col span={12} key={image.id}>
                      <Card
                        hoverable
                        cover={
                          <Image
                            src={image.url}
                            alt="渲染效果图"
                            style={{ height: 200, objectFit: 'cover' }}
                            preview={{
                              mask: <EyeOutlined style={{ fontSize: 20 }} />
                            }}
                          />
                        }
                        actions={[
                          <Button type="text" icon={<EyeOutlined />} key="preview">
                            预览
                          </Button>,
                          <Button type="text" icon={<DownloadOutlined />} key="download">
                            下载
                          </Button>
                        ]}
                      >
                        <Card.Meta
                          title={
                            <Space>
                              <span>效果图</span>
                              <Tag color="blue">
                                {viewAngles.find(a => a.value === image.angle)?.label}
                              </Tag>
                            </Space>
                          }
                          description={
                            <Space direction="vertical" size="small" style={{ width: '100%' }}>
                              <div>
                                <Tag color="green">
                                  {sceneTypes.find(s => s.value === image.scene)?.label}
                                </Tag>
                                <Tag color="orange">
                                  {renderStyles.find(s => s.value === image.style)?.label}
                                </Tag>
                              </div>
                              <div style={{ fontSize: 12, color: '#666' }}>
                                渲染时间：{image.renderTime}秒
                              </div>
                              <div style={{ fontSize: 12, color: '#666' }}>
                                生成时间：{image.createdAt}
                              </div>
                            </Space>
                          }
                        />
                      </Card>
                    </Col>
                  ))}
                </Row>
              </Card>
            </div>
          )}
        </Col>
      </Row>
    </div>
  )
}

export default Render2DPage
