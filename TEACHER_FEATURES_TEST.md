# 教管学考平台教师端功能测试指南

## 🎯 测试目标
验证教师端的四大核心功能：班级管理、课程管理、作业布置、学情分析

## 📋 测试步骤

### 1. 访问教师端入口

#### 方法一：通过统一门户
1. 访问 `http://localhost:5173/portal`
2. 确保用户角色为 `teacher`
3. 点击"教管学考平台"卡片
4. 应该自动跳转到 `/learning/classes`（班级管理页面）

#### 方法二：通过导航菜单
1. 在左侧导航菜单中找到"教管学考"
2. 展开后应该看到教师专用菜单项：
   - 班级管理
   - 课程管理  
   - 作业管理
   - 学情分析

#### 方法三：直接访问
- 班级管理：`http://localhost:5173/learning/classes`
- 课程管理：`http://localhost:5173/learning/teacher/courses`
- 作业管理：`http://localhost:5173/learning/teacher/assignments`
- 学情分析：`http://localhost:5173/learning/teacher/analytics`

### 2. 班级管理功能测试

**页面路径：** `/learning/classes`

**应该看到的内容：**
- ✅ 页面标题："教学管理中心"
- ✅ 统计数据卡片（我的班级、进行中课程、待批改作业、学生总数）
- ✅ "我的班级" 标签页处于激活状态
- ✅ 班级列表表格，包含：
  - 班级名称
  - 学生人数
  - 创建时间
  - 描述
  - 操作按钮（查看、编辑、管理学生）
- ✅ "创建班级" 按钮

**测试操作：**
1. 点击"创建班级"按钮 → 应该弹出创建班级模态框
2. 填写班级信息并提交 → 应该显示成功消息
3. 点击表格中的"查看"、"编辑"、"管理学生"按钮 → 应该有相应响应

### 3. 课程管理功能测试

**页面路径：** `/learning/teacher/courses`

**应该看到的内容：**
- ✅ "我的课程" 标签页处于激活状态
- ✅ 课程列表表格，包含：
  - 课程名称
  - 所属班级
  - 进度（课时数和进度条）
  - 学生数
  - 状态（已发布/草稿/已完成）
  - 操作按钮（查看、编辑、布置作业）
- ✅ "导入课程" 和 "创建课程" 按钮

**测试操作：**
1. 点击"创建课程"按钮 → 应该弹出创建课程模态框
2. 填写课程信息并提交 → 应该显示成功消息
3. 点击"布置作业"按钮 → 应该跳转到作业管理或弹出作业创建框

### 4. 作业管理功能测试

**页面路径：** `/learning/teacher/assignments`

**应该看到的内容：**
- ✅ "作业管理" 标签页处于激活状态
- ✅ 作业列表表格，包含：
  - 作业名称
  - 课程
  - 班级
  - 类型（实操作业/理论测试）
  - 提交情况（提交人数和进度条）
  - 平均分
  - 截止时间
  - 操作按钮（查看详情、统计分析、批改）
- ✅ "布置作业" 按钮

**测试操作：**
1. 点击"布置作业"按钮 → 应该弹出布置作业模态框
2. 选择作业类型（理论知识/实操知识）→ 应该有不同的设置选项
3. 填写作业信息并提交 → 应该显示成功消息
4. 点击"统计分析"按钮 → 应该显示作业数据分析

### 5. 学情分析功能测试

**页面路径：** `/learning/teacher/analytics`

**应该看到的内容：**
- ✅ "学情分析" 标签页处于激活状态
- ✅ 两个分析卡片：
  - 班级学习情况（带图表占位符）
  - 作业完成统计（带图表占位符）
- ✅ 图表区域显示占位符图标和说明文字

**测试操作：**
1. 查看图表占位符 → 应该显示相应的图标和说明
2. 未来可以在此处集成真实的数据可视化图表

### 6. 标签页切换测试

**测试步骤：**
1. 在任意教师端页面，点击不同的标签页
2. 验证URL是否正确更新：
   - 班级管理 → `/learning/classes`
   - 课程管理 → `/learning/teacher/courses`
   - 作业管理 → `/learning/teacher/assignments`
   - 学情分析 → `/learning/teacher/analytics`
3. 验证页面内容是否正确切换
4. 验证浏览器前进/后退按钮是否正常工作

### 7. 导航菜单测试

**测试步骤：**
1. 确保用户角色为 `teacher`
2. 查看左侧导航菜单中的"教管学考"项
3. 应该显示教师专用的子菜单项，而不是学生的菜单项
4. 点击各个子菜单项，验证跳转是否正确

## ✅ 预期结果

### 功能完整性
- [x] 班级管理：创建、查看、编辑班级
- [x] 课程管理：创建、管理课程，查看进度
- [x] 作业管理：布置作业、查看提交情况、批改作业
- [x] 学情分析：数据可视化展示区域

### 用户体验
- [x] 界面布局清晰，功能分区明确
- [x] 操作流程符合教师工作习惯
- [x] 统计数据一目了然
- [x] 响应式设计，适配不同屏幕

### 技术实现
- [x] 路由配置正确，URL与功能对应
- [x] 标签页切换流畅，状态同步
- [x] 表单验证和提交正常
- [x] 模态框交互正常

## 🐛 常见问题排查

### 问题1：看不到教师端菜单
**原因：** 用户角色不是 `teacher`
**解决：** 检查 `useAuthStore` 中的用户角色设置

### 问题2：点击菜单没有跳转
**原因：** 路由配置问题
**解决：** 检查 `App.tsx` 中的路由配置

### 问题3：标签页切换不正常
**原因：** `handleTabChange` 函数问题
**解决：** 检查 `TeacherManagePage.tsx` 中的标签页处理逻辑

### 问题4：统计数据不显示
**原因：** 模拟数据问题
**解决：** 检查组件中的数据源配置

## 📝 测试记录

请在测试时记录以下信息：

- [ ] 测试时间：
- [ ] 浏览器版本：
- [ ] 测试结果：通过/失败
- [ ] 发现的问题：
- [ ] 改进建议：

---

**注意：** 如果发现任何功能缺失或异常，请检查相关组件的实现和路由配置。所有教师端功能都已在 `TeacherManagePage.tsx` 中实现，只需要确保路由和导航配置正确。
