#!/usr/bin/env python
"""
测试智能教学代理系统的依赖包
"""

def test_imports():
    """测试所有必需的包是否能正常导入"""
    print("正在测试依赖包...")
    
    try:
        import streamlit
        print("✅ Streamlit 导入成功")
    except ImportError as e:
        print(f"❌ Streamlit 导入失败: {e}")
        return False
    
    try:
        import pandas
        print("✅ Pandas 导入成功")
    except ImportError as e:
        print(f"❌ Pandas 导入失败: {e}")
        return False
    
    try:
        import numpy
        print("✅ NumPy 导入成功")
    except ImportError as e:
        print(f"❌ NumPy 导入失败: {e}")
        return False
    
    try:
        import plotly
        print("✅ Plotly 导入成功")
    except ImportError as e:
        print(f"❌ Plotly 导入失败: {e}")
        return False
    
    try:
        from prophet import Prophet
        print("✅ Prophet 导入成功")
    except ImportError as e:
        print(f"❌ Prophet 导入失败: {e}")
        print("提示: 请运行 'pip install prophet' 安装Prophet")
        return False
    
    try:
        import scipy
        print("✅ SciPy 导入成功")
    except ImportError as e:
        print(f"❌ SciPy 导入失败: {e}")
        return False
    
    print("\n🎉 所有依赖包测试通过！")
    return True

def test_basic_functionality():
    """测试基本功能"""
    print("\n正在测试基本功能...")
    
    try:
        import pandas as pd
        import numpy as np
        
        # 测试数据生成
        dates = pd.date_range('2023-01-01', periods=100, freq='D')
        data = pd.DataFrame({
            'ds': dates,
            'y': np.random.randn(100) + 100
        })
        print("✅ 数据生成功能正常")
        
        # 测试AHP矩阵计算
        matrix = np.array([[1, 2, 3], [0.5, 1, 2], [0.33, 0.5, 1]])
        eigenvalues, eigenvectors = np.linalg.eig(matrix)
        print("✅ AHP矩阵计算功能正常")
        
        print("🎉 基本功能测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 基本功能测试失败: {e}")
        return False

if __name__ == "__main__":
    print("=" * 50)
    print("智能教学代理系统 - 依赖测试")
    print("=" * 50)
    
    # 测试导入
    imports_ok = test_imports()
    
    if imports_ok:
        # 测试基本功能
        functionality_ok = test_basic_functionality()
        
        if functionality_ok:
            print("\n✅ 系统准备就绪！可以启动应用了。")
            print("运行命令: streamlit run app.py")
        else:
            print("\n❌ 基本功能测试失败，请检查安装。")
    else:
        print("\n❌ 依赖包测试失败，请安装缺失的包。")
        print("运行命令: pip install -r requirements.txt")
