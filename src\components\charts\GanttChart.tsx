import React, { useMemo } from 'react'
import { Card, Typography, Tag, Progress, Tooltip } from 'antd'
import type { ConstructionTask } from '@/types/construction'

const { Title } = Typography

interface GanttChartProps {
  tasks: ConstructionTask[]
  startDate: string
  endDate: string
  height?: number
}

interface GanttBar {
  task: ConstructionTask
  left: number
  width: number
  color: string
}

const GanttChart: React.FC<GanttChartProps> = ({ 
  tasks, 
  startDate, 
  endDate, 
  height = 400 
}) => {
  // 计算甘特图数据
  const ganttData = useMemo(() => {
    const projectStart = new Date(startDate)
    const projectEnd = new Date(endDate)
    const totalDays = Math.ceil((projectEnd.getTime() - projectStart.getTime()) / (1000 * 60 * 60 * 24))

    const bars: GanttBar[] = tasks.map(task => {
      const taskStart = new Date(task.startDate)
      const taskEnd = new Date(task.endDate)
      
      const startOffset = Math.max(0, Math.ceil((taskStart.getTime() - projectStart.getTime()) / (1000 * 60 * 60 * 24)))
      const duration = Math.ceil((taskEnd.getTime() - taskStart.getTime()) / (1000 * 60 * 60 * 24))
      
      const left = (startOffset / totalDays) * 100
      const width = (duration / totalDays) * 100

      // 根据状态设置颜色
      let color = '#1890ff'
      if (task.status === 'completed') color = '#52c41a'
      else if (task.status === 'delayed') color = '#f5222d'
      else if (task.status === 'in_progress') color = '#fa8c16'
      else if (task.status === 'pending') color = '#d9d9d9'

      return {
        task,
        left,
        width,
        color
      }
    })

    return { bars, totalDays }
  }, [tasks, startDate, endDate])

  // 生成时间轴标签
  const timeLabels = useMemo(() => {
    const labels = []
    const start = new Date(startDate)
    const end = new Date(endDate)
    const totalDays = Math.ceil((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24))
    
    // 每5天显示一个标签
    for (let i = 0; i <= totalDays; i += 5) {
      const date = new Date(start)
      date.setDate(date.getDate() + i)
      labels.push({
        date: date.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' }),
        position: (i / totalDays) * 100
      })
    }
    
    return labels
  }, [startDate, endDate])

  const getStatusText = (status: ConstructionTask['status']) => {
    const statusMap = {
      pending: '待开始',
      in_progress: '进行中',
      completed: '已完成',
      delayed: '延期',
      cancelled: '已取消'
    }
    return statusMap[status]
  }

  const getCategoryText = (category: ConstructionTask['category']) => {
    const categoryMap = {
      preparation: '准备',
      structure: '结构',
      decoration: '装饰',
      installation: '安装',
      finishing: '收尾',
      inspection: '检验'
    }
    return categoryMap[category]
  }

  return (
    <Card>
      <Title level={4} style={{ marginBottom: 24 }}>项目甘特图</Title>
      
      <div style={{ overflowX: 'auto' }}>
        <div style={{ minWidth: 800, position: 'relative' }}>
          {/* 时间轴 */}
          <div style={{ 
            height: 40, 
            borderBottom: '1px solid #f0f0f0',
            position: 'relative',
            marginBottom: 16
          }}>
            {timeLabels.map((label, index) => (
              <div
                key={index}
                style={{
                  position: 'absolute',
                  left: `${label.position}%`,
                  top: 0,
                  height: '100%',
                  borderLeft: '1px solid #f0f0f0',
                  paddingLeft: 4,
                  fontSize: 12,
                  color: '#666',
                  display: 'flex',
                  alignItems: 'center'
                }}
              >
                {label.date}
              </div>
            ))}
          </div>

          {/* 甘特条 */}
          <div style={{ minHeight: height }}>
            {ganttData.bars.map((bar, index) => (
              <div
                key={bar.task.id}
                style={{
                  marginBottom: 16,
                  position: 'relative'
                }}
              >
                {/* 任务信息 */}
                <div style={{ 
                  display: 'flex', 
                  alignItems: 'center',
                  marginBottom: 8,
                  minHeight: 32
                }}>
                  <div style={{ width: 200, paddingRight: 16 }}>
                    <div style={{ fontWeight: 500, fontSize: 14 }}>
                      {bar.task.name}
                    </div>
                    <div style={{ fontSize: 12, color: '#666' }}>
                      <Tag size="small" color="blue">
                        {getCategoryText(bar.task.category)}
                      </Tag>
                      <Tag size="small" color={bar.color}>
                        {getStatusText(bar.task.status)}
                      </Tag>
                    </div>
                  </div>
                  
                  {/* 甘特条背景 */}
                  <div style={{ 
                    flex: 1, 
                    height: 24, 
                    backgroundColor: '#f5f5f5',
                    borderRadius: 4,
                    position: 'relative',
                    overflow: 'hidden'
                  }}>
                    {/* 甘特条 */}
                    <Tooltip
                      title={
                        <div>
                          <div><strong>{bar.task.name}</strong></div>
                          <div>开始：{bar.task.startDate}</div>
                          <div>结束：{bar.task.endDate}</div>
                          <div>进度：{bar.task.progress}%</div>
                          <div>状态：{getStatusText(bar.task.status)}</div>
                          {bar.task.assignedTo.length > 0 && (
                            <div>
                              分配给：{bar.task.assignedTo.map(id => `学生${id.split('-')[1]}`).join(', ')}
                            </div>
                          )}
                        </div>
                      }
                    >
                      <div
                        style={{
                          position: 'absolute',
                          left: `${bar.left}%`,
                          width: `${bar.width}%`,
                          height: '100%',
                          backgroundColor: bar.color,
                          borderRadius: 4,
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          color: 'white',
                          fontSize: 12,
                          fontWeight: 500,
                          cursor: 'pointer',
                          transition: 'all 0.3s ease'
                        }}
                        onMouseEnter={(e) => {
                          e.currentTarget.style.opacity = '0.8'
                          e.currentTarget.style.transform = 'scale(1.02)'
                        }}
                        onMouseLeave={(e) => {
                          e.currentTarget.style.opacity = '1'
                          e.currentTarget.style.transform = 'scale(1)'
                        }}
                      >
                        {bar.task.progress}%
                      </div>
                    </Tooltip>

                    {/* 进度条 */}
                    {bar.task.status === 'in_progress' && (
                      <div
                        style={{
                          position: 'absolute',
                          left: `${bar.left}%`,
                          width: `${(bar.width * bar.task.progress) / 100}%`,
                          height: '100%',
                          backgroundColor: '#52c41a',
                          borderRadius: 4,
                          opacity: 0.8
                        }}
                      />
                    )}
                  </div>

                  {/* 进度百分比 */}
                  <div style={{ width: 60, textAlign: 'center', paddingLeft: 8 }}>
                    <Progress
                      type="circle"
                      size={32}
                      percent={bar.task.progress}
                      strokeWidth={8}
                      format={() => `${bar.task.progress}%`}
                      strokeColor={bar.color}
                    />
                  </div>
                </div>

                {/* 依赖关系线 */}
                {bar.task.dependencies.length > 0 && (
                  <div style={{ 
                    position: 'absolute',
                    top: -8,
                    left: 200,
                    right: 0,
                    height: 1,
                    backgroundColor: '#d9d9d9',
                    zIndex: 1
                  }}>
                    {bar.task.dependencies.map(depId => {
                      const depTask = tasks.find(t => t.id === depId)
                      if (!depTask) return null
                      
                      const depBar = ganttData.bars.find(b => b.task.id === depId)
                      if (!depBar) return null

                      return (
                        <div
                          key={depId}
                          style={{
                            position: 'absolute',
                            left: `${depBar.left + depBar.width}%`,
                            width: `${bar.left - (depBar.left + depBar.width)}%`,
                            height: 1,
                            backgroundColor: '#1890ff',
                            top: 0
                          }}
                        />
                      )
                    })}
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* 图例 */}
      <div style={{ 
        marginTop: 24, 
        padding: 16, 
        backgroundColor: '#fafafa',
        borderRadius: 6
      }}>
        <div style={{ marginBottom: 8, fontWeight: 500 }}>图例说明：</div>
        <div style={{ display: 'flex', flexWrap: 'wrap', gap: 16 }}>
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <div style={{ 
              width: 16, 
              height: 16, 
              backgroundColor: '#d9d9d9', 
              marginRight: 8,
              borderRadius: 2
            }} />
            <span style={{ fontSize: 12 }}>待开始</span>
          </div>
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <div style={{ 
              width: 16, 
              height: 16, 
              backgroundColor: '#fa8c16', 
              marginRight: 8,
              borderRadius: 2
            }} />
            <span style={{ fontSize: 12 }}>进行中</span>
          </div>
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <div style={{ 
              width: 16, 
              height: 16, 
              backgroundColor: '#52c41a', 
              marginRight: 8,
              borderRadius: 2
            }} />
            <span style={{ fontSize: 12 }}>已完成</span>
          </div>
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <div style={{ 
              width: 16, 
              height: 16, 
              backgroundColor: '#f5222d', 
              marginRight: 8,
              borderRadius: 2
            }} />
            <span style={{ fontSize: 12 }}>延期</span>
          </div>
        </div>
      </div>
    </Card>
  )
}

export default GanttChart
