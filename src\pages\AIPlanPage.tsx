import React, { useState } from 'react'
import {
  Card,
  Form,
  Input,
  Select,
  Button,
  Row,
  Col,
  Typography,
  Space,
  Progress,
  Alert,
  Divider,
  Tag,
  Statistic
} from 'antd'
import {
  BulbOutlined,
  RocketOutlined,
  CheckCircleOutlined,
  LoadingOutlined
} from '@ant-design/icons'
import { useProjectStore } from '@/store/projectStore'

const { Title, Paragraph } = Typography
const { TextArea } = Input
const { Option } = Select

const AIPlanPage: React.FC = () => {
  const [form] = Form.useForm()
  const [generating, setGenerating] = useState(false)
  const [progress, setProgress] = useState(0)
  const [generatedPlan, setGeneratedPlan] = useState<any>(null)
  const { generatePlan, currentProject } = useProjectStore()

  const handleGenerate = async (values: any) => {
    setGenerating(true)
    setProgress(0)
    setGeneratedPlan(null)

    // 模拟进度更新
    const progressInterval = setInterval(() => {
      setProgress(prev => {
        if (prev >= 90) {
          clearInterval(progressInterval)
          return 90
        }
        return prev + 10
      })
    }, 300)

    try {
      const plan = await generatePlan(currentProject?.id || '1', values)
      if (plan) {
        setProgress(100)
        setGeneratedPlan(plan)
      }
    } catch (error) {
      console.error('生成方案失败:', error)
    } finally {
      setGenerating(false)
      clearInterval(progressInterval)
    }
  }

  const renderPlanVisualization = () => {
    if (!generatedPlan) return null

    return (
      <Card title="生成的方案预览" style={{ marginTop: 24 }}>
        <Row gutter={[16, 16]}>
          <Col span={12}>
            <div
              style={{
                width: '100%',
                height: 300,
                border: '2px dashed #d9d9d9',
                borderRadius: 8,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                background: '#fafafa',
                position: 'relative'
              }}
            >
              {/* 简单的布局可视化 */}
              <div style={{ textAlign: 'center' }}>
                <BulbOutlined style={{ fontSize: 48, color: '#1890ff', marginBottom: 16 }} />
                <div>布局预览图</div>
                <div style={{ fontSize: 12, color: '#999', marginTop: 8 }}>
                  {generatedPlan.layout.width} × {generatedPlan.layout.height}
                </div>
              </div>
              
              {/* 模拟展位 */}
              {generatedPlan.booths.slice(0, 3).map((booth: any, index: number) => (
                <div
                  key={booth.id}
                  style={{
                    position: 'absolute',
                    left: `${20 + index * 25}%`,
                    top: `${30 + index * 15}%`,
                    width: 40,
                    height: 30,
                    background: '#1890ff',
                    borderRadius: 4,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    color: 'white',
                    fontSize: 10
                  }}
                >
                  {booth.name}
                </div>
              ))}
            </div>
          </Col>
          <Col span={12}>
            <Space direction="vertical" style={{ width: '100%' }}>
              <Statistic title="展位数量" value={generatedPlan.booths.length} />
              <Statistic title="通道数量" value={generatedPlan.flowPaths.length} />
              <Statistic title="展区面积" value={`${generatedPlan.layout.width * generatedPlan.layout.height / 10000}`} suffix="万平方米" />
              
              <Divider />
              
              <div>
                <Title level={5}>展位列表</Title>
                <Space wrap>
                  {generatedPlan.booths.map((booth: any) => (
                    <Tag key={booth.id} color="blue">
                      {booth.name} - {booth.exhibitor}
                    </Tag>
                  ))}
                </Space>
              </div>
              
              <div style={{ marginTop: 16 }}>
                <Title level={5}>通道规划</Title>
                <Space wrap>
                  {generatedPlan.flowPaths.map((path: any) => (
                    <Tag key={path.id} color={path.type === 'main' ? 'green' : 'orange'}>
                      {path.name} ({path.width}m)
                    </Tag>
                  ))}
                </Space>
              </div>
            </Space>
          </Col>
        </Row>
        
        <div style={{ marginTop: 24, textAlign: 'center' }}>
          <Space>
            <Button type="primary" size="large">
              保存方案
            </Button>
            <Button size="large">
              导出方案
            </Button>
            <Button size="large">
              生成2D效果图
            </Button>
          </Space>
        </div>
      </Card>
    )
  }

  return (
    <div>
      <div style={{ marginBottom: 24 }}>
        <Title level={2}>
          <BulbOutlined style={{ marginRight: 8 }} />
          AI智能方案生成
        </Title>
        <Paragraph>
          基于AI技术，智能生成会展布局方案，包括展位分配、流线规划、区域划分等
        </Paragraph>
      </div>

      <Row gutter={24}>
        <Col span={8}>
          <Card title="方案需求配置" style={{ height: 'fit-content' }}>
            <Form
              form={form}
              layout="vertical"
              onFinish={handleGenerate}
            >
              <Form.Item
                name="exhibitionType"
                label="展览类型"
                rules={[{ required: true, message: '请选择展览类型' }]}
              >
                <Select placeholder="选择展览类型">
                  <Option value="auto">汽车展</Option>
                  <Option value="tech">科技展</Option>
                  <Option value="furniture">家具展</Option>
                  <Option value="food">食品展</Option>
                  <Option value="fashion">时装展</Option>
                </Select>
              </Form.Item>

              <Form.Item
                name="venueSize"
                label="场馆规模"
                rules={[{ required: true, message: '请选择场馆规模' }]}
              >
                <Select placeholder="选择场馆规模">
                  <Option value="small">小型 (1000-3000㎡)</Option>
                  <Option value="medium">中型 (3000-8000㎡)</Option>
                  <Option value="large">大型 (8000-15000㎡)</Option>
                  <Option value="mega">超大型 (15000㎡+)</Option>
                </Select>
              </Form.Item>

              <Form.Item
                name="boothCount"
                label="预计展位数量"
                rules={[{ required: true, message: '请输入展位数量' }]}
              >
                <Input placeholder="如：50" suffix="个" />
              </Form.Item>

              <Form.Item
                name="specialRequirements"
                label="特殊需求"
              >
                <TextArea
                  rows={4}
                  placeholder="请描述特殊需求，如：需要VIP区域、特殊展示区、无障碍通道等"
                />
              </Form.Item>

              <Form.Item
                name="style"
                label="设计风格"
              >
                <Select placeholder="选择设计风格">
                  <Option value="modern">现代简约</Option>
                  <Option value="classic">经典传统</Option>
                  <Option value="tech">科技未来</Option>
                  <Option value="artistic">艺术创意</Option>
                </Select>
              </Form.Item>

              <Form.Item>
                <Button
                  type="primary"
                  htmlType="submit"
                  loading={generating}
                  icon={generating ? <LoadingOutlined /> : <RocketOutlined />}
                  size="large"
                  style={{ width: '100%' }}
                >
                  {generating ? '正在生成方案...' : '生成AI方案'}
                </Button>
              </Form.Item>
            </Form>
          </Card>
        </Col>

        <Col span={16}>
          {generating && (
            <Card>
              <div style={{ textAlign: 'center', padding: '40px 0' }}>
                <LoadingOutlined style={{ fontSize: 48, color: '#1890ff', marginBottom: 24 }} />
                <Title level={3}>AI正在生成方案...</Title>
                <Paragraph style={{ marginBottom: 32 }}>
                  正在分析需求并生成最优布局方案，请稍候
                </Paragraph>
                <Progress
                  percent={progress}
                  status={progress === 100 ? 'success' : 'active'}
                  strokeColor={{
                    '0%': '#108ee9',
                    '100%': '#87d068',
                  }}
                />
                <div style={{ marginTop: 16, fontSize: 14, color: '#666' }}>
                  {progress < 30 && '正在分析展览需求...'}
                  {progress >= 30 && progress < 60 && '正在计算最优布局...'}
                  {progress >= 60 && progress < 90 && '正在生成展位分配...'}
                  {progress >= 90 && '正在完善方案细节...'}
                </div>
              </div>
            </Card>
          )}

          {!generating && !generatedPlan && (
            <Card>
              <div style={{ textAlign: 'center', padding: '60px 0' }}>
                <BulbOutlined style={{ fontSize: 64, color: '#d9d9d9', marginBottom: 24 }} />
                <Title level={3} style={{ color: '#999' }}>
                  配置需求并生成AI方案
                </Title>
                <Paragraph style={{ color: '#666' }}>
                  在左侧填写展览需求，点击生成按钮开始AI智能方案生成
                </Paragraph>
              </div>
            </Card>
          )}

          {generatedPlan && (
            <>
              <Alert
                message="方案生成成功！"
                description="AI已根据您的需求生成了最优的展览布局方案"
                type="success"
                showIcon
                icon={<CheckCircleOutlined />}
                style={{ marginBottom: 24 }}
              />
              {renderPlanVisualization()}
            </>
          )}
        </Col>
      </Row>
    </div>
  )
}

export default AIPlanPage
