{"name": "mctt-exhibition-platform", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "antd": "^5.12.8", "react-router-dom": "^6.20.1", "zustand": "^4.4.7", "axios": "^1.6.2", "three": "^0.158.0", "@react-three/fiber": "^8.15.11", "@react-three/drei": "^9.88.13", "fabric": "^5.3.0", "dayjs": "^1.11.10"}, "devDependencies": {"@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@types/three": "^0.158.3", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "@vitejs/plugin-react": "^4.1.1", "eslint": "^8.53.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "typescript": "^5.2.2", "vite": "^5.0.0", "vitest": "^1.0.0", "@vitest/ui": "^1.0.0"}}