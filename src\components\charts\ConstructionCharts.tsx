import React, { useMemo } from 'react'
import { Card, Row, Col, Typography, Progress, Tag } from 'antd'
import type { ConstructionTask, ConstructionStats } from '@/types/construction'

const { Title } = Typography

interface ConstructionChartsProps {
  tasks: ConstructionTask[]
  stats: ConstructionStats
}

const ConstructionCharts: React.FC<ConstructionChartsProps> = ({ tasks, stats }) => {
  // 状态分布数据
  const statusDistribution = useMemo(() => {
    const distribution = {
      pending: 0,
      in_progress: 0,
      completed: 0,
      delayed: 0,
      cancelled: 0
    }
    
    tasks.forEach(task => {
      distribution[task.status]++
    })
    
    return Object.entries(distribution).map(([status, count]) => ({
      status,
      count,
      percentage: tasks.length > 0 ? (count / tasks.length) * 100 : 0,
      color: {
        pending: '#d9d9d9',
        in_progress: '#fa8c16',
        completed: '#52c41a',
        delayed: '#f5222d',
        cancelled: '#8c8c8c'
      }[status],
      label: {
        pending: '待开始',
        in_progress: '进行中',
        completed: '已完成',
        delayed: '延期',
        cancelled: '已取消'
      }[status]
    }))
  }, [tasks])

  // 工序类型分布
  const categoryDistribution = useMemo(() => {
    const distribution = {
      preparation: 0,
      structure: 0,
      decoration: 0,
      installation: 0,
      finishing: 0,
      inspection: 0
    }
    
    tasks.forEach(task => {
      distribution[task.category]++
    })
    
    return Object.entries(distribution).map(([category, count]) => ({
      category,
      count,
      percentage: tasks.length > 0 ? (count / tasks.length) * 100 : 0,
      color: {
        preparation: '#1890ff',
        structure: '#52c41a',
        decoration: '#fa8c16',
        installation: '#722ed1',
        finishing: '#13c2c2',
        inspection: '#eb2f96'
      }[category],
      label: {
        preparation: '准备',
        structure: '结构',
        decoration: '装饰',
        installation: '安装',
        finishing: '收尾',
        inspection: '检验'
      }[category]
    }))
  }, [tasks])

  // 优先级分布
  const priorityDistribution = useMemo(() => {
    const distribution = {
      low: 0,
      medium: 0,
      high: 0,
      urgent: 0
    }
    
    tasks.forEach(task => {
      distribution[task.priority]++
    })
    
    return Object.entries(distribution).map(([priority, count]) => ({
      priority,
      count,
      percentage: tasks.length > 0 ? (count / tasks.length) * 100 : 0,
      color: {
        low: '#52c41a',
        medium: '#fa8c16',
        high: '#f5222d',
        urgent: '#722ed1'
      }[priority],
      label: {
        low: '低',
        medium: '中',
        high: '高',
        urgent: '紧急'
      }[priority]
    }))
  }, [tasks])

  // 进度分布
  const progressDistribution = useMemo(() => {
    const ranges = [
      { min: 0, max: 20, label: '0-20%', color: '#f5222d' },
      { min: 21, max: 40, label: '21-40%', color: '#fa8c16' },
      { min: 41, max: 60, label: '41-60%', color: '#fadb14' },
      { min: 61, max: 80, label: '61-80%', color: '#a0d911' },
      { min: 81, max: 100, label: '81-100%', color: '#52c41a' }
    ]
    
    return ranges.map(range => {
      const count = tasks.filter(task => 
        task.progress >= range.min && task.progress <= range.max
      ).length
      
      return {
        ...range,
        count,
        percentage: tasks.length > 0 ? (count / tasks.length) * 100 : 0
      }
    })
  }, [tasks])

  // 饼图组件
  const PieChart: React.FC<{
    data: Array<{ label: string; count: number; percentage: number; color: string }>
    title: string
  }> = ({ data, title }) => {
    const total = data.reduce((sum, item) => sum + item.count, 0)
    
    return (
      <Card title={title} style={{ height: '100%' }}>
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', minHeight: 200 }}>
          {/* 简化的饼图 - 使用环形进度条模拟 */}
          <div style={{ position: 'relative', width: 120, height: 120 }}>
            {data.map((item, index) => {
              const prevPercentage = data.slice(0, index).reduce((sum, prev) => sum + prev.percentage, 0)
              return (
                <div
                  key={item.label}
                  style={{
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    width: '100%',
                    height: '100%',
                    borderRadius: '50%',
                    background: `conic-gradient(
                      transparent ${prevPercentage * 3.6}deg,
                      ${item.color} ${prevPercentage * 3.6}deg ${(prevPercentage + item.percentage) * 3.6}deg,
                      transparent ${(prevPercentage + item.percentage) * 3.6}deg
                    )`
                  }}
                />
              )
            })}
            <div style={{
              position: 'absolute',
              top: '50%',
              left: '50%',
              transform: 'translate(-50%, -50%)',
              fontSize: 16,
              fontWeight: 'bold',
              color: '#262626'
            }}>
              {total}
            </div>
          </div>
        </div>
        
        {/* 图例 */}
        <div style={{ marginTop: 16 }}>
          {data.map(item => (
            <div key={item.label} style={{ 
              display: 'flex', 
              alignItems: 'center', 
              justifyContent: 'space-between',
              marginBottom: 8 
            }}>
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <div style={{
                  width: 12,
                  height: 12,
                  backgroundColor: item.color,
                  borderRadius: 2,
                  marginRight: 8
                }} />
                <span style={{ fontSize: 12 }}>{item.label}</span>
              </div>
              <div style={{ fontSize: 12, color: '#666' }}>
                {item.count} ({item.percentage.toFixed(1)}%)
              </div>
            </div>
          ))}
        </div>
      </Card>
    )
  }

  // 条形图组件
  const BarChart: React.FC<{
    data: Array<{ label: string; count: number; percentage: number; color: string }>
    title: string
  }> = ({ data, title }) => {
    const maxCount = Math.max(...data.map(item => item.count))
    
    return (
      <Card title={title} style={{ height: '100%' }}>
        <div style={{ padding: '16px 0' }}>
          {data.map(item => (
            <div key={item.label} style={{ marginBottom: 16 }}>
              <div style={{ 
                display: 'flex', 
                justifyContent: 'space-between', 
                alignItems: 'center',
                marginBottom: 4 
              }}>
                <span style={{ fontSize: 12, fontWeight: 500 }}>{item.label}</span>
                <span style={{ fontSize: 12, color: '#666' }}>
                  {item.count} ({item.percentage.toFixed(1)}%)
                </span>
              </div>
              <div style={{
                width: '100%',
                height: 8,
                backgroundColor: '#f5f5f5',
                borderRadius: 4,
                overflow: 'hidden'
              }}>
                <div style={{
                  width: `${maxCount > 0 ? (item.count / maxCount) * 100 : 0}%`,
                  height: '100%',
                  backgroundColor: item.color,
                  borderRadius: 4,
                  transition: 'width 0.3s ease'
                }} />
              </div>
            </div>
          ))}
        </div>
      </Card>
    )
  }

  return (
    <div>
      <Title level={4} style={{ marginBottom: 24 }}>项目统计图表</Title>
      
      <Row gutter={[16, 16]}>
        {/* 状态分布饼图 */}
        <Col span={12}>
          <PieChart data={statusDistribution} title="任务状态分布" />
        </Col>
        
        {/* 工序类型分布饼图 */}
        <Col span={12}>
          <PieChart data={categoryDistribution} title="工序类型分布" />
        </Col>
        
        {/* 优先级分布条形图 */}
        <Col span={12}>
          <BarChart data={priorityDistribution} title="优先级分布" />
        </Col>
        
        {/* 进度分布条形图 */}
        <Col span={12}>
          <BarChart data={progressDistribution} title="进度分布" />
        </Col>
      </Row>

      {/* 详细统计信息 */}
      <Row gutter={16} style={{ marginTop: 24 }}>
        <Col span={24}>
          <Card title="项目概览">
            <Row gutter={16}>
              <Col span={6}>
                <div style={{ textAlign: 'center' }}>
                  <div style={{ fontSize: 24, fontWeight: 'bold', color: '#1890ff' }}>
                    {stats.totalTasks}
                  </div>
                  <div style={{ color: '#666' }}>总任务数</div>
                </div>
              </Col>
              <Col span={6}>
                <div style={{ textAlign: 'center' }}>
                  <div style={{ fontSize: 24, fontWeight: 'bold', color: '#52c41a' }}>
                    {stats.overallProgress}%
                  </div>
                  <div style={{ color: '#666' }}>整体进度</div>
                  <Progress 
                    percent={stats.overallProgress} 
                    size="small" 
                    style={{ marginTop: 8 }}
                  />
                </div>
              </Col>
              <Col span={6}>
                <div style={{ textAlign: 'center' }}>
                  <div style={{ fontSize: 24, fontWeight: 'bold', color: '#fa8c16' }}>
                    {stats.onTimeRate}%
                  </div>
                  <div style={{ color: '#666' }}>按时完成率</div>
                </div>
              </Col>
              <Col span={6}>
                <div style={{ textAlign: 'center' }}>
                  <div style={{ fontSize: 24, fontWeight: 'bold', color: '#722ed1' }}>
                    {stats.activeStudents}/{stats.totalStudents}
                  </div>
                  <div style={{ color: '#666' }}>参与学生</div>
                </div>
              </Col>
            </Row>
          </Card>
        </Col>
      </Row>

      {/* 任务列表摘要 */}
      <Row gutter={16} style={{ marginTop: 16 }}>
        <Col span={24}>
          <Card title="任务状态摘要">
            <div style={{ display: 'flex', flexWrap: 'wrap', gap: 8 }}>
              {statusDistribution.map(item => (
                <Tag 
                  key={item.status}
                  color={item.color}
                  style={{ margin: 0, padding: '4px 8px' }}
                >
                  {item.label}: {item.count}个
                </Tag>
              ))}
            </div>
            
            <div style={{ marginTop: 16, display: 'flex', flexWrap: 'wrap', gap: 8 }}>
              {categoryDistribution.filter(item => item.count > 0).map(item => (
                <Tag 
                  key={item.category}
                  color={item.color}
                  style={{ margin: 0, padding: '4px 8px' }}
                >
                  {item.label}: {item.count}个
                </Tag>
              ))}
            </div>
          </Card>
        </Col>
      </Row>
    </div>
  )
}

export default ConstructionCharts
