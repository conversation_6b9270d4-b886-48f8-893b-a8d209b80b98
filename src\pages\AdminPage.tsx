import React, { useState, useEffect } from 'react'
import { <PERSON>, Row, Col, <PERSON>po<PERSON>, Button, Table, Tag, Space, Modal, Form, Input, Select, Switch, Tabs, Statistic, Upload, message } from 'antd'
import { useNavigate } from 'react-router-dom'
import {
  UserOutlined,
  TeamOutlined,
  SettingOutlined,
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  UploadOutlined,
  DownloadOutlined,
  EyeOutlined,
  LockOutlined,
  UnlockOutlined,
} from '@ant-design/icons'
import { useAuthStore } from '@/store/authStore'
import { ROUTES } from '@/utils/constants'
import type { ColumnsType } from 'antd/es/table'

const { Title, Paragraph, Text } = Typography
const { Option } = Select
const { TabPane } = Tabs

interface User {
  id: string
  username: string
  email: string
  role: 'student' | 'teacher' | 'enterprise' | 'admin'
  status: 'active' | 'inactive' | 'suspended'
  createdAt: string
  lastLogin?: string
  avatar?: string
  realName?: string
  phone?: string
  department?: string
}

interface Role {
  id: string
  name: string
  displayName: string
  description: string
  permissions: string[]
  userCount: number
  isSystem: boolean
}

const AdminPage: React.FC = () => {
  const navigate = useNavigate()
  const { user } = useAuthStore()
  const [activeTab, setActiveTab] = useState('users')
  const [isUserModalVisible, setIsUserModalVisible] = useState(false)
  const [isRoleModalVisible, setIsRoleModalVisible] = useState(false)
  const [editingUser, setEditingUser] = useState<User | null>(null)
  const [editingRole, setEditingRole] = useState<Role | null>(null)
  const [userForm] = Form.useForm()
  const [roleForm] = Form.useForm()

  // 模拟用户数据
  const [users] = useState<User[]>([
    {
      id: '1',
      username: 'student001',
      email: '<EMAIL>',
      role: 'student',
      status: 'active',
      createdAt: '2024-01-15',
      lastLogin: '2024-01-20',
      realName: '张三',
      phone: '13800138001',
      department: '会展设计系'
    },
    {
      id: '2',
      username: 'teacher001',
      email: '<EMAIL>',
      role: 'teacher',
      status: 'active',
      createdAt: '2024-01-10',
      lastLogin: '2024-01-20',
      realName: '李老师',
      phone: '13800138002',
      department: '会展设计系'
    },
    {
      id: '3',
      username: 'enterprise001',
      email: '<EMAIL>',
      role: 'enterprise',
      status: 'active',
      createdAt: '2024-01-12',
      lastLogin: '2024-01-19',
      realName: '王经理',
      phone: '13800138003',
      department: '某会展公司'
    }
  ])

  // 模拟角色数据
  const [roles] = useState<Role[]>([
    {
      id: '1',
      name: 'student',
      displayName: '学生',
      description: '学生用户，可以学习课程、参与项目、提交作业',
      permissions: ['view_courses', 'submit_assignments', 'join_projects', 'view_content'],
      userCount: 150,
      isSystem: true
    },
    {
      id: '2',
      name: 'teacher',
      displayName: '教师',
      description: '教师用户，可以管理课程、批改作业、指导项目',
      permissions: ['manage_courses', 'grade_assignments', 'manage_projects', 'view_reports'],
      userCount: 25,
      isSystem: true
    },
    {
      id: '3',
      name: 'enterprise',
      displayName: '企业用户',
      description: '企业用户，可以发布项目、查看学生作品、参与评审',
      permissions: ['publish_projects', 'view_works', 'participate_review'],
      userCount: 10,
      isSystem: false
    },
    {
      id: '4',
      name: 'admin',
      displayName: '管理员',
      description: '系统管理员，拥有所有权限',
      permissions: ['all_permissions'],
      userCount: 3,
      isSystem: true
    }
  ])

  const getStatusColor = (status: string) => {
    const colorMap: Record<string, string> = {
      active: 'green',
      inactive: 'orange',
      suspended: 'red'
    }
    return colorMap[status] || 'default'
  }

  const getStatusText = (status: string) => {
    const textMap: Record<string, string> = {
      active: '正常',
      inactive: '未激活',
      suspended: '已停用'
    }
    return textMap[status] || status
  }

  const getRoleDisplayName = (role: string) => {
    const roleMap: Record<string, string> = {
      student: '学生',
      teacher: '教师',
      enterprise: '企业用户',
      admin: '管理员'
    }
    return roleMap[role] || role
  }

  const userColumns: ColumnsType<User> = [
    {
      title: '用户信息',
      key: 'userInfo',
      render: (_, record) => (
        <Space direction="vertical" size={0}>
          <Text strong>{record.realName || record.username}</Text>
          <Text type="secondary" style={{ fontSize: 12 }}>
            {record.email}
          </Text>
          <Text type="secondary" style={{ fontSize: 12 }}>
            {record.phone}
          </Text>
        </Space>
      )
    },
    {
      title: '角色',
      dataIndex: 'role',
      key: 'role',
      render: (role) => (
        <Tag color="blue">
          {getRoleDisplayName(role)}
        </Tag>
      )
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <Tag color={getStatusColor(status)}>
          {getStatusText(status)}
        </Tag>
      )
    },
    {
      title: '部门',
      dataIndex: 'department',
      key: 'department'
    },
    {
      title: '最后登录',
      dataIndex: 'lastLogin',
      key: 'lastLogin'
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space>
          <Button 
            type="link" 
            icon={<EditOutlined />}
            onClick={() => handleEditUser(record)}
          >
            编辑
          </Button>
          <Button 
            type="link" 
            icon={record.status === 'active' ? <LockOutlined /> : <UnlockOutlined />}
            onClick={() => handleToggleUserStatus(record)}
          >
            {record.status === 'active' ? '停用' : '启用'}
          </Button>
          <Button 
            type="link" 
            danger
            icon={<DeleteOutlined />}
            onClick={() => handleDeleteUser(record)}
          >
            删除
          </Button>
        </Space>
      )
    }
  ]

  const roleColumns: ColumnsType<Role> = [
    {
      title: '角色名称',
      key: 'roleName',
      render: (_, record) => (
        <Space direction="vertical" size={0}>
          <Text strong>{record.displayName}</Text>
          <Text type="secondary" style={{ fontSize: 12 }}>
            {record.name}
          </Text>
        </Space>
      )
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description'
    },
    {
      title: '用户数量',
      dataIndex: 'userCount',
      key: 'userCount',
      render: (count) => (
        <Tag color="blue">{count}人</Tag>
      )
    },
    {
      title: '系统角色',
      dataIndex: 'isSystem',
      key: 'isSystem',
      render: (isSystem) => (
        <Tag color={isSystem ? 'red' : 'green'}>
          {isSystem ? '系统角色' : '自定义角色'}
        </Tag>
      )
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space>
          <Button 
            type="link" 
            icon={<EyeOutlined />}
            onClick={() => handleViewRole(record)}
          >
            查看权限
          </Button>
          {!record.isSystem && (
            <>
              <Button 
                type="link" 
                icon={<EditOutlined />}
                onClick={() => handleEditRole(record)}
              >
                编辑
              </Button>
              <Button 
                type="link" 
                danger
                icon={<DeleteOutlined />}
                onClick={() => handleDeleteRole(record)}
              >
                删除
              </Button>
            </>
          )}
        </Space>
      )
    }
  ]

  const handleEditUser = (user: User) => {
    setEditingUser(user)
    userForm.setFieldsValue(user)
    setIsUserModalVisible(true)
  }

  const handleEditRole = (role: Role) => {
    setEditingRole(role)
    roleForm.setFieldsValue(role)
    setIsRoleModalVisible(true)
  }

  const handleViewRole = (role: Role) => {
    Modal.info({
      title: `${role.displayName} 权限详情`,
      content: (
        <div>
          <p><strong>角色描述：</strong>{role.description}</p>
          <p><strong>权限列表：</strong></p>
          <ul>
            {role.permissions.map((permission, index) => (
              <li key={index}>{permission}</li>
            ))}
          </ul>
        </div>
      ),
      width: 500
    })
  }

  const handleToggleUserStatus = (user: User) => {
    const newStatus = user.status === 'active' ? 'suspended' : 'active'
    console.log(`切换用户 ${user.username} 状态为 ${newStatus}`)
    message.success(`用户状态已更新为${getStatusText(newStatus)}`)
  }

  const handleDeleteUser = (user: User) => {
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除用户 ${user.realName || user.username} 吗？此操作不可恢复。`,
      onOk: () => {
        console.log('删除用户:', user.id)
        message.success('用户删除成功')
      }
    })
  }

  const handleDeleteRole = (role: Role) => {
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除角色 ${role.displayName} 吗？此操作不可恢复。`,
      onOk: () => {
        console.log('删除角色:', role.id)
        message.success('角色删除成功')
      }
    })
  }

  const handleCreateUser = async (values: any) => {
    console.log('创建用户:', values)
    setIsUserModalVisible(false)
    userForm.resetFields()
    setEditingUser(null)
    message.success('用户创建成功')
  }

  const handleCreateRole = async (values: any) => {
    console.log('创建角色:', values)
    setIsRoleModalVisible(false)
    roleForm.resetFields()
    setEditingRole(null)
    message.success('角色创建成功')
  }

  const handleBatchImport = (info: any) => {
    if (info.file.status === 'done') {
      message.success('批量导入成功')
    } else if (info.file.status === 'error') {
      message.error('批量导入失败')
    }
  }

  return (
    <div style={{ padding: '24px' }}>
      {/* 页面标题 */}
      <div style={{ marginBottom: 24 }}>
        <Title level={2}>系统管理</Title>
        <Paragraph>
          用户权限管理、系统配置和运维监控
        </Paragraph>
      </div>

      {/* 统计数据 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="总用户数"
              value={users.length}
              prefix={<UserOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="活跃用户"
              value={users.filter(u => u.status === 'active').length}
              prefix={<TeamOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="角色数量"
              value={roles.length}
              prefix={<SettingOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="今日登录"
              value={12}
              prefix={<UserOutlined />}
              valueStyle={{ color: '#fa8c16' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 主要内容 */}
      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane tab="用户管理" key="users">
          <Card
            title="用户列表"
            extra={
              <Space>
                <Upload
                  accept=".xlsx,.xls"
                  showUploadList={false}
                  onChange={handleBatchImport}
                >
                  <Button icon={<UploadOutlined />}>
                    批量导入
                  </Button>
                </Upload>
                <Button icon={<DownloadOutlined />}>
                  导出用户
                </Button>
                <Button 
                  type="primary" 
                  icon={<PlusOutlined />}
                  onClick={() => {
                    setEditingUser(null)
                    userForm.resetFields()
                    setIsUserModalVisible(true)
                  }}
                >
                  新增用户
                </Button>
              </Space>
            }
          >
            <Table
              columns={userColumns}
              dataSource={users}
              rowKey="id"
              pagination={{ pageSize: 10 }}
            />
          </Card>
        </TabPane>

        <TabPane tab="角色权限" key="roles">
          <Card
            title="角色列表"
            extra={
              <Button 
                type="primary" 
                icon={<PlusOutlined />}
                onClick={() => {
                  setEditingRole(null)
                  roleForm.resetFields()
                  setIsRoleModalVisible(true)
                }}
              >
                新增角色
              </Button>
            }
          >
            <Table
              columns={roleColumns}
              dataSource={roles}
              rowKey="id"
              pagination={{ pageSize: 10 }}
            />
          </Card>
        </TabPane>
      </Tabs>

      {/* 用户编辑模态框 */}
      <Modal
        title={editingUser ? '编辑用户' : '新增用户'}
        open={isUserModalVisible}
        onOk={() => userForm.submit()}
        onCancel={() => {
          setIsUserModalVisible(false)
          setEditingUser(null)
          userForm.resetFields()
        }}
        width={600}
      >
        <Form
          form={userForm}
          layout="vertical"
          onFinish={handleCreateUser}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="username"
                label="用户名"
                rules={[{ required: true, message: '请输入用户名' }]}
              >
                <Input placeholder="请输入用户名" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="realName"
                label="真实姓名"
                rules={[{ required: true, message: '请输入真实姓名' }]}
              >
                <Input placeholder="请输入真实姓名" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="email"
                label="邮箱"
                rules={[
                  { required: true, message: '请输入邮箱' },
                  { type: 'email', message: '请输入有效的邮箱地址' }
                ]}
              >
                <Input placeholder="请输入邮箱" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="phone"
                label="手机号"
                rules={[{ required: true, message: '请输入手机号' }]}
              >
                <Input placeholder="请输入手机号" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="role"
                label="角色"
                rules={[{ required: true, message: '请选择角色' }]}
              >
                <Select placeholder="请选择角色">
                  <Option value="student">学生</Option>
                  <Option value="teacher">教师</Option>
                  <Option value="enterprise">企业用户</Option>
                  <Option value="admin">管理员</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="department"
                label="部门"
              >
                <Input placeholder="请输入部门" />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="status"
            label="状态"
            valuePropName="checked"
          >
            <Switch checkedChildren="启用" unCheckedChildren="禁用" />
          </Form.Item>
        </Form>
      </Modal>

      {/* 角色编辑模态框 */}
      <Modal
        title={editingRole ? '编辑角色' : '新增角色'}
        open={isRoleModalVisible}
        onOk={() => roleForm.submit()}
        onCancel={() => {
          setIsRoleModalVisible(false)
          setEditingRole(null)
          roleForm.resetFields()
        }}
        width={600}
      >
        <Form
          form={roleForm}
          layout="vertical"
          onFinish={handleCreateRole}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="name"
                label="角色标识"
                rules={[{ required: true, message: '请输入角色标识' }]}
              >
                <Input placeholder="请输入角色标识（英文）" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="displayName"
                label="显示名称"
                rules={[{ required: true, message: '请输入显示名称' }]}
              >
                <Input placeholder="请输入显示名称" />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="description"
            label="角色描述"
            rules={[{ required: true, message: '请输入角色描述' }]}
          >
            <Input.TextArea rows={3} placeholder="请输入角色描述" />
          </Form.Item>

          <Form.Item
            name="permissions"
            label="权限配置"
            rules={[{ required: true, message: '请选择权限' }]}
          >
            <Select
              mode="multiple"
              placeholder="请选择权限"
              style={{ width: '100%' }}
            >
              <Option value="view_courses">查看课程</Option>
              <Option value="manage_courses">管理课程</Option>
              <Option value="submit_assignments">提交作业</Option>
              <Option value="grade_assignments">批改作业</Option>
              <Option value="join_projects">参与项目</Option>
              <Option value="manage_projects">管理项目</Option>
              <Option value="view_content">查看内容</Option>
              <Option value="publish_content">发布内容</Option>
              <Option value="view_reports">查看报告</Option>
              <Option value="manage_users">管理用户</Option>
              <Option value="system_settings">系统设置</Option>
            </Select>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}

export default AdminPage
