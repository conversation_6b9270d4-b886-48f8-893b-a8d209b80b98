// 用户相关类型
export interface User {
  id: string
  username: string
  email: string
  role: UserRole
  avatar?: string
  createdAt: string
  updatedAt: string
}

export enum UserRole {
  STUDENT = 'student',
  TEACHER = 'teacher',
  ENTERPRISE = 'enterprise',
  ADMIN = 'admin'
}

// 项目相关类型
export interface Project {
  id: string
  name: string
  description: string
  type: ProjectType
  status: ProjectStatus
  createdBy: string
  participants: string[]
  createdAt: string
  updatedAt: string
}

export enum ProjectType {
  EXHIBITION = 'exhibition',
  CONFERENCE = 'conference',
  TRADE_SHOW = 'trade_show',
  CULTURAL_EVENT = 'cultural_event'
}

export enum ProjectStatus {
  DRAFT = 'draft',
  PLANNING = 'planning',
  DESIGN = 'design',
  CONSTRUCTION = 'construction',
  COMPLETED = 'completed'
}

// AI方案生成相关类型
export interface ExhibitionPlan {
  id: string
  projectId: string
  name: string
  layout: LayoutData
  booths: Booth[]
  flowPaths: FlowPath[]
  generatedAt: string
}

export interface LayoutData {
  width: number
  height: number
  scale: number
  areas: Area[]
}

export interface Area {
  id: string
  name: string
  type: AreaType
  coordinates: Coordinate[]
  properties: Record<string, any>
}

export enum AreaType {
  EXHIBITION = 'exhibition',
  ENTRANCE = 'entrance',
  EXIT = 'exit',
  RESTAURANT = 'restaurant',
  RESTROOM = 'restroom',
  STORAGE = 'storage'
}

export interface Booth {
  id: string
  name: string
  size: Size
  position: Position
  type: BoothType
  exhibitor?: string
}

export interface Size {
  width: number
  height: number
}

export interface Position {
  x: number
  y: number
  rotation?: number
}

export enum BoothType {
  STANDARD = 'standard',
  CORNER = 'corner',
  ISLAND = 'island',
  PENINSULA = 'peninsula'
}

export interface FlowPath {
  id: string
  name: string
  points: Coordinate[]
  width: number
  type: PathType
}

export interface Coordinate {
  x: number
  y: number
}

export enum PathType {
  MAIN = 'main',
  SECONDARY = 'secondary',
  EMERGENCY = 'emergency'
}

// 2D效果图相关类型
export interface RenderRequest {
  id: string
  projectId: string
  planId: string
  viewAngle: ViewAngle
  scene: SceneType
  style: RenderStyle
  status: RenderStatus
  createdAt: string
}

export enum ViewAngle {
  TOP = 'top',
  PERSPECTIVE = 'perspective',
  FRONT = 'front',
  SIDE = 'side'
}

export enum SceneType {
  EMPTY = 'empty',
  SETUP = 'setup',
  EXHIBITION = 'exhibition',
  BREAKDOWN = 'breakdown'
}

export enum RenderStyle {
  REALISTIC = 'realistic',
  ARCHITECTURAL = 'architectural',
  CONCEPTUAL = 'conceptual'
}

export enum RenderStatus {
  PENDING = 'pending',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  FAILED = 'failed'
}

// API响应类型
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  message?: string
  error?: string
}

export interface PaginatedResponse<T> {
  items: T[]
  total: number
  page: number
  pageSize: number
  totalPages: number
}
