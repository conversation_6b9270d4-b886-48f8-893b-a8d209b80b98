# 🎬 MCTT平台演示指南

## 🚀 快速启动演示

### 1. 安装依赖
```bash
npm install
```

### 2. 启动开发服务器
```bash
npm run dev
```

### 3. 访问演示
打开浏览器访问：`http://localhost:3000`

## 🎭 演示账号

| 用户名 | 密码 | 角色 | 权限说明 |
|--------|------|------|----------|
| student1 | 123456 | 学生 | 基础功能使用权限 |
| teacher1 | 123456 | 教师 | 教学监控和管理权限 |
| enterprise1 | 123456 | 企业 | 完整项目管理权限 |

## 🎯 演示功能

### 1. 用户登录系统
- ✅ 多角色登录（学生/教师/企业）
- ✅ 权限管理和路由保护
- ✅ 用户状态持久化

### 2. AI方案生成模块
- ✅ 智能需求配置界面
- ✅ AI方案生成模拟（3秒生成时间）
- ✅ 方案可视化预览
- ✅ 展位和通道规划展示

### 3. 2D效果图生成
- ✅ 多视角选择（俯视图、透视图、正视图、侧视图）
- ✅ 多场景类型（空场景、搭建中、展览中、撤展中）
- ✅ 多渲染风格（写实、建筑、概念）
- ✅ 渲染进度模拟和结果展示

### 4. 响应式布局
- ✅ 现代化侧边栏导航
- ✅ 用户信息显示和退出功能
- ✅ 实时统计数据展示

## 🎨 演示亮点

### 视觉效果
- 🎨 现代化UI设计，基于Ant Design
- 📱 完全响应式布局
- 🌈 渐变背景和卡片阴影效果
- ⚡ 流畅的加载动画和进度条

### 交互体验
- 🔄 实时数据更新
- 📊 动态统计图表
- 🎯 智能路由导航
- 💫 平滑的页面切换

### 功能完整性
- 🔐 完整的用户认证流程
- 🎛️ 丰富的配置选项
- 📈 实时进度反馈
- 💾 状态持久化存储

## 🎪 演示流程建议

### 完整演示流程（10-15分钟）

1. **登录展示（2分钟）**
   - 展示登录页面设计
   - 演示不同角色登录
   - 说明权限管理机制

2. **首页概览（2分钟）**
   - 展示实时统计数据
   - 介绍功能模块布局
   - 演示导航交互

3. **AI方案生成（4分钟）**
   - 配置展览需求参数
   - 观看AI生成过程动画
   - 查看生成的方案预览
   - 展示展位和通道规划

4. **2D效果图生成（4分钟）**
   - 选择不同视角和场景
   - 观看渲染进度动画
   - 查看生成的效果图
   - 演示图片预览功能

5. **系统特性展示（3分钟）**
   - 展示响应式设计
   - 演示用户权限切换
   - 说明技术架构优势

### 快速演示流程（5分钟）

1. **登录** → 使用teacher1账号登录
2. **首页** → 快速浏览统计数据和功能模块
3. **AI方案** → 快速配置并生成一个方案
4. **2D渲染** → 选择透视图+展览中场景进行渲染
5. **总结** → 强调AI技术集成和用户体验

## 🔧 技术特色

### 前端技术栈
- ⚛️ React 18 + TypeScript
- ⚡ Vite 构建工具
- 🎨 Ant Design UI组件库
- 🗂️ Zustand 状态管理
- 🛣️ React Router 路由管理

### 开发规范
- 📁 标准化目录结构
- 🔒 TypeScript 类型安全
- 🎯 模块化组件设计
- 📦 Mock数据服务
- 🔄 状态持久化

### AI集成准备
- 🤖 AI服务接口设计
- 📊 实时进度反馈
- 🎨 结果可视化展示
- ⚙️ 灵活的参数配置

## 📝 演示要点

### 强调优势
1. **用户体验** - 直观的界面设计和流畅的交互
2. **技术先进** - 现代化技术栈和AI集成
3. **功能完整** - 覆盖会展项目全流程
4. **扩展性强** - 模块化设计便于功能扩展

### 解答常见问题
- **Q: AI生成的准确性如何？**
  A: 演示版本使用模拟数据，实际版本将集成真实AI模型

- **Q: 支持哪些文件格式？**
  A: 支持常见的图片格式和PDF导出

- **Q: 是否支持移动端？**
  A: 完全响应式设计，支持各种设备

- **Q: 数据安全如何保障？**
  A: 采用现代化安全架构和权限管理

## 🎉 演示结语

这个演示展示了MCTT会展实训平台的核心功能和技术能力。通过AI技术的集成，我们为会展行业提供了一个创新的数字化解决方案，不仅提升了工作效率，也为教学和培训提供了强有力的支持。

**下一步计划：**
- 集成真实AI模型
- 完善3D建模功能
- 添加协作和分享功能
- 优化性能和用户体验
