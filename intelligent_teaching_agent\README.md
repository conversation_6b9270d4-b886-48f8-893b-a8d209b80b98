# 智能教学代理系统

## 📖 项目简介

智能教学代理系统是一个基于 Streamlit 的多页面桌面应用程序，专为会展行业虚实融合实训教学设计。系统包含两个核心功能模块：客户需求预测和供应商选择，通过交互式操作帮助师生掌握数据分析和决策支持的核心概念。

## 🎯 功能特色

### 📈 客户需求预测模块
- **时间序列预测**: 使用Facebook Prophet模型进行销售预测
- **多场景数据**: 提供默认、旺季、增长型、稳定型四种数据集
- **交互式可视化**: Plotly图表支持缩放、悬停等交互操作
- **成分分解分析**: 展示趋势、年季节性、周季节性等影响因素
- **性能评估**: 计算MAE、RMSE、MAPE等评估指标

### 🏭 供应商选择模块
- **AHP层次分析**: 通过成对比较确定评估标准权重
- **TOPSIS决策**: 理想解排序技术进行供应商评估
- **语义化界面**: 直观的滑块操作，无需复杂数学计算
- **雷达图对比**: 可视化供应商能力多维度对比
- **一致性检验**: 确保决策判断的逻辑一致性

## 🚀 快速开始

### 环境要求
- Python 3.8+
- 推荐使用虚拟环境

### 安装步骤

1. **克隆或下载项目**
   ```bash
   # 如果是从git仓库克隆
   git clone <repository-url>
   cd intelligent_teaching_agent
   
   # 或者直接下载解压到目标目录
   ```

2. **创建虚拟环境（推荐）**
   ```bash
   python -m venv venv
   
   # Windows
   venv\Scripts\activate
   
   # macOS/Linux
   source venv/bin/activate
   ```

3. **安装依赖包**
   ```bash
   pip install -r requirements.txt
   ```

4. **启动应用**
   ```bash
   streamlit run app.py
   ```

5. **访问应用**
   - 浏览器会自动打开 `http://localhost:8501`
   - 如果没有自动打开，请手动访问该地址

## 📁 项目结构

```
intelligent_teaching_agent/
├── app.py                          # 主应用入口（欢迎页面）
├── pages/                          # 页面目录
│   ├── 1_📈_Demand_Forecasting.py  # 客户需求预测模块
│   └── 2_🏭_Supplier_Selection.py  # 供应商选择模块
├── requirements.txt                # 依赖包列表
├── README.md                      # 项目说明文档
└── run.bat                        # Windows启动脚本
```

## 🎓 使用指南

### 客户需求预测模块使用流程

1. **选择数据集**: 从侧边栏选择一个预置的销售数据集
2. **设置参数**: 配置预测周期和历史数据长度
3. **运行预测**: 点击"运行预测"按钮开始分析
4. **查看结果**: 通过选项卡查看预测图表、成分分解和性能指标
5. **交互探索**: 使用鼠标操作深入探索预测结果

### 供应商选择模块使用流程

1. **权重设置**: 在侧边栏进行三组成对比较
2. **开始评估**: 点击"开始评估供应商"按钮
3. **查看排名**: 分析供应商排名报告和推荐结果
4. **能力对比**: 通过雷达图比较供应商多维能力
5. **决策分析**: 查看权重敏感性和一致性检验结果

## 🔧 技术架构

### 核心技术栈
- **前端框架**: Streamlit
- **数据处理**: Pandas, NumPy
- **可视化**: Plotly
- **预测模型**: Facebook Prophet
- **决策分析**: AHP + TOPSIS

### 算法说明

#### Prophet时间序列预测
- 自动处理季节性和趋势
- 支持节假日效应
- 提供置信区间
- 适合业务时间序列数据

#### AHP层次分析法
- 成对比较确定权重
- 一致性检验确保逻辑性
- 将主观判断量化

#### TOPSIS决策方法
- 基于理想解的排序技术
- 考虑正负理想解距离
- 适合多标准决策问题

## 🎯 教学目标

### 学习成果
学生通过使用本系统将能够：

1. **掌握时间序列预测基础知识**
   - 理解时间序列数据特点
   - 学会使用Prophet模型
   - 掌握预测结果解读

2. **学习多标准决策分析方法**
   - 理解AHP层次分析法原理
   - 掌握TOPSIS决策技术
   - 培养科学决策思维

3. **提升数据分析能力**
   - 学会数据可视化技巧
   - 掌握模型性能评估
   - 培养批判性思维

### 适用场景
- 会展行业实训教学
- 数据分析课程
- 决策支持系统教学
- 商业智能培训

## 🛠️ 故障排除

### 常见问题

1. **Prophet安装失败**
   ```bash
   # 如果pip安装失败，尝试conda安装
   conda install -c conda-forge prophet
   ```

2. **端口被占用**
   ```bash
   # 指定其他端口启动
   streamlit run app.py --server.port 8502
   ```

3. **依赖包冲突**
   ```bash
   # 使用虚拟环境避免冲突
   python -m venv fresh_env
   fresh_env\Scripts\activate  # Windows
   pip install -r requirements.txt
   ```

### 性能优化

- 使用缓存装饰器 `@st.cache_data` 优化数据加载
- 避免在主循环中进行重复计算
- 合理设置历史数据长度

## 📞 技术支持

如遇到技术问题，请：

1. 检查Python版本和依赖包版本
2. 查看控制台错误信息
3. 参考Streamlit官方文档
4. 联系开发团队获取支持

## 📄 许可证

本项目仅供教学使用，请勿用于商业用途。

---

**开发团队**: 会展行业虚实融合实训教学平台项目组  
**更新时间**: 2024年12月  
**版本**: v1.0.0
