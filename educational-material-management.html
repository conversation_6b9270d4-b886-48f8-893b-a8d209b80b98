<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>教学材料库管理 - 职业院校实训版</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
            background-color: #f5f5f5;
            margin: 0;
            padding: 24px;
            line-height: 1.6;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .header {
            background: white;
            padding: 24px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 24px;
        }
        .title {
            font-size: 28px;
            color: #1890ff;
            margin-bottom: 8px;
        }
        .subtitle {
            color: #666;
            font-size: 16px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 16px;
            margin-bottom: 24px;
        }
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            text-align: center;
        }
        .stat-number {
            font-size: 32px;
            font-weight: bold;
            margin-bottom: 8px;
        }
        .stat-label {
            color: #666;
            font-size: 14px;
        }
        .main-content {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .content-header {
            padding: 20px 24px;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            justify-content: between;
            align-items: center;
        }
        .content-title {
            font-size: 18px;
            font-weight: 600;
            margin: 0;
        }
        .table-container {
            overflow-x: auto;
        }
        .material-table {
            width: 100%;
            border-collapse: collapse;
        }
        .material-table th,
        .material-table td {
            padding: 12px 16px;
            text-align: left;
            border-bottom: 1px solid #f0f0f0;
        }
        .material-table th {
            background: #fafafa;
            font-weight: 600;
            color: #262626;
        }
        .material-table tr:hover {
            background: #f5f5f5;
        }
        .tag {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
            margin-right: 4px;
        }
        .tag-blue { background: #e6f7ff; color: #1890ff; }
        .tag-green { background: #f6ffed; color: #52c41a; }
        .tag-orange { background: #fff7e6; color: #fa8c16; }
        .tag-purple { background: #f9f0ff; color: #722ed1; }
        .price {
            font-weight: bold;
            color: #f5222d;
            font-size: 16px;
        }
        .price-note {
            font-size: 12px;
            color: #52c41a;
            margin-top: 2px;
        }
        .usage-stats {
            text-align: center;
        }
        .usage-count {
            font-size: 14px;
            margin-bottom: 4px;
        }
        .total-cost {
            font-weight: bold;
            color: #722ed1;
            font-size: 14px;
        }
        .optimization-note {
            background: #e6f7ff;
            border: 1px solid #91d5ff;
            border-radius: 6px;
            padding: 16px;
            margin: 24px 0;
        }
        .optimization-title {
            font-weight: 600;
            color: #0958d9;
            margin-bottom: 8px;
        }
        .removed-features {
            background: #fff2e8;
            border: 1px solid #ffbb96;
            border-radius: 6px;
            padding: 16px;
            margin: 16px 0;
        }
        .removed-title {
            font-weight: 600;
            color: #d4380d;
            margin-bottom: 8px;
        }
        .feature-list {
            margin: 8px 0;
            padding-left: 20px;
        }
        .feature-list li {
            margin: 4px 0;
        }
        .educational-focus {
            background: #f6ffed;
            border: 1px solid #b7eb8f;
            border-radius: 6px;
            padding: 16px;
            margin: 16px 0;
        }
        .educational-title {
            font-weight: 600;
            color: #389e0d;
            margin-bottom: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 页面标题 -->
        <div class="header">
            <h1 class="title">📚 教学材料库管理</h1>
            <p class="subtitle">
                专为职业院校实训设计的材料管理系统，专注于教学用途，提供材料信息和参考价格
            </p>
        </div>

        <!-- 优化说明 -->
        <div class="optimization-note">
            <div class="optimization-title">🎯 职业院校实训优化</div>
            <p>根据职业院校实训教学的特点，我们对材料管理系统进行了专门优化，移除了商业采购相关功能，专注于教学核心需求。</p>
        </div>

        <!-- 移除的功能 -->
        <div class="removed-features">
            <div class="removed-title">❌ 已移除的商业功能</div>
            <ul class="feature-list">
                <li><strong>供应商管理</strong> - 供应商名称、联系方式（教学不需要真实采购）</li>
                <li><strong>库存管理</strong> - 库存数量、库存价值统计（教学环境无需库存控制）</li>
                <li><strong>采购信息</strong> - 最小起订量、交货周期（教学不涉及真实采购）</li>
                <li><strong>成本统计</strong> - 总成本字段（避免过度关注金钱，专注学习过程）</li>
                <li><strong>低库存预警</strong> - 库存不足提醒（教学环境不适用）</li>
            </ul>
        </div>

        <!-- 保留的教学功能 -->
        <div class="educational-focus">
            <div class="educational-title">✅ 保留的教学核心功能</div>
            <ul class="feature-list">
                <li><strong>材料基本信息</strong> - 名称、分类、品牌、型号、规格</li>
                <li><strong>教学参考价格</strong> - 用于成本计算和预算控制教学</li>
                <li><strong>材料描述</strong> - 详细的材料特性和应用说明</li>
                <li><strong>使用统计</strong> - 在3D编辑器中的使用次数统计</li>
                <li><strong>材料标签</strong> - 环保、防火、防水等特性标签</li>
                <li><strong>3D编辑器联动</strong> - 自动成本计算和材料选择</li>
            </ul>
        </div>

        <!-- 统计数据 -->
        <div style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 16px; margin-bottom: 24px;">
            <div class="stat-card">
                <div class="stat-number" style="color: #1890ff;">3</div>
                <div class="stat-label">材料总数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" style="color: #52c41a;">6</div>
                <div class="stat-label">材料分类</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" style="color: #fa8c16;">55</div>
                <div class="stat-label">本月使用次数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" style="color: #722ed1;">2</div>
                <div class="stat-label">热门材料</div>
            </div>
        </div>

        <!-- 材料列表 -->
        <div class="main-content">
            <div class="content-header">
                <h3 class="content-title">教学材料库</h3>
            </div>
            <div class="table-container">
                <table class="material-table">
                    <thead>
                        <tr>
                            <th>材料信息</th>
                            <th>分类</th>
                            <th>价格信息</th>
                            <th>使用情况</th>
                            <th>状态</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>
                                <div style="font-weight: 600; margin-bottom: 4px;">铝合金展台桁架</div>
                                <div style="font-size: 12px; color: #666;">展通 ZT-300</div>
                                <div style="font-size: 12px; color: #666;">规格：300x300x3000mm</div>
                            </td>
                            <td>
                                <span class="tag tag-blue">结构件</span>
                                <div style="font-size: 12px; margin-top: 4px;">桁架系统</div>
                            </td>
                            <td>
                                <div class="price">¥280.00/件</div>
                                <div class="price-note">教学参考价格</div>
                            </td>
                            <td class="usage-stats">
                                <div class="usage-count">使用：25次</div>
                                <div style="font-size: 12px; color: #666;">教学使用统计</div>
                            </td>
                            <td>
                                <span class="tag tag-green">启用</span>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <div style="font-weight: 600; margin-bottom: 4px;">LED射灯</div>
                                <div style="font-size: 12px; color: #666;">飞利浦 PH-LED-50W</div>
                                <div style="font-size: 12px; color: #666;">规格：50W 3000K</div>
                            </td>
                            <td>
                                <span class="tag tag-orange">照明设备</span>
                                <div style="font-size: 12px; margin-top: 4px;">射灯</div>
                            </td>
                            <td>
                                <div class="price">¥120.00/件</div>
                                <div class="price-note">教学参考价格</div>
                            </td>
                            <td class="usage-stats">
                                <div class="usage-count">使用：18次</div>
                                <div style="font-size: 12px; color: #666;">教学使用统计</div>
                            </td>
                            <td>
                                <span class="tag tag-green">启用</span>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <div style="font-weight: 600; margin-bottom: 4px;">亚克力展示柜</div>
                                <div style="font-size: 12px; color: #666;">晶彩 JC-800</div>
                                <div style="font-size: 12px; color: #666;">规格：800x400x1200mm</div>
                            </td>
                            <td>
                                <span class="tag tag-purple">展具家具</span>
                                <div style="font-size: 12px; margin-top: 4px;">展示柜</div>
                            </td>
                            <td>
                                <div class="price">¥450.00/件</div>
                                <div class="price-note">教学参考价格</div>
                            </td>
                            <td class="usage-stats">
                                <div class="usage-count">使用：12次</div>
                                <div style="font-size: 12px; color: #666;">教学使用统计</div>
                            </td>
                            <td>
                                <span class="tag tag-green">启用</span>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- 教学应用说明 -->
        <div style="background: white; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); padding: 24px; margin-top: 24px;">
            <h3 style="color: #1890ff; margin-bottom: 16px;">🎓 教学应用场景</h3>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">
                <div>
                    <h4 style="color: #52c41a;">💡 价格认知教学</h4>
                    <ul style="color: #666; margin: 0; padding-left: 20px;">
                        <li>学生了解不同材料的参考价格</li>
                        <li>培养学生的成本意识</li>
                        <li>比较不同材料的价格差异</li>
                        <li>学习合理的材料选择方法</li>
                    </ul>
                </div>
                <div>
                    <h4 style="color: #fa8c16;">🔧 材料特性学习</h4>
                    <ul style="color: #666; margin: 0; padding-left: 20px;">
                        <li>通过详细描述了解材料特性</li>
                        <li>学习材料的适用场景</li>
                        <li>掌握材料的优缺点</li>
                        <li>培养材料选择能力</li>
                    </ul>
                </div>
                <div>
                    <h4 style="color: #722ed1;">📊 使用统计分析</h4>
                    <ul style="color: #666; margin: 0; padding-left: 20px;">
                        <li>分析材料使用频率</li>
                        <li>统计材料使用次数</li>
                        <li>比较不同材料的受欢迎程度</li>
                        <li>制作材料使用分析报告</li>
                    </ul>
                </div>
                <div>
                    <h4 style="color: #13c2c2;">🎯 实践技能培养</h4>
                    <ul style="color: #666; margin: 0; padding-left: 20px;">
                        <li>在虚拟环境中进行材料选择</li>
                        <li>体验真实的设计流程</li>
                        <li>培养实际工作能力</li>
                        <li>提升职业素养</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 总结 -->
        <div style="background: #f6ffed; border: 1px solid #b7eb8f; border-radius: 6px; padding: 20px; margin-top: 24px; text-align: center;">
            <h3 style="color: #389e0d; margin-bottom: 12px;">✅ 优化完成</h3>
            <p style="margin: 0; color: #666;">
                教学材料库已成功优化，专注于职业院校实训教学需求。
                移除了商业采购功能，保留了教学核心功能，更适合教学环境使用。
            </p>
        </div>
    </div>
</body>
</html>
