import React, { useState, useEffect } from 'react'
import {
  Card,
  Tabs,
  Row,
  Col,
  Statistic,
  Button,
  Space,
  Typography,
  Tag,
  Progress,
  Select,
  DatePicker,
  Input,
  Table,
  Modal,
  Form,
  message,
  Tooltip,
  Badge
} from 'antd'
import {
  PlusOutlined,
  CalendarOutlined,
  <PERSON><PERSON><PERSON>Outlined,
  TeamOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  EyeOutlined,
  EditOutlined,
  DeleteOutlined
} from '@ant-design/icons'
import Gantt<PERSON><PERSON> from '@/components/charts/GanttChart'
import ConstructionCharts from '@/components/charts/ConstructionCharts'
import type { ConstructionTask, ProjectSchedule, ConstructionStats, TaskFilter } from '@/types/construction'

const { Title, Paragraph } = Typography
const { TabPane } = Tabs
const { Option } = Select
const { RangePicker } = DatePicker

const ConstructionManagePage: React.FC = () => {
  const [activeTab, setActiveTab] = useState('overview')
  const [selectedProject, setSelectedProject] = useState<string>('project-1')
  const [taskFilter, setTaskFilter] = useState<TaskFilter>({})
  const [isTaskModalVisible, setIsTaskModalVisible] = useState(false)
  const [isDetailModalVisible, setIsDetailModalVisible] = useState(false)
  const [editingTask, setEditingTask] = useState<ConstructionTask | null>(null)
  const [viewingTask, setViewingTask] = useState<ConstructionTask | null>(null)
  const [userRole, setUserRole] = useState<'student' | 'teacher'>('teacher') // 模拟用户角色
  const [currentUserId] = useState('student-1') // 模拟当前用户ID
  const [form] = Form.useForm()

  // 模拟数据
  const [stats] = useState<ConstructionStats>({
    totalTasks: 24,
    completedTasks: 8,
    inProgressTasks: 12,
    delayedTasks: 4,
    overallProgress: 65,
    onTimeRate: 83,
    averageTaskDuration: 3.5,
    totalStudents: 15,
    activeStudents: 12
  })

  const [tasks, setTasks] = useState<ConstructionTask[]>([
    {
      id: 'task-1',
      name: '场地清理与准备',
      description: '清理施工现场，设置安全围挡，准备施工材料',
      category: 'preparation',
      priority: 'high',
      status: 'completed',
      progress: 100,
      startDate: '2024-01-15',
      endDate: '2024-01-17',
      actualStartDate: '2024-01-15',
      actualEndDate: '2024-01-17',
      estimatedDuration: 2,
      actualDuration: 2,
      assignedTo: ['student-1', 'student-2'],
      dependencies: [],
      materials: ['material-1', 'material-2'],
      notes: '按时完成，质量良好',
      createdBy: 'teacher-1',
      createdAt: '2024-01-10',
      updatedAt: '2024-01-17',
      projectId: 'project-1'
    },
    {
      id: 'task-2',
      name: '基础结构搭建',
      description: '搭建展台主体框架结构',
      category: 'structure',
      priority: 'high',
      status: 'in_progress',
      progress: 75,
      startDate: '2024-01-18',
      endDate: '2024-01-22',
      actualStartDate: '2024-01-18',
      estimatedDuration: 4,
      assignedTo: ['student-3', 'student-4', 'student-5'],
      dependencies: ['task-1'],
      materials: ['material-3', 'material-4'],
      notes: '进展顺利，预计按时完成',
      createdBy: 'teacher-1',
      createdAt: '2024-01-10',
      updatedAt: '2024-01-20',
      projectId: 'project-1'
    },
    {
      id: 'task-3',
      name: '电路安装',
      description: '安装展台照明和电源系统',
      category: 'installation',
      priority: 'medium',
      status: 'pending',
      progress: 0,
      startDate: '2024-01-23',
      endDate: '2024-01-25',
      estimatedDuration: 2,
      assignedTo: ['student-6', 'student-7'],
      dependencies: ['task-2'],
      materials: ['material-5', 'material-6'],
      notes: '等待前置任务完成',
      createdBy: 'teacher-1',
      createdAt: '2024-01-10',
      updatedAt: '2024-01-20',
      projectId: 'project-1'
    }
  ])

  const projects = [
    { id: 'project-1', name: '科技展览馆展台设计' },
    { id: 'project-2', name: '汽车展示厅布置' },
    { id: 'project-3', name: '文化艺术展览空间' }
  ]

  // 获取状态颜色
  const getStatusColor = (status: ConstructionTask['status']) => {
    const colors = {
      pending: 'default',
      in_progress: 'processing',
      completed: 'success',
      delayed: 'error',
      cancelled: 'default'
    }
    return colors[status]
  }

  // 获取优先级颜色
  const getPriorityColor = (priority: ConstructionTask['priority']) => {
    const colors = {
      low: 'green',
      medium: 'orange',
      high: 'red',
      urgent: 'purple'
    }
    return colors[priority]
  }

  // 表格列定义
  const taskColumns = [
    {
      title: '任务信息',
      key: 'taskInfo',
      render: (_, record: ConstructionTask) => (
        <div>
          <div style={{ fontWeight: 600, marginBottom: 4 }}>{record.name}</div>
          <div style={{ fontSize: 12, color: '#666', marginBottom: 4 }}>
            {record.description}
          </div>
          <Space size={4}>
            <Tag color={getPriorityColor(record.priority)}>
              {record.priority === 'low' ? '低' : 
               record.priority === 'medium' ? '中' :
               record.priority === 'high' ? '高' : '紧急'}
            </Tag>
            <Tag>
              {record.category === 'preparation' ? '准备' :
               record.category === 'structure' ? '结构' :
               record.category === 'decoration' ? '装饰' :
               record.category === 'installation' ? '安装' :
               record.category === 'finishing' ? '收尾' : '检验'}
            </Tag>
          </Space>
        </div>
      )
    },
    {
      title: '状态',
      key: 'status',
      render: (_, record: ConstructionTask) => (
        <Space direction="vertical" size={4}>
          <Badge 
            status={getStatusColor(record.status) as any}
            text={
              record.status === 'pending' ? '待开始' :
              record.status === 'in_progress' ? '进行中' :
              record.status === 'completed' ? '已完成' :
              record.status === 'delayed' ? '延期' : '已取消'
            }
          />
          <Progress 
            percent={record.progress} 
            size="small" 
            status={record.status === 'delayed' ? 'exception' : 'normal'}
          />
        </Space>
      )
    },
    {
      title: '时间安排',
      key: 'schedule',
      render: (_, record: ConstructionTask) => (
        <Space direction="vertical" size={0}>
          <div style={{ fontSize: 12 }}>
            计划：{record.startDate} ~ {record.endDate}
          </div>
          {record.actualStartDate && (
            <div style={{ fontSize: 12, color: '#666' }}>
              实际：{record.actualStartDate} ~ {record.actualEndDate || '进行中'}
            </div>
          )}
          <div style={{ fontSize: 12, color: '#666' }}>
            工期：{record.estimatedDuration}天
            {record.actualDuration && ` (实际${record.actualDuration}天)`}
          </div>
        </Space>
      )
    },
    {
      title: '分配人员',
      key: 'assignedTo',
      render: (_, record: ConstructionTask) => (
        <div>
          {record.assignedTo.map(studentId => (
            <Tag key={studentId} style={{ marginBottom: 2 }}>
              学生{studentId.split('-')[1]}
            </Tag>
          ))}
        </div>
      )
    },
    {
      title: '操作',
      key: 'actions',
      render: (_, record: ConstructionTask) => {
        const isAssignedToCurrentUser = record.assignedTo.includes(currentUserId)
        const canEdit = userRole === 'teacher' || (userRole === 'student' && isAssignedToCurrentUser)
        const canDelete = userRole === 'teacher'

        return (
          <Space>
            <Tooltip title="查看详情">
              <Button
                type="text"
                icon={<EyeOutlined />}
                size="small"
                onClick={() => handleViewTask(record)}
              />
            </Tooltip>
            {canEdit && (
              <Tooltip title={userRole === 'student' ? '更新进度' : '编辑任务'}>
                <Button
                  type="text"
                  icon={<EditOutlined />}
                  size="small"
                  onClick={() => handleEditTask(record)}
                />
              </Tooltip>
            )}
            {canDelete && (
              <Tooltip title="删除">
                <Button
                  type="text"
                  icon={<DeleteOutlined />}
                  size="small"
                  danger
                  onClick={() => handleDeleteTask(record.id)}
                />
              </Tooltip>
            )}
          </Space>
        )
      }
    }
  ]

  const handleViewTask = (task: ConstructionTask) => {
    setViewingTask(task)
    setIsDetailModalVisible(true)
  }

  const handleEditTask = (task: ConstructionTask) => {
    setEditingTask(task)
    form.setFieldsValue(task)
    setIsTaskModalVisible(true)
  }

  const handleDeleteTask = (taskId: string) => {
    Modal.confirm({
      title: '确认删除',
      content: '确定要删除这个任务吗？',
      onOk: () => {
        setTasks(tasks.filter(task => task.id !== taskId))
        message.success('任务删除成功')
      }
    })
  }

  const handleSaveTask = async (values: any) => {
    try {
      if (editingTask) {
        // 更新任务
        setTasks(tasks.map(task => 
          task.id === editingTask.id 
            ? { ...task, ...values, updatedAt: new Date().toISOString() }
            : task
        ))
        message.success('任务更新成功')
      } else {
        // 创建新任务
        const newTask: ConstructionTask = {
          ...values,
          id: `task-${Date.now()}`,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          createdBy: 'current-user',
          projectId: selectedProject
        }
        setTasks([...tasks, newTask])
        message.success('任务创建成功')
      }
      setIsTaskModalVisible(false)
      setEditingTask(null)
      form.resetFields()
    } catch (error) {
      message.error('操作失败')
    }
  }

  return (
    <div style={{ padding: 24 }}>
      {/* 页面标题 */}
      <div style={{ marginBottom: 24 }}>
        <Title level={2}>施工工序管理</Title>
        <Paragraph>
          管理项目施工进度，学生填写工序进展，老师监控项目状态
        </Paragraph>
      </div>

      {/* 项目选择和操作按钮 */}
      <Row justify="space-between" align="middle" style={{ marginBottom: 24 }}>
        <Col>
          <Space>
            <span>当前项目：</span>
            <Select
              value={selectedProject}
              onChange={setSelectedProject}
              style={{ width: 200 }}
            >
              {projects.map(project => (
                <Option key={project.id} value={project.id}>
                  {project.name}
                </Option>
              ))}
            </Select>
            <span style={{ marginLeft: 16 }}>当前角色：</span>
            <Select
              value={userRole}
              onChange={setUserRole}
              style={{ width: 100 }}
            >
              <Option value="teacher">老师</Option>
              <Option value="student">学生</Option>
            </Select>
          </Space>
        </Col>
        <Col>
          <Space>
            {userRole === 'teacher' && (
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => setIsTaskModalVisible(true)}
              >
                新建任务
              </Button>
            )}
            <Button
              icon={<CalendarOutlined />}
              onClick={() => setActiveTab('gantt')}
            >
              甘特图
            </Button>
            <Button
              icon={<BarChartOutlined />}
              onClick={() => setActiveTab('charts')}
            >
              统计图表
            </Button>
          </Space>
        </Col>
      </Row>

      {/* 统计数据卡片 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="总任务数"
              value={stats.totalTasks}
              suffix="个"
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="整体进度"
              value={stats.overallProgress}
              suffix="%"
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="按时完成率"
              value={stats.onTimeRate}
              suffix="%"
              valueStyle={{ color: '#fa8c16' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="参与学生"
              value={stats.activeStudents}
              suffix={`/${stats.totalStudents}`}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 主要内容区域 */}
      <Card>
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane tab="任务列表" key="tasks">
            {/* 筛选条件 */}
            <Row gutter={16} style={{ marginBottom: 16 }}>
              <Col span={6}>
                <Select
                  placeholder="状态筛选"
                  allowClear
                  style={{ width: '100%' }}
                  onChange={(value) => setTaskFilter({...taskFilter, status: value})}
                >
                  <Option value="pending">待开始</Option>
                  <Option value="in_progress">进行中</Option>
                  <Option value="completed">已完成</Option>
                  <Option value="delayed">延期</Option>
                </Select>
              </Col>
              <Col span={6}>
                <Select
                  placeholder="优先级筛选"
                  allowClear
                  style={{ width: '100%' }}
                  onChange={(value) => setTaskFilter({...taskFilter, priority: value})}
                >
                  <Option value="urgent">紧急</Option>
                  <Option value="high">高</Option>
                  <Option value="medium">中</Option>
                  <Option value="low">低</Option>
                </Select>
              </Col>
              <Col span={6}>
                <Select
                  placeholder="工序类型"
                  allowClear
                  style={{ width: '100%' }}
                  onChange={(value) => setTaskFilter({...taskFilter, category: value})}
                >
                  <Option value="preparation">准备</Option>
                  <Option value="structure">结构</Option>
                  <Option value="decoration">装饰</Option>
                  <Option value="installation">安装</Option>
                  <Option value="finishing">收尾</Option>
                  <Option value="inspection">检验</Option>
                </Select>
              </Col>
              <Col span={6}>
                <Input.Search
                  placeholder="搜索任务名称"
                  allowClear
                  onSearch={(value) => console.log('搜索:', value)}
                />
              </Col>
            </Row>

            {/* 任务表格 */}
            <Table
              columns={taskColumns}
              dataSource={tasks}
              rowKey="id"
              pagination={{
                pageSize: 10,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total) => `共 ${total} 条记录`
              }}
            />
          </TabPane>

          <TabPane tab="甘特图" key="gantt">
            <GanttChart
              tasks={tasks}
              startDate="2024-01-15"
              endDate="2024-02-15"
              height={500}
            />
          </TabPane>

          <TabPane tab="统计图表" key="charts">
            <ConstructionCharts
              tasks={tasks}
              stats={stats}
            />
          </TabPane>
        </Tabs>
      </Card>

      {/* 任务详情查看模态框 */}
      <Modal
        title="任务详情"
        open={isDetailModalVisible}
        onCancel={() => {
          setIsDetailModalVisible(false)
          setViewingTask(null)
        }}
        footer={[
          <Button key="close" onClick={() => setIsDetailModalVisible(false)}>
            关闭
          </Button>,
          viewingTask && viewingTask.assignedTo.includes(currentUserId) && userRole === 'student' && (
            <Button
              key="update"
              type="primary"
              onClick={() => {
                setIsDetailModalVisible(false)
                handleEditTask(viewingTask)
              }}
            >
              更新进度
            </Button>
          )
        ]}
        width={800}
      >
        {viewingTask && (
          <div>
            <Row gutter={16} style={{ marginBottom: 16 }}>
              <Col span={12}>
                <div style={{ marginBottom: 8 }}>
                  <strong>任务名称：</strong>
                  <span>{viewingTask.name}</span>
                </div>
                <div style={{ marginBottom: 8 }}>
                  <strong>工序类型：</strong>
                  <Tag color="blue">
                    {viewingTask.category === 'preparation' ? '准备' :
                     viewingTask.category === 'structure' ? '结构' :
                     viewingTask.category === 'decoration' ? '装饰' :
                     viewingTask.category === 'installation' ? '安装' :
                     viewingTask.category === 'finishing' ? '收尾' : '检验'}
                  </Tag>
                </div>
                <div style={{ marginBottom: 8 }}>
                  <strong>优先级：</strong>
                  <Tag color={getPriorityColor(viewingTask.priority)}>
                    {viewingTask.priority === 'low' ? '低' :
                     viewingTask.priority === 'medium' ? '中' :
                     viewingTask.priority === 'high' ? '高' : '紧急'}
                  </Tag>
                </div>
              </Col>
              <Col span={12}>
                <div style={{ marginBottom: 8 }}>
                  <strong>任务状态：</strong>
                  <Badge
                    status={getStatusColor(viewingTask.status) as any}
                    text={
                      viewingTask.status === 'pending' ? '待开始' :
                      viewingTask.status === 'in_progress' ? '进行中' :
                      viewingTask.status === 'completed' ? '已完成' :
                      viewingTask.status === 'delayed' ? '延期' : '已取消'
                    }
                  />
                </div>
                <div style={{ marginBottom: 8 }}>
                  <strong>完成进度：</strong>
                  <Progress percent={viewingTask.progress} size="small" style={{ width: 150 }} />
                </div>
                <div style={{ marginBottom: 8 }}>
                  <strong>预计工期：</strong>
                  <span>{viewingTask.estimatedDuration}天</span>
                  {viewingTask.actualDuration && (
                    <span style={{ color: '#666', marginLeft: 8 }}>
                      (实际{viewingTask.actualDuration}天)
                    </span>
                  )}
                </div>
              </Col>
            </Row>

            <div style={{ marginBottom: 16 }}>
              <strong>任务描述：</strong>
              <div style={{
                marginTop: 8,
                padding: 12,
                backgroundColor: '#fafafa',
                borderRadius: 6,
                border: '1px solid #f0f0f0'
              }}>
                {viewingTask.description}
              </div>
            </div>

            <Row gutter={16} style={{ marginBottom: 16 }}>
              <Col span={12}>
                <div style={{ marginBottom: 8 }}>
                  <strong>计划时间：</strong>
                </div>
                <div style={{ fontSize: 14, color: '#666' }}>
                  开始：{viewingTask.startDate}
                </div>
                <div style={{ fontSize: 14, color: '#666' }}>
                  结束：{viewingTask.endDate}
                </div>
              </Col>
              <Col span={12}>
                {(viewingTask.actualStartDate || viewingTask.actualEndDate) && (
                  <div>
                    <div style={{ marginBottom: 8 }}>
                      <strong>实际时间：</strong>
                    </div>
                    {viewingTask.actualStartDate && (
                      <div style={{ fontSize: 14, color: '#666' }}>
                        开始：{viewingTask.actualStartDate}
                      </div>
                    )}
                    {viewingTask.actualEndDate && (
                      <div style={{ fontSize: 14, color: '#666' }}>
                        结束：{viewingTask.actualEndDate}
                      </div>
                    )}
                  </div>
                )}
              </Col>
            </Row>

            <div style={{ marginBottom: 16 }}>
              <strong>分配人员：</strong>
              <div style={{ marginTop: 8 }}>
                {viewingTask.assignedTo.map(studentId => (
                  <Tag key={studentId} style={{ marginBottom: 4 }}>
                    学生{studentId.split('-')[1]}
                    {studentId === currentUserId && userRole === 'student' && (
                      <span style={{ color: '#1890ff', marginLeft: 4 }}>(我)</span>
                    )}
                  </Tag>
                ))}
              </div>
            </div>

            {viewingTask.dependencies.length > 0 && (
              <div style={{ marginBottom: 16 }}>
                <strong>依赖任务：</strong>
                <div style={{ marginTop: 8 }}>
                  {viewingTask.dependencies.map(depId => {
                    const depTask = tasks.find(t => t.id === depId)
                    return depTask ? (
                      <Tag key={depId} style={{ marginBottom: 4 }}>
                        {depTask.name}
                      </Tag>
                    ) : null
                  })}
                </div>
              </div>
            )}

            {viewingTask.notes && (
              <div style={{ marginBottom: 16 }}>
                <strong>备注信息：</strong>
                <div style={{
                  marginTop: 8,
                  padding: 12,
                  backgroundColor: '#f6ffed',
                  borderRadius: 6,
                  border: '1px solid #b7eb8f'
                }}>
                  {viewingTask.notes}
                </div>
              </div>
            )}

            <div style={{
              marginTop: 16,
              paddingTop: 16,
              borderTop: '1px solid #f0f0f0',
              fontSize: 12,
              color: '#999'
            }}>
              <div>创建时间：{viewingTask.createdAt}</div>
              <div>更新时间：{viewingTask.updatedAt}</div>
            </div>
          </div>
        )}
      </Modal>

      {/* 任务编辑模态框 */}
      <Modal
        title={
          userRole === 'student'
            ? '更新任务进度'
            : (editingTask ? '编辑任务' : '新建任务')
        }
        open={isTaskModalVisible}
        onCancel={() => {
          setIsTaskModalVisible(false)
          setEditingTask(null)
          form.resetFields()
        }}
        onOk={() => form.submit()}
        width={800}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSaveTask}
        >
          {userRole === 'teacher' ? (
            // 教师端：完整的任务编辑表单
            <>
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    name="name"
                    label="任务名称"
                    rules={[{ required: true, message: '请输入任务名称' }]}
                  >
                    <Input placeholder="请输入任务名称" />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name="category"
                    label="工序类型"
                    rules={[{ required: true, message: '请选择工序类型' }]}
                  >
                    <Select placeholder="请选择工序类型">
                      <Option value="preparation">准备</Option>
                      <Option value="structure">结构</Option>
                      <Option value="decoration">装饰</Option>
                      <Option value="installation">安装</Option>
                      <Option value="finishing">收尾</Option>
                      <Option value="inspection">检验</Option>
                    </Select>
                  </Form.Item>
                </Col>
              </Row>

              <Form.Item
                name="description"
                label="任务描述"
                rules={[{ required: true, message: '请输入任务描述' }]}
              >
                <Input.TextArea rows={3} placeholder="请输入任务描述" />
              </Form.Item>

              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    name="priority"
                    label="优先级"
                    rules={[{ required: true, message: '请选择优先级' }]}
                  >
                    <Select placeholder="请选择优先级">
                      <Option value="low">低</Option>
                      <Option value="medium">中</Option>
                      <Option value="high">高</Option>
                      <Option value="urgent">紧急</Option>
                    </Select>
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name="estimatedDuration"
                    label="预计工期（天）"
                    rules={[{ required: true, message: '请输入预计工期' }]}
                  >
                    <Input type="number" placeholder="请输入预计工期" />
                  </Form.Item>
                </Col>
              </Row>

              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    name="startDate"
                    label="开始日期"
                    rules={[{ required: true, message: '请选择开始日期' }]}
                  >
                    <DatePicker style={{ width: '100%' }} />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name="endDate"
                    label="结束日期"
                    rules={[{ required: true, message: '请选择结束日期' }]}
                  >
                    <DatePicker style={{ width: '100%' }} />
                  </Form.Item>
                </Col>
              </Row>
            </>
          ) : (
            // 学生端：只能更新进度和状态
            <>
              <div style={{ marginBottom: 16, padding: 16, backgroundColor: '#f6ffed', borderRadius: 6 }}>
                <div style={{ fontWeight: 600, marginBottom: 8 }}>任务信息</div>
                <div style={{ marginBottom: 4 }}>
                  <strong>任务名称：</strong>{editingTask?.name}
                </div>
                <div style={{ marginBottom: 4 }}>
                  <strong>任务描述：</strong>{editingTask?.description}
                </div>
                <div>
                  <strong>计划时间：</strong>{editingTask?.startDate} ~ {editingTask?.endDate}
                </div>
              </div>

              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    name="status"
                    label="任务状态"
                    rules={[{ required: true, message: '请选择任务状态' }]}
                  >
                    <Select placeholder="请选择任务状态">
                      <Option value="pending">待开始</Option>
                      <Option value="in_progress">进行中</Option>
                      <Option value="completed">已完成</Option>
                      <Option value="delayed">延期</Option>
                    </Select>
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name="progress"
                    label="完成进度（%）"
                    rules={[{ required: true, message: '请输入完成进度' }]}
                  >
                    <Input
                      type="number"
                      min={0}
                      max={100}
                      placeholder="请输入完成进度"
                      suffix="%"
                    />
                  </Form.Item>
                </Col>
              </Row>

              <Form.Item
                name="notes"
                label="工作备注"
                extra="请描述工作进展、遇到的问题或需要的帮助"
              >
                <Input.TextArea
                  rows={4}
                  placeholder="请输入工作备注，如：今日完成了基础框架搭建，遇到材料不足问题..."
                />
              </Form.Item>

              {editingTask?.status === 'in_progress' && (
                <Form.Item
                  name="actualStartDate"
                  label="实际开始日期"
                >
                  <DatePicker style={{ width: '100%' }} />
                </Form.Item>
              )}

              {editingTask?.status === 'completed' && (
                <Form.Item
                  name="actualEndDate"
                  label="实际完成日期"
                >
                  <DatePicker style={{ width: '100%' }} />
                </Form.Item>
              )}
            </>
          )}
        </Form>
      </Modal>
    </div>
  )
}

export default ConstructionManagePage
