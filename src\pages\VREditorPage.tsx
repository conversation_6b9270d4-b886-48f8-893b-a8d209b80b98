import React, { useState, useEffect } from 'react'
import { Card, Row, Col, Typo<PERSON>, Button, Space, Modal, Form, Input, Select, Tabs, Statistic, List, Avatar, Tag, Alert } from 'antd'
import { useNavigate } from 'react-router-dom'
import {
  EyeOutlined,
  PlayCircleOutlined,
  SettingOutlined,
  TeamOutlined,
  SaveOutlined,
  ShareAltOutlined,
  DownloadOutlined,
  UploadOutlined,
  ExperimentOutlined,
  UserOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
} from '@ant-design/icons'
import { useAuthStore } from '@/store/authStore'
import { ROUTES } from '@/utils/constants'

const { Title, Paragraph, Text } = Typography
const { Option } = Select
const { TabPane } = Tabs

interface VRScene {
  id: string
  name: string
  description: string
  creator: string
  createdAt: string
  lastModified: string
  status: 'draft' | 'published' | 'shared'
  thumbnail: string
  participants: string[]
  duration?: number
  interactions: number
}

interface VRTemplate {
  id: string
  name: string
  description: string
  category: 'exhibition' | 'conference' | 'training'
  thumbnail: string
  downloads: number
  rating: number
  creator: string
}

const VREditorPage: React.FC = () => {
  const navigate = useNavigate()
  const { user } = useAuthStore()
  const [activeTab, setActiveTab] = useState('scenes')
  const [isCreateModalVisible, setIsCreateModalVisible] = useState(false)
  const [isVRSupported, setIsVRSupported] = useState(false)
  const [form] = Form.useForm()

  // 模拟VR场景数据
  const [scenes] = useState<VRScene[]>([
    {
      id: '1',
      name: '汽车展VR体验',
      description: '基于2024春季汽车展的VR沉浸式体验场景',
      creator: '张同学',
      createdAt: '2024-01-20',
      lastModified: '2024-01-21',
      status: 'published',
      thumbnail: '/api/placeholder/300/200',
      participants: ['张同学', '李老师'],
      duration: 15,
      interactions: 8
    },
    {
      id: '2',
      name: '科技会议室',
      description: '多人协同的科技会议VR环境',
      creator: '李同学',
      createdAt: '2024-01-18',
      lastModified: '2024-01-19',
      status: 'draft',
      thumbnail: '/api/placeholder/300/200',
      participants: ['李同学'],
      duration: 10,
      interactions: 5
    }
  ])

  // 模拟VR模板数据
  const [templates] = useState<VRTemplate[]>([
    {
      id: '1',
      name: '标准展览模板',
      description: '适用于各类展览的标准VR模板',
      category: 'exhibition',
      thumbnail: '/api/placeholder/300/200',
      downloads: 45,
      rating: 4.5,
      creator: '系统'
    },
    {
      id: '2',
      name: '会议室模板',
      description: '多人会议和培训的VR环境模板',
      category: 'conference',
      thumbnail: '/api/placeholder/300/200',
      downloads: 32,
      rating: 4.2,
      creator: '系统'
    },
    {
      id: '3',
      name: '实训教学模板',
      description: '专为教学设计的互动式VR模板',
      category: 'training',
      thumbnail: '/api/placeholder/300/200',
      downloads: 28,
      rating: 4.8,
      creator: '系统'
    }
  ])

  useEffect(() => {
    // 检测VR设备支持
    if (navigator.xr) {
      navigator.xr.isSessionSupported('immersive-vr').then(supported => {
        setIsVRSupported(supported)
      })
    }
  }, [])

  const getStatusColor = (status: string) => {
    const colorMap: Record<string, string> = {
      draft: 'orange',
      published: 'green',
      shared: 'blue'
    }
    return colorMap[status] || 'default'
  }

  const getStatusText = (status: string) => {
    const textMap: Record<string, string> = {
      draft: '草稿',
      published: '已发布',
      shared: '已分享'
    }
    return textMap[status] || status
  }

  const getCategoryText = (category: string) => {
    const categoryMap: Record<string, string> = {
      exhibition: '展览',
      conference: '会议',
      training: '培训'
    }
    return categoryMap[category] || category
  }

  const handleCreateScene = async (values: any) => {
    console.log('创建VR场景:', values)
    setIsCreateModalVisible(false)
    form.resetFields()
  }

  const handleEditScene = (sceneId: string) => {
    console.log('编辑VR场景:', sceneId)
    // 这里应该打开VR编辑器
  }

  const handlePreviewScene = (sceneId: string) => {
    console.log('预览VR场景:', sceneId)
    // 这里应该启动VR预览
  }

  const handleUseTemplate = (templateId: string) => {
    console.log('使用模板:', templateId)
    // 基于模板创建新场景
  }

  const handleEnterVR = (sceneId: string) => {
    if (!isVRSupported) {
      Modal.warning({
        title: 'VR设备未检测到',
        content: '请确保您的设备支持WebXR并连接了VR头显设备。'
      })
      return
    }
    console.log('进入VR体验:', sceneId)
    // 启动VR会话
  }

  return (
    <div style={{ padding: '24px' }}>
      {/* 页面标题 */}
      <div style={{ marginBottom: 24 }}>
        <Title level={2}>VR编辑器</Title>
        <Paragraph>
          沉浸式VR环境，支持虚拟搭建和多人协同
        </Paragraph>
      </div>

      {/* VR设备状态提示 */}
      {!isVRSupported && (
        <Alert
          message="VR设备未检测到"
          description="为了获得最佳体验，请连接支持WebXR的VR头显设备。您仍可以在桌面模式下编辑和预览VR场景。"
          type="warning"
          showIcon
          style={{ marginBottom: 24 }}
        />
      )}

      {/* 统计数据 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="我的场景"
              value={scenes.filter(s => s.creator === user?.username).length}
              prefix={<EyeOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="已发布场景"
              value={scenes.filter(s => s.status === 'published').length}
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="协作场景"
              value={scenes.filter(s => s.participants.length > 1).length}
              prefix={<TeamOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="VR设备"
              value={isVRSupported ? 1 : 0}
              suffix="台"
              prefix={<ExperimentOutlined />}
              valueStyle={{ color: isVRSupported ? '#52c41a' : '#f5222d' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 主要内容 */}
      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane tab="我的场景" key="scenes">
          <Card
            title="VR场景列表"
            extra={
              <Button 
                type="primary" 
                icon={<PlayCircleOutlined />}
                onClick={() => setIsCreateModalVisible(true)}
              >
                创建场景
              </Button>
            }
          >
            <List
              grid={{ gutter: 16, xs: 1, sm: 2, md: 2, lg: 3, xl: 3, xxl: 4 }}
              dataSource={scenes}
              renderItem={scene => (
                <List.Item>
                  <Card
                    hoverable
                    cover={
                      <div style={{ position: 'relative' }}>
                        <img
                          alt={scene.name}
                          src={scene.thumbnail}
                          style={{ width: '100%', height: 160, objectFit: 'cover' }}
                        />
                        <Tag 
                          color={getStatusColor(scene.status)}
                          style={{ position: 'absolute', top: 8, right: 8 }}
                        >
                          {getStatusText(scene.status)}
                        </Tag>
                      </div>
                    }
                    actions={[
                      <Button 
                        type="text" 
                        icon={<SettingOutlined />}
                        onClick={() => handleEditScene(scene.id)}
                      >
                        编辑
                      </Button>,
                      <Button 
                        type="text" 
                        icon={<EyeOutlined />}
                        onClick={() => handlePreviewScene(scene.id)}
                      >
                        预览
                      </Button>,
                      <Button 
                        type="text" 
                        icon={<ExperimentOutlined />}
                        onClick={() => handleEnterVR(scene.id)}
                        disabled={!isVRSupported}
                      >
                        进入VR
                      </Button>
                    ]}
                  >
                    <Card.Meta
                      title={scene.name}
                      description={
                        <Space direction="vertical" style={{ width: '100%' }}>
                          <Text type="secondary" ellipsis>
                            {scene.description}
                          </Text>
                          <div>
                            <Space>
                              <Avatar size="small" icon={<UserOutlined />} />
                              <Text style={{ fontSize: 12 }}>{scene.creator}</Text>
                            </Space>
                          </div>
                          <div>
                            <Space>
                              <Text style={{ fontSize: 12 }}>
                                <ClockCircleOutlined /> {scene.duration}分钟
                              </Text>
                              <Text style={{ fontSize: 12 }}>
                                <TeamOutlined /> {scene.participants.length}人
                              </Text>
                            </Space>
                          </div>
                          <Text type="secondary" style={{ fontSize: 12 }}>
                            更新于 {scene.lastModified}
                          </Text>
                        </Space>
                      }
                    />
                  </Card>
                </List.Item>
              )}
            />
          </Card>
        </TabPane>

        <TabPane tab="模板库" key="templates">
          <Card title="VR场景模板">
            <List
              grid={{ gutter: 16, xs: 1, sm: 2, md: 2, lg: 3, xl: 3, xxl: 4 }}
              dataSource={templates}
              renderItem={template => (
                <List.Item>
                  <Card
                    hoverable
                    cover={
                      <img
                        alt={template.name}
                        src={template.thumbnail}
                        style={{ width: '100%', height: 160, objectFit: 'cover' }}
                      />
                    }
                    actions={[
                      <Button 
                        type="primary" 
                        size="small"
                        onClick={() => handleUseTemplate(template.id)}
                      >
                        使用模板
                      </Button>,
                      <Button 
                        type="text" 
                        icon={<EyeOutlined />}
                        size="small"
                      >
                        预览
                      </Button>
                    ]}
                  >
                    <Card.Meta
                      title={template.name}
                      description={
                        <Space direction="vertical" style={{ width: '100%' }}>
                          <Text type="secondary" ellipsis>
                            {template.description}
                          </Text>
                          <Tag color="blue">
                            {getCategoryText(template.category)}
                          </Tag>
                          <div>
                            <Space>
                              <Text style={{ fontSize: 12 }}>
                                <DownloadOutlined /> {template.downloads}
                              </Text>
                              <Text style={{ fontSize: 12 }}>
                                ⭐ {template.rating}
                              </Text>
                            </Space>
                          </div>
                          <Text type="secondary" style={{ fontSize: 12 }}>
                            by {template.creator}
                          </Text>
                        </Space>
                      }
                    />
                  </Card>
                </List.Item>
              )}
            />
          </Card>
        </TabPane>
      </Tabs>

      {/* 创建场景模态框 */}
      <Modal
        title="创建VR场景"
        open={isCreateModalVisible}
        onOk={() => form.submit()}
        onCancel={() => setIsCreateModalVisible(false)}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleCreateScene}
        >
          <Form.Item
            name="name"
            label="场景名称"
            rules={[{ required: true, message: '请输入场景名称' }]}
          >
            <Input placeholder="请输入场景名称" />
          </Form.Item>
          
          <Form.Item
            name="description"
            label="场景描述"
            rules={[{ required: true, message: '请输入场景描述' }]}
          >
            <Input.TextArea rows={3} placeholder="请描述VR场景的内容和用途" />
          </Form.Item>
          
          <Form.Item
            name="template"
            label="基础模板"
          >
            <Select placeholder="选择一个基础模板（可选）">
              <Option value="">从空白开始</Option>
              {templates.map(template => (
                <Option key={template.id} value={template.id}>
                  {template.name}
                </Option>
              ))}
            </Select>
          </Form.Item>
          
          <Form.Item
            name="collaborators"
            label="协作者"
          >
            <Select
              mode="multiple"
              placeholder="邀请其他用户协作（可选）"
            >
              <Option value="teacher1">李老师</Option>
              <Option value="student1">王同学</Option>
              <Option value="student2">赵同学</Option>
            </Select>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}

export default VREditorPage
