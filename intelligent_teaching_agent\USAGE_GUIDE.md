# 智能教学代理系统使用指南

## 🚀 快速启动

### 1. 启动应用
在项目目录中运行以下命令：
```bash
python -m streamlit run app.py
```

### 2. 访问应用
应用启动后，浏览器会自动打开 `http://localhost:8501`

## 📖 功能使用指南

### 🏠 欢迎主页
- 系统概述和功能介绍
- 教学目标说明
- 使用指南

### 📈 客户需求预测模块

#### 操作步骤：
1. **选择数据集**：在左侧边栏选择一个预置的销售数据集
   - 默认销售数据：标准的销售模式
   - 旺季销售数据：具有强季节性特征
   - 增长型销售数据：呈现增长趋势
   - 稳定型销售数据：相对稳定的销售模式

2. **设置参数**：
   - 预测周期：设置需要预测的未来天数（7-90天）
   - 历史数据长度：设置用于训练的历史数据长度（180-730天）

3. **运行预测**：点击"🚀 运行预测"按钮

4. **查看结果**：
   - **预测图表**：查看历史数据、预测值和置信区间
   - **模型成分分解**：分析趋势、年季节性、周季节性
   - **性能评估指标**：查看MAE、RMSE、MAPE等指标

#### 学习要点：
- 理解时间序列预测的基本概念
- 学会解读预测图表和置信区间
- 掌握季节性分析方法
- 了解模型性能评估指标

### 🏭 供应商选择模块

#### 操作步骤：
1. **设置权重**：在左侧边栏进行三组成对比较
   - 价格 vs 质量
   - 价格 vs 交付表现
   - 质量 vs 交付表现

2. **权重设置说明**：
   - 使用语义化滑块进行比较
   - 选项从"极端重要"到"同等重要"
   - 系统会自动转换为AHP标度

3. **开始评估**：点击"🚀 开始评估供应商"按钮

4. **查看结果**：
   - **权重信息**：查看计算出的各标准权重
   - **供应商排名**：查看详细的排名报告
   - **能力对比**：通过雷达图比较供应商
   - **决策分析**：查看权重敏感性和一致性检验

#### 学习要点：
- 掌握AHP层次分析法的基本原理
- 理解多标准决策的权重设置
- 学会使用TOPSIS方法进行排序
- 了解一致性检验的重要性

## 🎯 教学建议

### 对于教师：
1. **课前准备**：
   - 熟悉系统各功能模块
   - 准备相关理论知识讲解
   - 设计教学案例和练习

2. **课堂教学**：
   - 先讲解理论，再演示操作
   - 引导学生思考参数设置的影响
   - 鼓励学生尝试不同的数据集和参数

3. **课后练习**：
   - 让学生独立完成预测和决策任务
   - 要求学生分析结果并撰写报告
   - 组织小组讨论和经验分享

### 对于学生：
1. **学习准备**：
   - 复习相关理论知识
   - 了解系统基本操作
   - 准备学习笔记

2. **实践操作**：
   - 按照指南逐步操作
   - 仔细观察结果变化
   - 思考参数对结果的影响

3. **深入学习**：
   - 尝试不同的参数组合
   - 分析不同数据集的特点
   - 总结学习心得和体会

## 🔧 故障排除

### 常见问题：

1. **应用无法启动**
   - 检查Python环境是否正确
   - 确认所有依赖包已安装
   - 运行 `python test_dependencies.py` 检查

2. **预测功能报错**
   - 确认Prophet包已正确安装
   - 检查数据参数设置是否合理
   - 重新启动应用

3. **图表显示异常**
   - 检查浏览器兼容性
   - 清除浏览器缓存
   - 尝试刷新页面

4. **性能较慢**
   - 减少历史数据长度
   - 降低预测周期
   - 关闭其他占用资源的程序

### 获取帮助：
- 查看README.md文档
- 检查控制台错误信息
- 联系技术支持团队

## 📝 学习记录

建议学生在使用过程中记录：
- 使用的参数设置
- 观察到的现象
- 分析结果和结论
- 遇到的问题和解决方法
- 学习心得和体会

这将有助于加深理解和巩固学习效果。
