import React, { useState } from 'react'
import { Form, Input, Button, Card, Typography, Space, Alert, Divider, Tag } from 'antd'
import { UserOutlined, LockOutlined, LoginOutlined } from '@ant-design/icons'
import { useAuthStore } from '@/store/authStore'
import { useNavigate } from 'react-router-dom'
import { ROUTES } from '@/utils/constants'

const { Title, Paragraph } = Typography

const LoginPage: React.FC = () => {
  const [form] = Form.useForm()
  const [loginError, setLoginError] = useState<string>('')
  const { login, isLoading } = useAuthStore()
  const navigate = useNavigate()

  const handleLogin = async (values: { username: string; password: string }) => {
    setLoginError('')
    const success = await login(values.username, values.password)
    
    if (success) {
      navigate(ROUTES.HOME)
    } else {
      setLoginError('登录失败，请检查用户名和密码')
    }
  }

  const demoAccounts = [
    { username: 'student1', role: '学生', color: 'blue' },
    { username: 'teacher1', role: '教师', color: 'green' },
    { username: 'enterprise1', role: '企业', color: 'orange' },
  ]

  const handleDemoLogin = (username: string) => {
    form.setFieldsValue({ username, password: '123456' })
  }

  return (
    <div style={{
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      padding: '20px'
    }}>
      <Card
        style={{
          width: '100%',
          maxWidth: 400,
          boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
          borderRadius: 12
        }}
        bodyStyle={{ padding: '40px 32px' }}
      >
        <div style={{ textAlign: 'center', marginBottom: 32 }}>
          <Title level={2} style={{ color: '#1890ff', marginBottom: 8 }}>
            MCTT平台
          </Title>
          <Paragraph style={{ color: '#666', margin: 0 }}>
            会展行业虚实融合实训教学平台
          </Paragraph>
        </div>

        {loginError && (
          <Alert
            message={loginError}
            type="error"
            showIcon
            style={{ marginBottom: 24 }}
          />
        )}

        <Form
          form={form}
          name="login"
          onFinish={handleLogin}
          layout="vertical"
          size="large"
        >
          <Form.Item
            name="username"
            rules={[{ required: true, message: '请输入用户名' }]}
          >
            <Input
              prefix={<UserOutlined />}
              placeholder="用户名"
            />
          </Form.Item>

          <Form.Item
            name="password"
            rules={[{ required: true, message: '请输入密码' }]}
          >
            <Input.Password
              prefix={<LockOutlined />}
              placeholder="密码"
            />
          </Form.Item>

          <Form.Item>
            <Button
              type="primary"
              htmlType="submit"
              loading={isLoading}
              icon={<LoginOutlined />}
              style={{ width: '100%', height: 48 }}
            >
              登录
            </Button>
          </Form.Item>
        </Form>

        <Divider>演示账号</Divider>

        <div style={{ textAlign: 'center' }}>
          <Paragraph style={{ fontSize: 12, color: '#999', marginBottom: 16 }}>
            点击下方账号快速登录体验（密码：123456）
          </Paragraph>
          <Space direction="vertical" style={{ width: '100%' }}>
            {demoAccounts.map((account) => (
              <Button
                key={account.username}
                type="dashed"
                style={{ width: '100%', textAlign: 'left' }}
                onClick={() => handleDemoLogin(account.username)}
              >
                <Space>
                  <UserOutlined />
                  <span>{account.username}</span>
                  <Tag color={account.color}>{account.role}</Tag>
                </Space>
              </Button>
            ))}
          </Space>
        </div>

        <div style={{ textAlign: 'center', marginTop: 24 }}>
          <Paragraph style={{ fontSize: 12, color: '#999' }}>
            © 2024 MCTT会展实训平台. All rights reserved.
          </Paragraph>
        </div>
      </Card>
    </div>
  )
}

export default LoginPage
