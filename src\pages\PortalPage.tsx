import React from 'react'
import { Card, Row, Col, Typography, Button, Space, Avatar, Statistic } from 'antd'
import { useNavigate } from 'react-router-dom'
import {
  BookOutlined,
  ProjectOutlined,
  DesktopOutlined,
  EyeOutlined,
  CloudOutlined,
  SettingOutlined,
  UserOutlined,
  TeamOutlined,
  Bar<PERSON><PERSON>Outlined,
  ExperimentOutlined,
} from '@ant-design/icons'
import { useAuthStore } from '@/store/authStore'
import { useProjectStore } from '@/store/projectStore'
import { ROUTES } from '@/utils/constants'

const { Title, Paragraph, Text } = Typography

interface PlatformCard {
  title: string
  description: string
  icon: React.ReactNode
  color: string
  route: string
  features: string[]
  userRole?: string[]
}

const PortalPage: React.FC = () => {
  const navigate = useNavigate()
  const { user } = useAuthStore()
  const { statistics } = useProjectStore()

  // 根据PRD定义的平台入口
  const platforms: PlatformCard[] = [
    {
      title: '教管学考平台',
      description: user?.role === 'teacher'
        ? '班级管理、课程管理、作业布置、学情分析一体化平台'
        : '在线学习、课程学习、作业提交、考试测评一体化平台',
      icon: <BookOutlined style={{ fontSize: 32, color: '#1890ff' }} />,
      color: '#e6f7ff',
      route: ROUTES.LEARNING,
      features: user?.role === 'teacher'
        ? ['班级管理', '课程管理', '作业布置', '学情分析']
        : ['课程学习', '作业提交', '在线考试', '学习进度'],
      userRole: ['student', 'teacher', 'admin']
    },
    {
      title: '项目管理平台',
      description: '项目全生命周期管理，从立项到交付的完整流程',
      icon: <ProjectOutlined style={{ fontSize: 32, color: '#52c41a' }} />,
      color: '#f6ffed',
      route: ROUTES.PROJECTS,
      features: ['项目总览', '方案管理', '施工管理', '进度跟踪'],
      userRole: ['student', 'teacher', 'enterprise', 'admin']
    },
    {
      title: '3D设计软件',
      description: 'PC端专业3D设计工具，支持2D规划和3D建模',
      icon: <DesktopOutlined style={{ fontSize: 32, color: '#722ed1' }} />,
      color: '#f9f0ff',
      route: ROUTES.DESIGN_3D,
      features: ['2D平面规划', '3D建模', '材质编辑', '渲染输出'],
      userRole: ['student', 'teacher', 'enterprise']
    },
    {
      title: 'VR搭建系统',
      description: '沉浸式VR环境，支持虚拟搭建和多人协同',
      icon: <EyeOutlined style={{ fontSize: 32, color: '#fa8c16' }} />,
      color: '#fff7e6',
      route: ROUTES.VR_EDITOR,
      features: ['VR搭建', '多人协同', '沉浸体验', '评审指导'],
      userRole: ['student', 'teacher']
    },
    {
      title: '内容空间',
      description: '作品展示与分享平台，构建学习社区',
      icon: <CloudOutlined style={{ fontSize: 32, color: '#13c2c2' }} />,
      color: '#e6fffb',
      route: ROUTES.CONTENT_SPACE,
      features: ['作品发布', '作品浏览', '互动评论', '作品集'],
      userRole: ['student', 'teacher', 'admin']
    },
    {
      title: '系统管理',
      description: '用户权限管理、系统配置和运维监控',
      icon: <SettingOutlined style={{ fontSize: 32, color: '#f5222d' }} />,
      color: '#fff1f0',
      route: ROUTES.ADMIN,
      features: ['用户管理', '权限配置', '系统监控', '数据分析'],
      userRole: ['admin']
    }
  ]

  // 根据用户角色过滤平台
  const availablePlatforms = platforms.filter(platform => 
    !platform.userRole || platform.userRole.includes(user?.role || 'student')
  )

  const handlePlatformClick = (route: string) => {
    // 根据用户角色和平台类型决定跳转路径
    if (route === ROUTES.LEARNING) {
      if (user?.role === 'teacher') {
        navigate(ROUTES.LEARNING_CLASSES) // 教师跳转到班级管理
      } else {
        navigate(ROUTES.LEARNING_COURSES) // 学生跳转到课程学习
      }
    } else {
      navigate(route)
    }
  }

  const getGreeting = () => {
    const hour = new Date().getHours()
    if (hour < 12) return '上午好'
    if (hour < 18) return '下午好'
    return '晚上好'
  }

  const getRoleDisplayName = (role: string) => {
    const roleMap: Record<string, string> = {
      student: '学生',
      teacher: '教师',
      enterprise: '企业用户',
      admin: '管理员'
    }
    return roleMap[role] || role
  }

  return (
    <div style={{ padding: '24px', background: '#f5f5f5', minHeight: '100vh' }}>
      {/* 欢迎区域 */}
      <Card style={{ marginBottom: 24 }}>
        <Row align="middle" justify="space-between">
          <Col>
            <Space size="large">
              <Avatar size={64} icon={<UserOutlined />} src={user?.avatar} />
              <div>
                <Title level={3} style={{ margin: 0 }}>
                  {getGreeting()}，{user?.username || '用户'}！
                </Title>
                <Text type="secondary">
                  {getRoleDisplayName(user?.role || 'student')} | 会展行业虚实融合实训教学平台
                </Text>
              </div>
            </Space>
          </Col>
          <Col>
            <Button type="primary" onClick={() => navigate(ROUTES.PROFILE)}>
              个人中心
            </Button>
          </Col>
        </Row>
      </Card>

      {/* 统计数据 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="我的项目"
              value={statistics?.activeProjects || 0}
              prefix={<ProjectOutlined />}
              valueStyle={{ color: '#3f8600' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="学习课程"
              value={statistics?.enrolledCourses || 0}
              prefix={<BookOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="作品数量"
              value={statistics?.publishedWorks || 0}
              prefix={<CloudOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="在线时长"
              value={statistics?.onlineHours || 0}
              suffix="小时"
              prefix={<BarChartOutlined />}
              valueStyle={{ color: '#fa8c16' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 平台入口 */}
      <Title level={3} style={{ marginBottom: 24 }}>
        平台入口
      </Title>
      <Row gutter={[24, 24]}>
        {availablePlatforms.map((platform, index) => (
          <Col xs={24} sm={12} lg={8} key={index}>
            <Card
              hoverable
              style={{ 
                height: '100%',
                background: platform.color,
                border: 'none',
                borderRadius: 12
              }}
              bodyStyle={{ padding: 24 }}
              onClick={() => handlePlatformClick(platform.route)}
            >
              <Space direction="vertical" size="middle" style={{ width: '100%' }}>
                <div style={{ textAlign: 'center' }}>
                  {platform.icon}
                </div>
                <div>
                  <Title level={4} style={{ margin: 0, textAlign: 'center' }}>
                    {platform.title}
                  </Title>
                  <Paragraph 
                    style={{ 
                      margin: '8px 0 16px', 
                      color: '#666',
                      textAlign: 'center',
                      fontSize: 14
                    }}
                  >
                    {platform.description}
                  </Paragraph>
                </div>
                <div>
                  <Text strong style={{ fontSize: 12, color: '#999' }}>
                    主要功能：
                  </Text>
                  <div style={{ marginTop: 8 }}>
                    {platform.features.map((feature, idx) => (
                      <span
                        key={idx}
                        style={{
                          display: 'inline-block',
                          background: 'rgba(255,255,255,0.8)',
                          padding: '2px 8px',
                          borderRadius: 4,
                          fontSize: 12,
                          margin: '2px 4px 2px 0',
                          color: '#666'
                        }}
                      >
                        {feature}
                      </span>
                    ))}
                  </div>
                </div>
              </Space>
            </Card>
          </Col>
        ))}
      </Row>

      {/* 快速操作 */}
      <Card style={{ marginTop: 24 }}>
        <Title level={4}>快速操作</Title>
        <Space wrap size="middle">
          <Button 
            type="primary" 
            icon={<ProjectOutlined />}
            onClick={() => navigate(ROUTES.PROJECT_MANAGEMENT)}
          >
            创建新项目
          </Button>
          <Button 
            icon={<BookOutlined />}
            onClick={() => navigate(ROUTES.LEARNING_COURSES)}
          >
            浏览课程
          </Button>
          <Button 
            icon={<ExperimentOutlined />}
            onClick={() => navigate(ROUTES.AI_PLAN)}
          >
            AI方案生成
          </Button>
          <Button 
            icon={<CloudOutlined />}
            onClick={() => navigate(ROUTES.CONTENT_GALLERY)}
          >
            作品展示
          </Button>
        </Space>
      </Card>
    </div>
  )
}

export default PortalPage
