<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>项目管理 - 详情查看修复演示</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
            background-color: #f5f5f5;
            margin: 0;
            padding: 24px;
            line-height: 1.6;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .header {
            background: white;
            padding: 24px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 24px;
        }
        .title {
            font-size: 28px;
            color: #1890ff;
            margin-bottom: 8px;
        }
        .subtitle {
            color: #666;
            font-size: 16px;
        }
        .fix-highlight {
            background: #f6ffed;
            border: 1px solid #b7eb8f;
            border-radius: 6px;
            padding: 16px;
            margin-bottom: 24px;
        }
        .fix-title {
            font-weight: 600;
            color: #389e0d;
            margin-bottom: 8px;
        }
        .demo-section {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            padding: 24px;
            margin-bottom: 24px;
        }
        .demo-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 16px;
            color: #262626;
        }
        .proposal-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 16px;
        }
        .proposal-table th,
        .proposal-table td {
            padding: 12px 16px;
            text-align: left;
            border-bottom: 1px solid #f0f0f0;
        }
        .proposal-table th {
            background: #fafafa;
            font-weight: 600;
            color: #262626;
        }
        .proposal-table tr:hover {
            background: #f5f5f5;
        }
        .tag {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
            margin-right: 4px;
        }
        .tag-green { background: #f6ffed; color: #52c41a; }
        .tag-orange { background: #fff7e6; color: #fa8c16; }
        .tag-red { background: #fff2f0; color: #f5222d; }
        .tag-blue { background: #e6f7ff; color: #1890ff; }
        .btn {
            padding: 4px 8px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            background: white;
            cursor: pointer;
            transition: all 0.3s;
            font-size: 12px;
            margin-right: 8px;
        }
        .btn-link {
            background: none;
            border: none;
            color: #1890ff;
            text-decoration: underline;
        }
        .btn:hover {
            border-color: #1890ff;
            color: #1890ff;
        }
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }
        .modal-content {
            background: white;
            border-radius: 8px;
            width: 90%;
            max-width: 800px;
            max-height: 90%;
            overflow-y: auto;
        }
        .modal-header {
            padding: 16px 20px;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .modal-title {
            font-size: 16px;
            font-weight: 600;
            margin: 0;
        }
        .modal-body {
            padding: 20px;
        }
        .modal-footer {
            padding: 16px 20px;
            border-top: 1px solid #f0f0f0;
            display: flex;
            justify-content: flex-end;
            gap: 8px;
        }
        .close-btn {
            background: none;
            border: none;
            font-size: 20px;
            cursor: pointer;
            color: #666;
        }
        .detail-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 16px;
            margin-bottom: 16px;
        }
        .detail-item {
            margin-bottom: 8px;
        }
        .detail-label {
            font-weight: 600;
            margin-right: 8px;
        }
        .info-box {
            padding: 12px;
            border-radius: 6px;
            margin-top: 8px;
        }
        .info-box-gray {
            background: #fafafa;
            border: 1px solid #f0f0f0;
        }
        .info-box-blue {
            background: #e6f7ff;
            border: 1px solid #91d5ff;
        }
        .info-box-green {
            background: #f6ffed;
            border: 1px solid #b7eb8f;
        }
        .info-box-orange {
            background: #fff7e6;
            border: 1px solid #ffd591;
        }
        .info-box-purple {
            background: #f9f0ff;
            border: 1px solid #d3adf7;
        }
        .btn-primary {
            background: #1890ff;
            color: white;
            border-color: #1890ff;
        }
        .btn-success {
            background: #52c41a;
            color: white;
            border-color: #52c41a;
        }
        .btn-danger {
            background: #f5222d;
            color: white;
            border-color: #f5222d;
        }
        .code-highlight {
            background: #f6f8fa;
            border: 1px solid #e1e4e8;
            border-radius: 6px;
            padding: 16px;
            margin: 16px 0;
            font-family: 'Courier New', monospace;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 页面标题 -->
        <div class="header">
            <h1 class="title">📋 项目管理 - 详情查看修复演示</h1>
            <p class="subtitle">
                修复了项目管理中查看详情页面为空的问题，现在可以正常查看方案详情
            </p>
        </div>

        <!-- 修复说明 -->
        <div class="fix-highlight">
            <div class="fix-title">🔧 问题修复说明</div>
            <p><strong>问题：</strong>项目管理页面中的"查看详情"按钮点击后页面为空，没有显示任何内容。</p>
            <p><strong>原因：</strong>查看详情按钮缺少onClick处理函数，没有正确的详情展示模态框。</p>
            <p><strong>解决方案：</strong>添加了完整的方案详情查看功能，包括处理函数、状态管理和详情展示模态框。</p>
        </div>

        <!-- 修复内容展示 -->
        <div class="demo-section">
            <div class="demo-title">修复的代码内容</div>
            
            <h4>1. 添加了状态管理</h4>
            <div class="code-highlight">
const [selectedProposal, setSelectedProposal] = useState&lt;Proposal | null&gt;(null)
const [isProposalDetailVisible, setIsProposalDetailVisible] = useState(false)
            </div>

            <h4>2. 添加了点击处理函数</h4>
            <div class="code-highlight">
const handleViewProposal = (proposal: Proposal) => {
  setSelectedProposal(proposal)
  setIsProposalDetailVisible(true)
}

// 在按钮中添加onClick事件
&lt;Button 
  type="link" 
  icon={&lt;EyeOutlined /&gt;}
  onClick={() => handleViewProposal(record)}
&gt;
  查看详情
&lt;/Button&gt;
            </div>

            <h4>3. 添加了完整的详情展示模态框</h4>
            <div class="code-highlight">
&lt;Modal
  title="方案详情"
  open={isProposalDetailVisible}
  onCancel={() => setIsProposalDetailVisible(false)}
  width={800}
&gt;
  {/* 详细的方案信息展示 */}
&lt;/Modal&gt;
            </div>
        </div>

        <!-- 功能演示 -->
        <div class="demo-section">
            <div class="demo-title">方案审批列表 - 功能演示</div>
            <p style="color: #666; margin-bottom: 16px;">点击"查看详情"按钮可以查看完整的方案信息</p>
            
            <table class="proposal-table">
                <thead>
                    <tr>
                        <th>方案名称</th>
                        <th>提交人</th>
                        <th>状态</th>
                        <th>优先级</th>
                        <th>预算</th>
                        <th>提交时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>科技展览馆展台设计方案</td>
                        <td>张同学</td>
                        <td><span class="tag tag-orange">待审核</span></td>
                        <td><span class="tag tag-red">高</span></td>
                        <td>¥50,000</td>
                        <td>2024-01-20</td>
                        <td>
                            <button class="btn btn-link" onclick="showProposalDetail('proposal1')">查看详情</button>
                            <button class="btn btn-success">通过</button>
                            <button class="btn btn-danger">驳回</button>
                        </td>
                    </tr>
                    <tr>
                        <td>汽车展示厅布置方案</td>
                        <td>李同学</td>
                        <td><span class="tag tag-green">已批准</span></td>
                        <td><span class="tag tag-orange">中</span></td>
                        <td>¥80,000</td>
                        <td>2024-01-18</td>
                        <td>
                            <button class="btn btn-link" onclick="showProposalDetail('proposal2')">查看详情</button>
                        </td>
                    </tr>
                    <tr>
                        <td>文化艺术展览空间方案</td>
                        <td>王同学</td>
                        <td><span class="tag tag-red">已驳回</span></td>
                        <td><span class="tag tag-blue">低</span></td>
                        <td>¥30,000</td>
                        <td>2024-01-15</td>
                        <td>
                            <button class="btn btn-link" onclick="showProposalDetail('proposal3')">查看详情</button>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- 方案详情模态框 -->
        <div id="proposal-detail-modal" class="modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 class="modal-title">方案详情</h3>
                    <button class="close-btn" onclick="closeModal()">&times;</button>
                </div>
                <div class="modal-body" id="proposal-detail-content">
                    <!-- 动态内容 -->
                </div>
                <div class="modal-footer">
                    <button class="btn" onclick="closeModal()">关闭</button>
                    <button id="approve-btn" class="btn btn-success" onclick="approveProposal()" style="display: none;">批准方案</button>
                    <button id="reject-btn" class="btn btn-danger" onclick="rejectProposal()" style="display: none;">驳回方案</button>
                </div>
            </div>
        </div>

        <!-- 功能特点 -->
        <div class="demo-section">
            <div class="demo-title">🎯 修复后的功能特点</div>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">
                <div>
                    <h4 style="color: #52c41a;">✅ 完整的详情展示</h4>
                    <ul style="color: #666; margin: 0; padding-left: 20px;">
                        <li>方案基本信息</li>
                        <li>项目描述和设计理念</li>
                        <li>目标受众和预期效果</li>
                        <li>时间安排和预算信息</li>
                        <li>审核意见和状态</li>
                    </ul>
                </div>
                <div>
                    <h4 style="color: #fa8c16;">🔧 交互功能</h4>
                    <ul style="color: #666; margin: 0; padding-left: 20px;">
                        <li>点击查看详情</li>
                        <li>模态框展示</li>
                        <li>批准/驳回操作</li>
                        <li>状态实时更新</li>
                        <li>响应式设计</li>
                    </ul>
                </div>
                <div>
                    <h4 style="color: #722ed1;">👥 角色权限</h4>
                    <ul style="color: #666; margin: 0; padding-left: 20px;">
                        <li>学生可查看详情</li>
                        <li>教师可审核方案</li>
                        <li>权限控制按钮</li>
                        <li>状态相关操作</li>
                        <li>安全性保障</li>
                    </ul>
                </div>
                <div>
                    <h4 style="color: #13c2c2;">📊 数据展示</h4>
                    <ul style="color: #666; margin: 0; padding-left: 20px;">
                        <li>结构化信息展示</li>
                        <li>颜色编码状态</li>
                        <li>时间戳记录</li>
                        <li>预算格式化</li>
                        <li>标签分类显示</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 总结 -->
        <div style="background: #f6ffed; border: 1px solid #b7eb8f; border-radius: 6px; padding: 20px; margin-top: 24px; text-align: center;">
            <h3 style="color: #389e0d; margin-bottom: 12px;">✅ 问题修复完成</h3>
            <p style="margin: 0; color: #666;">
                项目管理中的查看详情功能已完全修复，现在可以正常查看方案的完整信息，
                包括基本信息、描述、审核状态等所有相关内容。
            </p>
        </div>
    </div>

    <script>
        function showProposalDetail(proposalId) {
            const proposalDetails = {
                proposal1: {
                    name: '科技展览馆展台设计方案',
                    submittedBy: '张同学',
                    submittedAt: '2024-01-20 14:30:00',
                    status: 'pending',
                    priority: 'high',
                    budget: 50000,
                    description: '本方案旨在为科技展览馆设计一个现代化、互动性强的展台。采用简约现代的设计风格，结合最新的展示技术，为参观者提供沉浸式的科技体验。展台将分为产品展示区、互动体验区和洽谈区三个主要功能区域。',
                    designConcept: '以"科技引领未来"为主题，采用蓝白色调为主色彩，配合LED灯光效果，营造科技感十足的展示环境。整体设计简洁大方，突出展品的科技特色。',
                    targetAudience: '主要面向科技爱好者、行业专业人士、投资者和普通参观者。通过不同层次的展示内容，满足不同观众群体的需求。',
                    expectedOutcome: '预期能够吸引大量参观者，提升品牌知名度，促进产品销售，建立行业合作关系。预计参观人数可达到5000人次。',
                    timeline: '设计阶段：1周，制作阶段：2周，安装调试：3天，展览期间：5天，撤展：1天。总计约4周时间。',
                    canApprove: true
                },
                proposal2: {
                    name: '汽车展示厅布置方案',
                    submittedBy: '李同学',
                    submittedAt: '2024-01-18 10:15:00',
                    status: 'approved',
                    priority: 'medium',
                    budget: 80000,
                    description: '为汽车展示厅设计的豪华展示方案，突出汽车的品质和性能。采用环形布局，让参观者可以360度欣赏展车。配备专业的照明系统和音响设备。',
                    designConcept: '以"速度与激情"为设计理念，采用黑红配色方案，营造动感十足的展示氛围。地面采用镜面材质，增强空间感和豪华感。',
                    targetAudience: '汽车爱好者、潜在购车客户、媒体记者和行业专家。',
                    expectedOutcome: '提升品牌形象，促进销售，建立客户关系。预期销售线索转化率达到15%。',
                    timeline: '总计3周，包括设计、制作、安装和展示各个阶段。',
                    reviewComments: '方案设计优秀，符合品牌定位，预算合理，批准执行。',
                    reviewedAt: '2024-01-19 09:30:00',
                    canApprove: false
                },
                proposal3: {
                    name: '文化艺术展览空间方案',
                    submittedBy: '王同学',
                    submittedAt: '2024-01-15 16:45:00',
                    status: 'rejected',
                    priority: 'low',
                    budget: 30000,
                    description: '为文化艺术展览设计的展示空间，强调艺术氛围和文化内涵。采用传统与现代相结合的设计手法，营造优雅的展示环境。',
                    designConcept: '以"传承与创新"为主题，采用暖色调设计，结合传统文化元素和现代展示技术。',
                    targetAudience: '艺术爱好者、文化学者、学生和普通观众。',
                    expectedOutcome: '传播文化艺术，提升公众文化素养，促进文化交流。',
                    timeline: '设计制作2周，展览1个月。',
                    reviewComments: '设计理念很好，但预算不足，展示效果可能达不到预期。建议增加预算或简化设计方案后重新提交。',
                    reviewedAt: '2024-01-16 11:20:00',
                    canApprove: false
                }
            };

            const proposal = proposalDetails[proposalId];

            document.getElementById('proposal-detail-content').innerHTML = `
                <div class="detail-grid">
                    <div>
                        <div class="detail-item">
                            <span class="detail-label">方案名称：</span>
                            <span>${proposal.name}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">提交人：</span>
                            <span>${proposal.submittedBy}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">提交时间：</span>
                            <span>${proposal.submittedAt}</span>
                        </div>
                    </div>
                    <div>
                        <div class="detail-item">
                            <span class="detail-label">方案状态：</span>
                            <span class="tag ${proposal.status === 'approved' ? 'tag-green' : proposal.status === 'rejected' ? 'tag-red' : 'tag-orange'}">
                                ${proposal.status === 'approved' ? '已批准' : proposal.status === 'rejected' ? '已驳回' : '待审核'}
                            </span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">优先级：</span>
                            <span class="tag ${proposal.priority === 'high' ? 'tag-red' : proposal.priority === 'medium' ? 'tag-orange' : 'tag-blue'}">
                                ${proposal.priority === 'high' ? '高' : proposal.priority === 'medium' ? '中' : '低'}
                            </span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">预估预算：</span>
                            <span>¥${proposal.budget.toLocaleString()}</span>
                        </div>
                    </div>
                </div>

                <div style="margin-bottom: 16px;">
                    <div class="detail-label">项目描述：</div>
                    <div class="info-box info-box-gray">
                        ${proposal.description}
                    </div>
                </div>

                <div style="margin-bottom: 16px;">
                    <div class="detail-label">设计理念：</div>
                    <div class="info-box info-box-blue">
                        ${proposal.designConcept}
                    </div>
                </div>

                <div style="margin-bottom: 16px;">
                    <div class="detail-label">目标受众：</div>
                    <div class="info-box info-box-green">
                        ${proposal.targetAudience}
                    </div>
                </div>

                <div style="margin-bottom: 16px;">
                    <div class="detail-label">预期效果：</div>
                    <div class="info-box info-box-orange">
                        ${proposal.expectedOutcome}
                    </div>
                </div>

                <div style="margin-bottom: 16px;">
                    <div class="detail-label">时间安排：</div>
                    <div class="info-box info-box-purple">
                        ${proposal.timeline}
                    </div>
                </div>

                ${proposal.reviewComments ? `
                    <div style="margin-bottom: 16px;">
                        <div class="detail-label">审核意见：</div>
                        <div class="info-box ${proposal.status === 'approved' ? 'info-box-green' : 'info-box-red'}">
                            ${proposal.reviewComments}
                        </div>
                    </div>
                ` : ''}

                <div style="margin-top: 16px; padding-top: 16px; border-top: 1px solid #f0f0f0; font-size: 12px; color: #999;">
                    <div>创建时间：${proposal.submittedAt}</div>
                    ${proposal.reviewedAt ? `<div>审核时间：${proposal.reviewedAt}</div>` : ''}
                </div>
            `;

            // 显示或隐藏审核按钮
            const approveBtn = document.getElementById('approve-btn');
            const rejectBtn = document.getElementById('reject-btn');
            
            if (proposal.canApprove && proposal.status === 'pending') {
                approveBtn.style.display = 'inline-block';
                rejectBtn.style.display = 'inline-block';
            } else {
                approveBtn.style.display = 'none';
                rejectBtn.style.display = 'none';
            }

            document.getElementById('proposal-detail-modal').style.display = 'flex';
        }

        function closeModal() {
            document.getElementById('proposal-detail-modal').style.display = 'none';
        }

        function approveProposal() {
            alert('方案已批准！');
            closeModal();
        }

        function rejectProposal() {
            const reason = prompt('请输入驳回原因：', '需要进一步完善');
            if (reason) {
                alert(`方案已驳回！原因：${reason}`);
                closeModal();
            }
        }

        // 点击模态框外部关闭
        document.getElementById('proposal-detail-modal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeModal();
            }
        });
    </script>
</body>
</html>
