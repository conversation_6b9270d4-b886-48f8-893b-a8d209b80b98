import React, { useState, useEffect } from 'react'
import { Card, Row, Col, <PERSON>po<PERSON>, Button, Table, Tag, Space, Modal, Form, Input, Select, DatePicker, Tabs, Statistic, Upload, message, Progress } from 'antd'
import { useNavigate, useLocation } from 'react-router-dom'
import {
  BookOutlined,
  PlusOutlined,
  EditOutlined,
  EyeOutlined,
  TeamOutlined,
  FileTextOutlined,
  UploadOutlined,
  DownloadOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  UserOutlined,
  BarChartOutlined,
  TrophyOutlined,
} from '@ant-design/icons'
import { useAuthStore } from '@/store/authStore'
import type { ColumnsType } from 'antd/es/table'

const { Title, Paragraph, Text } = Typography
const { Option } = Select
const { TextArea } = Input
const { TabPane } = Tabs

interface Class {
  id: string
  name: string
  studentCount: number
  createdAt: string
  description: string
}

interface Course {
  id: string
  title: string
  classId: string
  className: string
  totalLessons: number
  completedLessons: number
  studentCount: number
  avgProgress: number
  status: 'draft' | 'published' | 'completed'
}

interface Assignment {
  id: string
  title: string
  courseId: string
  courseName: string
  className: string
  type: 'theory' | 'practice'
  dueDate: string
  submittedCount: number
  totalCount: number
  avgScore?: number
  status: 'active' | 'closed'
}

interface Exam {
  id: string
  title: string
  courseId: string
  courseName: string
  className: string
  duration: number // 分钟
  totalQuestions: number
  totalScore: number
  startTime: string
  endTime: string
  participantCount: number
  completedCount: number
  avgScore?: number
  status: 'draft' | 'published' | 'ongoing' | 'completed'
}

interface Question {
  id: string
  type: 'single' | 'multiple' | 'judge' | 'essay'
  category: string
  difficulty: 'easy' | 'medium' | 'hard'
  content: string
  options?: string[]
  correctAnswer: string | string[]
  score: number
  explanation?: string
  tags: string[]
  createdAt: string
}

interface Resource {
  id: string
  name: string
  type: 'video' | 'document' | 'image' | 'audio' | 'other'
  category: string
  size: string
  uploadedAt: string
  downloadCount: number
  isPublic: boolean
  description?: string
}

interface Student {
  id: string
  name: string
  studentId: string
  classId: string
  avgScore: number
  completedAssignments: number
  totalAssignments: number
  lastActive: string
}

const TeacherManagePage: React.FC = () => {
  const navigate = useNavigate()
  const location = useLocation()
  const { user } = useAuthStore()
  const [activeTab, setActiveTab] = useState('classes')
  const [isClassModalVisible, setIsClassModalVisible] = useState(false)
  const [isCourseModalVisible, setIsCourseModalVisible] = useState(false)
  const [isAssignmentModalVisible, setIsAssignmentModalVisible] = useState(false)
  const [classForm] = Form.useForm()
  const [courseForm] = Form.useForm()
  const [assignmentForm] = Form.useForm()

  // 根据路由设置活动标签页
  useEffect(() => {
    const path = location.pathname
    if (path.includes('/teacher/courses')) {
      setActiveTab('courses')
    } else if (path.includes('/teacher/assignments')) {
      setActiveTab('assignments')
    } else if (path.includes('/teacher/exams')) {
      setActiveTab('exams')
    } else if (path.includes('/teacher/questions')) {
      setActiveTab('questions')
    } else if (path.includes('/teacher/resources')) {
      setActiveTab('resources')
    } else if (path.includes('/teacher/analytics')) {
      setActiveTab('analytics')
    } else {
      setActiveTab('classes')
    }
  }, [location.pathname])

  // 模拟数据
  const [classes] = useState<Class[]>([
    { id: '1', name: '会展设计2024-1班', studentCount: 32, createdAt: '2024-01-15', description: '会展设计专业一班' },
    { id: '2', name: '会展设计2024-2班', studentCount: 28, createdAt: '2024-01-15', description: '会展设计专业二班' }
  ])

  const [courses] = useState<Course[]>([
    {
      id: '1',
      title: '会展策划基础',
      classId: '1',
      className: '会展设计2024-1班',
      totalLessons: 12,
      completedLessons: 8,
      studentCount: 32,
      avgProgress: 75,
      status: 'published'
    },
    {
      id: '2',
      title: '展示设计原理',
      classId: '2',
      className: '会展设计2024-2班',
      totalLessons: 10,
      completedLessons: 6,
      studentCount: 28,
      avgProgress: 60,
      status: 'published'
    }
  ])

  const [assignments] = useState<Assignment[]>([
    {
      id: '1',
      title: '展位设计方案',
      courseId: '1',
      courseName: '会展策划基础',
      className: '会展设计2024-1班',
      type: 'practice',
      dueDate: '2024-01-25',
      submittedCount: 25,
      totalCount: 32,
      avgScore: 82.5,
      status: 'active'
    },
    {
      id: '2',
      title: '理论知识测试',
      courseId: '1',
      courseName: '会展策划基础',
      className: '会展设计2024-1班',
      type: 'theory',
      dueDate: '2024-01-22',
      submittedCount: 32,
      totalCount: 32,
      avgScore: 85.2,
      status: 'closed'
    }
  ])

  const [students] = useState<Student[]>([
    {
      id: '1',
      name: '张三',
      studentId: '2024001',
      classId: '1',
      avgScore: 88.5,
      completedAssignments: 8,
      totalAssignments: 10,
      lastActive: '2024-01-21'
    },
    {
      id: '2',
      name: '李四',
      studentId: '2024002',
      classId: '1',
      avgScore: 76.3,
      completedAssignments: 7,
      totalAssignments: 10,
      lastActive: '2024-01-20'
    }
  ])

  // 考试数据
  const [exams] = useState<Exam[]>([
    {
      id: '1',
      title: '会展策划基础期中考试',
      courseId: '1',
      courseName: '会展策划基础',
      className: '会展设计2024-1班',
      duration: 90,
      totalQuestions: 50,
      totalScore: 100,
      startTime: '2024-01-25 09:00',
      endTime: '2024-01-25 10:30',
      participantCount: 32,
      completedCount: 28,
      avgScore: 82.5,
      status: 'completed'
    },
    {
      id: '2',
      title: '展示设计原理测试',
      courseId: '2',
      courseName: '展示设计原理',
      className: '会展设计2024-2班',
      duration: 60,
      totalQuestions: 30,
      totalScore: 100,
      startTime: '2024-01-28 14:00',
      endTime: '2024-01-28 15:00',
      participantCount: 28,
      completedCount: 0,
      status: 'published'
    }
  ])

  // 题库数据
  const [questions] = useState<Question[]>([
    {
      id: '1',
      type: 'single',
      category: '会展策划',
      difficulty: 'medium',
      content: '会展策划的核心要素不包括以下哪项？',
      options: ['目标定位', '预算控制', '场地选择', '天气预报'],
      correctAnswer: '天气预报',
      score: 2,
      explanation: '天气预报虽然重要，但不是会展策划的核心要素',
      tags: ['基础概念', '策划要素'],
      createdAt: '2024-01-15'
    },
    {
      id: '2',
      type: 'multiple',
      category: '展示设计',
      difficulty: 'hard',
      content: '展示设计中需要考虑的因素包括：',
      options: ['空间布局', '色彩搭配', '照明设计', '音响效果', '安全通道'],
      correctAnswer: ['空间布局', '色彩搭配', '照明设计', '安全通道'],
      score: 5,
      explanation: '音响效果通常不是展示设计的主要考虑因素',
      tags: ['设计原理', '综合应用'],
      createdAt: '2024-01-16'
    }
  ])

  // 资源库数据
  const [resources] = useState<Resource[]>([
    {
      id: '1',
      name: '会展策划基础教程.mp4',
      type: 'video',
      category: '教学视频',
      size: '256MB',
      uploadedAt: '2024-01-10',
      downloadCount: 45,
      isPublic: true,
      description: '会展策划基础知识讲解视频'
    },
    {
      id: '2',
      name: '展位设计案例集.pdf',
      type: 'document',
      category: '参考资料',
      size: '12MB',
      uploadedAt: '2024-01-12',
      downloadCount: 32,
      isPublic: true,
      description: '经典展位设计案例分析'
    },
    {
      id: '3',
      name: '3D建模素材包.zip',
      type: 'other',
      category: '素材资源',
      size: '128MB',
      uploadedAt: '2024-01-14',
      downloadCount: 28,
      isPublic: false,
      description: '3D建模常用素材和模型文件'
    }
  ])

  const classColumns: ColumnsType<Class> = [
    { title: '班级名称', dataIndex: 'name', key: 'name' },
    { title: '学生人数', dataIndex: 'studentCount', key: 'studentCount' },
    { title: '创建时间', dataIndex: 'createdAt', key: 'createdAt' },
    { title: '描述', dataIndex: 'description', key: 'description' },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space>
          <Button type="link" icon={<EyeOutlined />}>查看</Button>
          <Button type="link" icon={<EditOutlined />}>编辑</Button>
          <Button type="link" icon={<TeamOutlined />}>管理学生</Button>
        </Space>
      )
    }
  ]

  const courseColumns: ColumnsType<Course> = [
    { title: '课程名称', dataIndex: 'title', key: 'title' },
    { title: '所属班级', dataIndex: 'className', key: 'className' },
    {
      title: '进度',
      key: 'progress',
      render: (_, record) => (
        <div>
          <Text>{record.completedLessons}/{record.totalLessons} 课时</Text>
          <Progress percent={record.avgProgress} size="small" style={{ marginTop: 4 }} />
        </div>
      )
    },
    { title: '学生数', dataIndex: 'studentCount', key: 'studentCount' },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <Tag color={status === 'published' ? 'green' : status === 'draft' ? 'orange' : 'blue'}>
          {status === 'published' ? '已发布' : status === 'draft' ? '草稿' : '已完成'}
        </Tag>
      )
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space>
          <Button type="link" icon={<EyeOutlined />}>查看</Button>
          <Button type="link" icon={<EditOutlined />}>编辑</Button>
          <Button type="link" icon={<FileTextOutlined />}>布置作业</Button>
        </Space>
      )
    }
  ]

  const assignmentColumns: ColumnsType<Assignment> = [
    { title: '作业名称', dataIndex: 'title', key: 'title' },
    { title: '课程', dataIndex: 'courseName', key: 'courseName' },
    { title: '班级', dataIndex: 'className', key: 'className' },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      render: (type) => (
        <Tag color={type === 'practice' ? 'blue' : 'green'}>
          {type === 'practice' ? '实操作业' : '理论测试'}
        </Tag>
      )
    },
    {
      title: '提交情况',
      key: 'submission',
      render: (_, record) => (
        <div>
          <Text>{record.submittedCount}/{record.totalCount}</Text>
          <Progress 
            percent={Math.round((record.submittedCount / record.totalCount) * 100)} 
            size="small" 
            style={{ marginTop: 4 }}
          />
        </div>
      )
    },
    {
      title: '平均分',
      dataIndex: 'avgScore',
      key: 'avgScore',
      render: (score) => score ? `${score.toFixed(1)}分` : '-'
    },
    { title: '截止时间', dataIndex: 'dueDate', key: 'dueDate' },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space>
          <Button type="link" icon={<EyeOutlined />}>查看详情</Button>
          <Button type="link" icon={<BarChartOutlined />}>统计分析</Button>
          <Button type="link">批改</Button>
        </Space>
      )
    }
  ]

  const handleCreateClass = async (values: any) => {
    console.log('创建班级:', values)
    setIsClassModalVisible(false)
    classForm.resetFields()
    message.success('班级创建成功')
  }

  const handleCreateCourse = async (values: any) => {
    console.log('创建课程:', values)
    setIsCourseModalVisible(false)
    courseForm.resetFields()
    message.success('课程创建成功')
  }

  const handleCreateAssignment = async (values: any) => {
    console.log('布置作业:', values)
    setIsAssignmentModalVisible(false)
    assignmentForm.resetFields()
    message.success('作业布置成功')
  }

  const handleTabChange = (key: string) => {
    setActiveTab(key)
    // 根据标签页更新URL
    switch (key) {
      case 'classes':
        navigate('/learning/classes')
        break
      case 'courses':
        navigate('/learning/teacher/courses')
        break
      case 'assignments':
        navigate('/learning/teacher/assignments')
        break
      case 'exams':
        navigate('/learning/teacher/exams')
        break
      case 'questions':
        navigate('/learning/teacher/questions')
        break
      case 'resources':
        navigate('/learning/teacher/resources')
        break
      case 'analytics':
        navigate('/learning/teacher/analytics')
        break
      default:
        navigate('/learning/classes')
    }
  }

  // 考试管理表格列
  const examColumns: ColumnsType<Exam> = [
    { title: '考试名称', dataIndex: 'title', key: 'title' },
    { title: '课程', dataIndex: 'courseName', key: 'courseName' },
    { title: '班级', dataIndex: 'className', key: 'className' },
    {
      title: '考试信息',
      key: 'examInfo',
      render: (_, record) => (
        <Space direction="vertical" size={0}>
          <Text style={{ fontSize: 12 }}>时长：{record.duration}分钟</Text>
          <Text style={{ fontSize: 12 }}>题数：{record.totalQuestions}题</Text>
          <Text style={{ fontSize: 12 }}>总分：{record.totalScore}分</Text>
        </Space>
      )
    },
    {
      title: '考试时间',
      key: 'examTime',
      render: (_, record) => (
        <Space direction="vertical" size={0}>
          <Text style={{ fontSize: 12 }}>开始：{record.startTime}</Text>
          <Text style={{ fontSize: 12 }}>结束：{record.endTime}</Text>
        </Space>
      )
    },
    {
      title: '参与情况',
      key: 'participation',
      render: (_, record) => (
        <div>
          <Text>{record.completedCount}/{record.participantCount}</Text>
          <Progress
            percent={Math.round((record.completedCount / record.participantCount) * 100)}
            size="small"
            style={{ marginTop: 4 }}
          />
        </div>
      )
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <Tag color={
          status === 'draft' ? 'orange' :
          status === 'published' ? 'blue' :
          status === 'ongoing' ? 'green' : 'purple'
        }>
          {status === 'draft' ? '草稿' :
           status === 'published' ? '已发布' :
           status === 'ongoing' ? '进行中' : '已完成'}
        </Tag>
      )
    },
    {
      title: '平均分',
      dataIndex: 'avgScore',
      key: 'avgScore',
      render: (score) => score ? `${score.toFixed(1)}分` : '-'
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space>
          <Button type="link" icon={<EyeOutlined />}>查看</Button>
          <Button type="link" icon={<EditOutlined />}>编辑</Button>
          <Button type="link" icon={<BarChartOutlined />}>统计</Button>
        </Space>
      )
    }
  ]

  // 题库管理表格列
  const questionColumns: ColumnsType<Question> = [
    {
      title: '题目内容',
      dataIndex: 'content',
      key: 'content',
      render: (content) => (
        <Text ellipsis style={{ maxWidth: 200 }}>{content}</Text>
      )
    },
    {
      title: '题型',
      dataIndex: 'type',
      key: 'type',
      render: (type) => (
        <Tag color={
          type === 'single' ? 'blue' :
          type === 'multiple' ? 'green' :
          type === 'judge' ? 'orange' : 'purple'
        }>
          {type === 'single' ? '单选题' :
           type === 'multiple' ? '多选题' :
           type === 'judge' ? '判断题' : '问答题'}
        </Tag>
      )
    },
    { title: '分类', dataIndex: 'category', key: 'category' },
    {
      title: '难度',
      dataIndex: 'difficulty',
      key: 'difficulty',
      render: (difficulty) => (
        <Tag color={
          difficulty === 'easy' ? 'green' :
          difficulty === 'medium' ? 'orange' : 'red'
        }>
          {difficulty === 'easy' ? '简单' :
           difficulty === 'medium' ? '中等' : '困难'}
        </Tag>
      )
    },
    { title: '分值', dataIndex: 'score', key: 'score', render: (score) => `${score}分` },
    {
      title: '标签',
      dataIndex: 'tags',
      key: 'tags',
      render: (tags) => (
        <Space wrap>
          {tags.slice(0, 2).map((tag: string) => (
            <Tag key={tag} size="small">{tag}</Tag>
          ))}
          {tags.length > 2 && <Text type="secondary">+{tags.length - 2}</Text>}
        </Space>
      )
    },
    { title: '创建时间', dataIndex: 'createdAt', key: 'createdAt' },
    {
      title: '操作',
      key: 'action',
      render: () => (
        <Space>
          <Button type="link" icon={<EyeOutlined />}>查看</Button>
          <Button type="link" icon={<EditOutlined />}>编辑</Button>
          <Button type="link" danger>删除</Button>
        </Space>
      )
    }
  ]

  // 资源库表格列
  const resourceColumns: ColumnsType<Resource> = [
    {
      title: '资源名称',
      dataIndex: 'name',
      key: 'name',
      render: (name, record) => (
        <Space>
          <FileTextOutlined style={{
            color: record.type === 'video' ? '#fa8c16' :
                   record.type === 'document' ? '#1890ff' :
                   record.type === 'image' ? '#52c41a' : '#722ed1'
          }} />
          <Text>{name}</Text>
        </Space>
      )
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      render: (type) => (
        <Tag color={
          type === 'video' ? 'orange' :
          type === 'document' ? 'blue' :
          type === 'image' ? 'green' : 'purple'
        }>
          {type === 'video' ? '视频' :
           type === 'document' ? '文档' :
           type === 'image' ? '图片' :
           type === 'audio' ? '音频' : '其他'}
        </Tag>
      )
    },
    { title: '分类', dataIndex: 'category', key: 'category' },
    { title: '大小', dataIndex: 'size', key: 'size' },
    { title: '下载次数', dataIndex: 'downloadCount', key: 'downloadCount' },
    {
      title: '权限',
      dataIndex: 'isPublic',
      key: 'isPublic',
      render: (isPublic) => (
        <Tag color={isPublic ? 'green' : 'orange'}>
          {isPublic ? '公开' : '私有'}
        </Tag>
      )
    },
    { title: '上传时间', dataIndex: 'uploadedAt', key: 'uploadedAt' },
    {
      title: '操作',
      key: 'action',
      render: () => (
        <Space>
          <Button type="link" icon={<DownloadOutlined />}>下载</Button>
          <Button type="link" icon={<EditOutlined />}>编辑</Button>
          <Button type="link" danger>删除</Button>
        </Space>
      )
    }
  ]

  return (
    <div style={{ padding: '24px' }}>
      {/* 页面标题 */}
      <div style={{ marginBottom: 24 }}>
        <Title level={2}>教学管理中心</Title>
        <Paragraph>
          课程管理、作业布置、学情分析一体化平台
        </Paragraph>
      </div>

      {/* 统计数据 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="我的班级"
              value={classes.length}
              prefix={<TeamOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="进行中课程"
              value={courses.filter(c => c.status === 'published').length}
              prefix={<BookOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="待批改作业"
              value={assignments.filter(a => a.submittedCount < a.totalCount).length}
              prefix={<FileTextOutlined />}
              valueStyle={{ color: '#fa8c16' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="学生总数"
              value={classes.reduce((sum, cls) => sum + cls.studentCount, 0)}
              prefix={<UserOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 主要内容 */}
      <Tabs activeKey={activeTab} onChange={handleTabChange}>
        <TabPane tab="我的班级" key="classes">
          <Card
            title="班级管理"
            extra={
              <Button 
                type="primary" 
                icon={<PlusOutlined />}
                onClick={() => setIsClassModalVisible(true)}
              >
                创建班级
              </Button>
            }
          >
            <Table
              columns={classColumns}
              dataSource={classes}
              rowKey="id"
              pagination={{ pageSize: 10 }}
            />
          </Card>
        </TabPane>

        <TabPane tab="我的课程" key="courses">
          <Card
            title="课程管理"
            extra={
              <Space>
                <Button icon={<UploadOutlined />}>导入课程</Button>
                <Button 
                  type="primary" 
                  icon={<PlusOutlined />}
                  onClick={() => setIsCourseModalVisible(true)}
                >
                  创建课程
                </Button>
              </Space>
            }
          >
            <Table
              columns={courseColumns}
              dataSource={courses}
              rowKey="id"
              pagination={{ pageSize: 10 }}
            />
          </Card>
        </TabPane>

        <TabPane tab="作业管理" key="assignments">
          <Card
            title="作业与考试"
            extra={
              <Button 
                type="primary" 
                icon={<PlusOutlined />}
                onClick={() => setIsAssignmentModalVisible(true)}
              >
                布置作业
              </Button>
            }
          >
            <Table
              columns={assignmentColumns}
              dataSource={assignments}
              rowKey="id"
              pagination={{ pageSize: 10 }}
            />
          </Card>
        </TabPane>

        <TabPane tab="考试中心" key="exams">
          <Card
            title="考试管理"
            extra={
              <Space>
                <Button icon={<FileTextOutlined />}>组卷</Button>
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={() => setIsAssignmentModalVisible(true)}
                >
                  发布考试
                </Button>
              </Space>
            }
          >
            <Table
              columns={examColumns}
              dataSource={exams}
              rowKey="id"
              pagination={{ pageSize: 10 }}
            />
          </Card>
        </TabPane>

        <TabPane tab="题库管理" key="questions">
          <Card
            title="习题库"
            extra={
              <Space>
                <Button icon={<UploadOutlined />}>导入题目</Button>
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={() => setIsAssignmentModalVisible(true)}
                >
                  添加题目
                </Button>
              </Space>
            }
          >
            <Table
              columns={questionColumns}
              dataSource={questions}
              rowKey="id"
              pagination={{ pageSize: 10 }}
            />
          </Card>
        </TabPane>

        <TabPane tab="资源库" key="resources">
          <Card
            title="教学资源管理"
            extra={
              <Space>
                <Button icon={<UploadOutlined />}>上传资源</Button>
                <Button icon={<DownloadOutlined />}>批量下载</Button>
              </Space>
            }
          >
            <Table
              columns={resourceColumns}
              dataSource={resources}
              rowKey="id"
              pagination={{ pageSize: 10 }}
            />
          </Card>
        </TabPane>

        <TabPane tab="学情分析" key="analytics">
          <Row gutter={16}>
            <Col span={12}>
              <Card title="班级学习情况" style={{ marginBottom: 16 }}>
                <div style={{ textAlign: 'center', padding: '40px 0' }}>
                  <BarChartOutlined style={{ fontSize: 48, color: '#d9d9d9' }} />
                  <div style={{ marginTop: 16, color: '#999' }}>学习数据分析图表</div>
                </div>
              </Card>
            </Col>
            <Col span={12}>
              <Card title="作业完成统计" style={{ marginBottom: 16 }}>
                <div style={{ textAlign: 'center', padding: '40px 0' }}>
                  <TrophyOutlined style={{ fontSize: 48, color: '#d9d9d9' }} />
                  <div style={{ marginTop: 16, color: '#999' }}>作业统计图表</div>
                </div>
              </Card>
            </Col>
          </Row>
        </TabPane>
      </Tabs>

      {/* 创建班级模态框 */}
      <Modal
        title="创建班级"
        open={isClassModalVisible}
        onOk={() => classForm.submit()}
        onCancel={() => setIsClassModalVisible(false)}
      >
        <Form form={classForm} layout="vertical" onFinish={handleCreateClass}>
          <Form.Item name="name" label="班级名称" rules={[{ required: true }]}>
            <Input placeholder="请输入班级名称" />
          </Form.Item>
          <Form.Item name="description" label="班级描述">
            <TextArea rows={3} placeholder="请输入班级描述" />
          </Form.Item>
        </Form>
      </Modal>

      {/* 创建课程模态框 */}
      <Modal
        title="创建课程"
        open={isCourseModalVisible}
        onOk={() => courseForm.submit()}
        onCancel={() => setIsCourseModalVisible(false)}
        width={600}
      >
        <Form form={courseForm} layout="vertical" onFinish={handleCreateCourse}>
          <Form.Item name="title" label="课程名称" rules={[{ required: true }]}>
            <Input placeholder="请输入课程名称" />
          </Form.Item>
          <Form.Item name="classId" label="所属班级" rules={[{ required: true }]}>
            <Select placeholder="请选择班级">
              {classes.map(cls => (
                <Option key={cls.id} value={cls.id}>{cls.name}</Option>
              ))}
            </Select>
          </Form.Item>
          <Form.Item name="description" label="课程描述">
            <TextArea rows={3} placeholder="请输入课程描述" />
          </Form.Item>
        </Form>
      </Modal>

      {/* 布置作业模态框 */}
      <Modal
        title="布置作业"
        open={isAssignmentModalVisible}
        onOk={() => assignmentForm.submit()}
        onCancel={() => setIsAssignmentModalVisible(false)}
        width={600}
      >
        <Form form={assignmentForm} layout="vertical" onFinish={handleCreateAssignment}>
          <Form.Item name="title" label="作业标题" rules={[{ required: true }]}>
            <Input placeholder="请输入作业标题" />
          </Form.Item>
          <Form.Item name="courseId" label="所属课程" rules={[{ required: true }]}>
            <Select placeholder="请选择课程">
              {courses.map(course => (
                <Option key={course.id} value={course.id}>{course.title}</Option>
              ))}
            </Select>
          </Form.Item>
          <Form.Item name="type" label="作业类型" rules={[{ required: true }]}>
            <Select placeholder="请选择作业类型">
              <Option value="theory">理论知识</Option>
              <Option value="practice">实操知识</Option>
            </Select>
          </Form.Item>
          <Form.Item name="dueDate" label="截止时间" rules={[{ required: true }]}>
            <DatePicker showTime style={{ width: '100%' }} />
          </Form.Item>
          <Form.Item name="description" label="作业要求" rules={[{ required: true }]}>
            <TextArea rows={4} placeholder="请详细描述作业要求..." />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}

export default TeacherManagePage
