<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MCTT - 会展行业虚实融合实训教学平台</title>
    <link href="https://cdn.jsdelivr.net/npm/antd@5.12.8/dist/reset.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/antd@5.12.8/dist/antd.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Robot<PERSON>', 'Oxygen', 'Ubuntu', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>s', 'Droid Sans', 'Helvetica Neue', sans-serif;
            background-color: #f5f5f5;
        }

        .login-container {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .login-card {
            background: white;
            padding: 40px;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 400px;
            text-align: center;
        }

        .login-title {
            color: #1890ff;
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 8px;
        }

        .login-subtitle {
            color: #666;
            margin-bottom: 32px;
        }

        .demo-account {
            background: #f8f9fa;
            border: 2px dashed #d9d9d9;
            border-radius: 8px;
            padding: 16px;
            margin: 16px 0;
            cursor: pointer;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .demo-account:hover {
            border-color: #1890ff;
            background: #e6f7ff;
        }

        .demo-account-info {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .demo-account-role {
            background: #1890ff;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
        }

        .main-container {
            display: none;
            min-height: 100vh;
        }

        .sidebar {
            width: 256px;
            background: #001529;
            min-height: 100vh;
            position: fixed;
            left: 0;
            top: 0;
            z-index: 1000;
        }

        .sidebar.collapsed {
            width: 80px;
        }

        .logo {
            height: 64px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 18px;
            border-bottom: 1px solid #002140;
        }

        .sidebar.collapsed .logo {
            font-size: 14px;
        }

        .menu-item {
            display: flex;
            align-items: center;
            padding: 16px 24px;
            color: rgba(255, 255, 255, 0.65);
            cursor: pointer;
            transition: all 0.3s;
            border-bottom: 1px solid #002140;
        }

        .menu-item:hover,
        .menu-item.active {
            background: #1890ff;
            color: white;
        }

        .menu-item i {
            margin-right: 12px;
            width: 20px;
            text-align: center;
        }

        .sidebar.collapsed .menu-item span {
            display: none;
        }

        .main-content {
            margin-left: 256px;
            transition: margin-left 0.3s;
        }

        .main-content.expanded {
            margin-left: 80px;
        }

        .header {
            background: white;
            height: 64px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 24px;
            box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
        }

        .header-left {
            display: flex;
            align-items: center;
        }

        .toggle-btn {
            background: none;
            border: none;
            font-size: 18px;
            cursor: pointer;
            padding: 8px;
            margin-right: 16px;
        }

        .header-right {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: #1890ff;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            cursor: pointer;
        }

        .content {
            padding: 24px;
            min-height: calc(100vh - 64px);
        }

        .page-header {
            margin-bottom: 24px;
        }

        .page-title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .page-description {
            color: #666;
            font-size: 16px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
            margin-bottom: 32px;
        }

        .stat-card {
            background: white;
            padding: 24px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        .stat-value {
            font-size: 32px;
            font-weight: bold;
            color: #1890ff;
            margin-bottom: 8px;
        }

        .stat-label {
            color: #666;
            font-size: 14px;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 16px;
            margin-bottom: 32px;
        }

        .feature-card {
            background: white;
            padding: 32px 24px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
        }

        .feature-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
        }

        .feature-icon {
            font-size: 48px;
            color: #1890ff;
            margin-bottom: 16px;
        }

        .feature-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 8px;
        }

        .feature-description {
            color: #666;
            margin-bottom: 16px;
            line-height: 1.5;
        }

        .btn {
            background: #1890ff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s;
        }

        .btn:hover {
            background: #40a9ff;
        }

        .btn-large {
            padding: 12px 24px;
            font-size: 16px;
        }

        .hidden {
            display: none !important;
        }

        .loading {
            text-align: center;
            padding: 60px 0;
        }

        .loading-icon {
            font-size: 48px;
            color: #1890ff;
            animation: spin 1s linear infinite;
            margin-bottom: 16px;
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #f0f0f0;
            border-radius: 4px;
            overflow: hidden;
            margin: 16px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #108ee9, #87d068);
            border-radius: 4px;
            transition: width 0.3s;
        }

        .demo-result {
            background: white;
            padding: 24px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            margin-top: 24px;
        }

        .result-preview {
            width: 100%;
            height: 300px;
            background: #f5f5f5;
            border: 2px dashed #d9d9d9;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 16px;
            position: relative;
        }

        .preview-placeholder {
            text-align: center;
            color: #999;
        }

        .booth-demo {
            position: absolute;
            background: #1890ff;
            color: white;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
        }

        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
            }
            .sidebar.mobile-open {
                transform: translateX(0);
            }
            .main-content {
                margin-left: 0;
            }
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
            .features-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- 登录页面 -->
    <div id="loginPage" class="login-container">
        <div class="login-card">
            <div class="login-title">MCTT平台</div>
            <div class="login-subtitle">会展行业虚实融合实训教学平台</div>
            
            <div style="margin: 32px 0;">
                <p style="color: #999; font-size: 14px; margin-bottom: 16px;">
                    点击下方账号快速登录体验（密码：123456）
                </p>
                
                <div class="demo-account" onclick="login('student1', '学生')">
                    <div class="demo-account-info">
                        <i class="fas fa-user"></i>
                        <span>student1</span>
                    </div>
                    <div class="demo-account-role" style="background: #1890ff;">学生</div>
                </div>
                
                <div class="demo-account" onclick="login('teacher1', '教师')">
                    <div class="demo-account-info">
                        <i class="fas fa-chalkboard-teacher"></i>
                        <span>teacher1</span>
                    </div>
                    <div class="demo-account-role" style="background: #52c41a;">教师</div>
                </div>
                
                <div class="demo-account" onclick="login('enterprise1', '企业')">
                    <div class="demo-account-info">
                        <i class="fas fa-building"></i>
                        <span>enterprise1</span>
                    </div>
                    <div class="demo-account-role" style="background: #fa8c16;">企业</div>
                </div>
            </div>
            
            <div style="text-align: center; margin-top: 24px;">
                <p style="font-size: 12px; color: #999;">
                    © 2024 MCTT会展实训平台. All rights reserved.
                </p>
            </div>
        </div>
    </div>

    <!-- 主应用界面 -->
    <div id="mainApp" class="main-container">
        <!-- 侧边栏 -->
        <div id="sidebar" class="sidebar">
            <div class="logo">
                <span id="logoText">会展实训平台</span>
            </div>
            <div class="menu-item active" onclick="showPage('home')">
                <i class="fas fa-home"></i>
                <span>首页</span>
            </div>
            <div class="menu-item" onclick="showPage('ai-plan')">
                <i class="fas fa-lightbulb"></i>
                <span>AI方案生成</span>
            </div>
            <div class="menu-item" onclick="showPage('render-2d')">
                <i class="fas fa-image"></i>
                <span>2D效果图</span>
            </div>
            <div class="menu-item" onclick="showPage('model-3d')">
                <i class="fas fa-cube"></i>
                <span>3D模型</span>
            </div>
            <div class="menu-item" onclick="showPage('construction')">
                <i class="fas fa-tools"></i>
                <span>施工管理</span>
            </div>
            <div class="menu-item" onclick="showPage('monitoring')">
                <i class="fas fa-chart-line"></i>
                <span>教学监控</span>
            </div>
        </div>

        <!-- 主内容区 -->
        <div id="mainContent" class="main-content">
            <!-- 头部 -->
            <div class="header">
                <div class="header-left">
                    <button class="toggle-btn" onclick="toggleSidebar()">
                        <i id="toggleIcon" class="fas fa-bars"></i>
                    </button>
                </div>
                <div class="header-right">
                    <span id="userName">欢迎，用户</span>
                    <div class="user-avatar" onclick="logout()">
                        <i class="fas fa-user"></i>
                    </div>
                </div>
            </div>

            <!-- 内容区域 -->
            <div class="content">
                <!-- 首页 -->
                <div id="homePage" class="page">
                    <div class="page-header">
                        <div class="page-title">
                            <i class="fas fa-home"></i>
                            欢迎使用会展行业虚实融合实训教学平台
                        </div>
                        <div class="page-description">
                            一站式会展项目管理平台，从方案设计到施工管理，让学生体验完整的会展项目流程
                        </div>
                    </div>

                    <!-- 统计数据 -->
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-value">12</div>
                            <div class="stat-label">活跃项目</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value">48</div>
                            <div class="stat-label">生成方案</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value">156</div>
                            <div class="stat-label">效果图数量</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value">24</div>
                            <div class="stat-label">在线用户</div>
                        </div>
                    </div>

                    <!-- 功能模块 -->
                    <div class="features-grid">
                        <div class="feature-card" onclick="showPage('ai-plan')">
                            <div class="feature-icon">
                                <i class="fas fa-lightbulb"></i>
                            </div>
                            <div class="feature-title">AI方案生成</div>
                            <div class="feature-description">
                                智能生成会展布局方案，包括展位分配、流线规划等
                            </div>
                            <button class="btn">开始使用</button>
                        </div>
                        <div class="feature-card" onclick="showPage('render-2d')">
                            <div class="feature-icon">
                                <i class="fas fa-image"></i>
                            </div>
                            <div class="feature-title">2D效果图生成</div>
                            <div class="feature-description">
                                多角度、多场景的2D效果图生成，支持实时预览
                            </div>
                            <button class="btn">开始使用</button>
                        </div>
                        <div class="feature-card" onclick="showPage('model-3d')">
                            <div class="feature-icon">
                                <i class="fas fa-cube"></i>
                            </div>
                            <div class="feature-title">3D模型展示</div>
                            <div class="feature-description">
                                生成3D会展模型，支持虚拟漫游和交互体验
                            </div>
                            <button class="btn">开始使用</button>
                        </div>
                        <div class="feature-card" onclick="showPage('construction')">
                            <div class="feature-icon">
                                <i class="fas fa-tools"></i>
                            </div>
                            <div class="feature-title">施工管理</div>
                            <div class="feature-description">
                                完整的施工管理流程，包括进度跟踪和质量控制
                            </div>
                            <button class="btn">开始使用</button>
                        </div>
                    </div>
                </div>

                <!-- AI方案生成页面 -->
                <div id="ai-planPage" class="page hidden">
                    <div class="page-header">
                        <div class="page-title">
                            <i class="fas fa-lightbulb"></i>
                            AI智能方案生成
                        </div>
                        <div class="page-description">
                            基于AI技术，智能生成会展布局方案，包括展位分配、流线规划、区域划分等
                        </div>
                    </div>

                    <div style="display: grid; grid-template-columns: 1fr 2fr; gap: 24px;">
                        <!-- 配置面板 -->
                        <div style="background: white; padding: 24px; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); height: fit-content;">
                            <h3 style="margin-bottom: 24px;">方案需求配置</h3>

                            <div style="margin-bottom: 16px;">
                                <label style="display: block; margin-bottom: 8px; font-weight: bold;">展览类型</label>
                                <select id="exhibitionType" style="width: 100%; padding: 8px; border: 1px solid #d9d9d9; border-radius: 6px;">
                                    <option value="auto">汽车展</option>
                                    <option value="tech">科技展</option>
                                    <option value="furniture">家具展</option>
                                    <option value="food">食品展</option>
                                </select>
                            </div>

                            <div style="margin-bottom: 16px;">
                                <label style="display: block; margin-bottom: 8px; font-weight: bold;">场馆规模</label>
                                <select id="venueSize" style="width: 100%; padding: 8px; border: 1px solid #d9d9d9; border-radius: 6px;">
                                    <option value="small">小型 (1000-3000㎡)</option>
                                    <option value="medium">中型 (3000-8000㎡)</option>
                                    <option value="large">大型 (8000-15000㎡)</option>
                                </select>
                            </div>

                            <div style="margin-bottom: 16px;">
                                <label style="display: block; margin-bottom: 8px; font-weight: bold;">预计展位数量</label>
                                <input id="boothCount" type="number" placeholder="如：50" style="width: 100%; padding: 8px; border: 1px solid #d9d9d9; border-radius: 6px;">
                            </div>

                            <div style="margin-bottom: 24px;">
                                <label style="display: block; margin-bottom: 8px; font-weight: bold;">特殊需求</label>
                                <textarea id="specialRequirements" rows="4" placeholder="请描述特殊需求，如：需要VIP区域、特殊展示区、无障碍通道等" style="width: 100%; padding: 8px; border: 1px solid #d9d9d9; border-radius: 6px; resize: vertical;"></textarea>
                            </div>

                            <button id="generateBtn" class="btn btn-large" onclick="generateAIPlan()" style="width: 100%;">
                                <i class="fas fa-rocket"></i> 生成AI方案
                            </button>
                        </div>

                        <!-- 结果展示区 -->
                        <div id="aiResultArea">
                            <div id="aiPlaceholder" style="background: white; padding: 60px; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); text-align: center;">
                                <i class="fas fa-lightbulb" style="font-size: 64px; color: #d9d9d9; margin-bottom: 24px;"></i>
                                <h3 style="color: #999; margin-bottom: 16px;">配置需求并生成AI方案</h3>
                                <p style="color: #666;">在左侧填写展览需求，点击生成按钮开始AI智能方案生成</p>
                            </div>

                            <div id="aiLoading" class="hidden" style="background: white; padding: 60px; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); text-align: center;">
                                <i class="fas fa-cog loading-icon"></i>
                                <h3>AI正在生成方案...</h3>
                                <p style="margin-bottom: 32px;">正在分析需求并生成最优布局方案，请稍候</p>
                                <div class="progress-bar">
                                    <div id="aiProgress" class="progress-fill" style="width: 0%;"></div>
                                </div>
                                <div id="aiProgressText" style="margin-top: 16px; font-size: 14px; color: #666;">正在分析展览需求...</div>
                            </div>

                            <div id="aiResult" class="hidden demo-result">
                                <h3 style="margin-bottom: 16px;">生成的方案预览</h3>
                                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 16px;">
                                    <div class="result-preview">
                                        <div class="preview-placeholder">
                                            <i class="fas fa-lightbulb" style="font-size: 48px; color: #1890ff; margin-bottom: 16px;"></i>
                                            <div>布局预览图</div>
                                            <div style="font-size: 12px; color: #999; margin-top: 8px;">1200 × 800</div>
                                        </div>
                                        <!-- 模拟展位 -->
                                        <div class="booth-demo" style="left: 20%; top: 30%; width: 40px; height: 30px;">A01</div>
                                        <div class="booth-demo" style="left: 45%; top: 45%; width: 40px; height: 30px;">A02</div>
                                        <div class="booth-demo" style="left: 70%; top: 60%; width: 40px; height: 30px;">B01</div>
                                    </div>
                                    <div>
                                        <div style="margin-bottom: 16px;">
                                            <div style="font-size: 24px; font-weight: bold; color: #1890ff;">3</div>
                                            <div style="color: #666;">展位数量</div>
                                        </div>
                                        <div style="margin-bottom: 16px;">
                                            <div style="font-size: 24px; font-weight: bold; color: #52c41a;">2</div>
                                            <div style="color: #666;">通道数量</div>
                                        </div>
                                        <div style="margin-bottom: 16px;">
                                            <div style="font-size: 24px; font-weight: bold; color: #722ed1;">9.6</div>
                                            <div style="color: #666;">展区面积(万平方米)</div>
                                        </div>
                                        <div style="margin-top: 24px;">
                                            <h4>展位列表</h4>
                                            <div style="margin: 8px 0;">
                                                <span style="background: #e6f7ff; color: #1890ff; padding: 4px 8px; border-radius: 4px; margin-right: 8px; font-size: 12px;">A01 - 参展商A</span>
                                                <span style="background: #e6f7ff; color: #1890ff; padding: 4px 8px; border-radius: 4px; margin-right: 8px; font-size: 12px;">A02 - 参展商B</span>
                                                <span style="background: #e6f7ff; color: #1890ff; padding: 4px 8px; border-radius: 4px; font-size: 12px;">B01 - 参展商C</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div style="text-align: center; margin-top: 24px;">
                                    <button class="btn" style="margin-right: 8px;">保存方案</button>
                                    <button class="btn" style="margin-right: 8px;">导出方案</button>
                                    <button class="btn" onclick="showPage('render-2d')">生成2D效果图</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentUser = null;
        let sidebarCollapsed = false;

        // 登录功能
        function login(username, role) {
            currentUser = { username, role };
            document.getElementById('userName').textContent = `欢迎，${username}`;
            document.getElementById('loginPage').style.display = 'none';
            document.getElementById('mainApp').style.display = 'block';
            showPage('home');
        }

        // 退出登录
        function logout() {
            currentUser = null;
            document.getElementById('loginPage').style.display = 'flex';
            document.getElementById('mainApp').style.display = 'none';
        }

        // 切换侧边栏
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            const mainContent = document.getElementById('mainContent');
            const toggleIcon = document.getElementById('toggleIcon');
            const logoText = document.getElementById('logoText');
            
            sidebarCollapsed = !sidebarCollapsed;
            
            if (sidebarCollapsed) {
                sidebar.classList.add('collapsed');
                mainContent.classList.add('expanded');
                toggleIcon.className = 'fas fa-indent';
                logoText.textContent = 'MCTT';
            } else {
                sidebar.classList.remove('collapsed');
                mainContent.classList.remove('expanded');
                toggleIcon.className = 'fas fa-bars';
                logoText.textContent = '会展实训平台';
            }
        }

        // 显示页面
        function showPage(pageId) {
            // 隐藏所有页面
            const pages = document.querySelectorAll('.page');
            pages.forEach(page => page.classList.add('hidden'));
            
            // 移除所有菜单项的active状态
            const menuItems = document.querySelectorAll('.menu-item');
            menuItems.forEach(item => item.classList.remove('active'));
            
            // 显示对应页面
            const targetPage = document.getElementById(pageId + 'Page');
            if (targetPage) {
                targetPage.classList.remove('hidden');
            } else {
                // 如果页面不存在，创建一个占位页面
                createPlaceholderPage(pageId);
            }
            
            // 激活对应菜单项
            const menuItem = Array.from(menuItems).find(item => 
                item.onclick && item.onclick.toString().includes(pageId)
            );
            if (menuItem) {
                menuItem.classList.add('active');
            }
        }

        // 创建占位页面
        function createPlaceholderPage(pageId) {
            const content = document.querySelector('.content');
            
            // 移除现有的占位页面
            const existingPlaceholder = document.getElementById('placeholderPage');
            if (existingPlaceholder) {
                existingPlaceholder.remove();
            }
            
            const pageNames = {
                'ai-plan': { title: 'AI智能方案生成', icon: 'fas fa-lightbulb', desc: '基于AI技术，智能生成会展布局方案' },
                'render-2d': { title: 'AI 2D效果图生成', icon: 'fas fa-image', desc: '基于AI技术生成多角度、多场景的2D效果图' },
                'model-3d': { title: '3D模型生成和展示', icon: 'fas fa-cube', desc: '集成3D建模技术，生成会展场馆3D模型' },
                'construction': { title: '施工管理系统', icon: 'fas fa-tools', desc: '完整的施工管理流程，包括进度跟踪、资源管理' },
                'monitoring': { title: '教学监控系统', icon: 'fas fa-chart-line', desc: '为教师提供学生学习进度监控、项目管理功能' }
            };
            
            const pageInfo = pageNames[pageId] || { title: '功能开发中', icon: 'fas fa-cog', desc: '该功能正在开发中，敬请期待' };
            
            const placeholderHTML = `
                <div id="placeholderPage" class="page">
                    <div class="page-header">
                        <div class="page-title">
                            <i class="${pageInfo.icon}"></i>
                            ${pageInfo.title}
                        </div>
                        <div class="page-description">
                            ${pageInfo.desc}
                        </div>
                    </div>
                    <div style="text-align: center; padding: 80px 0; background: white; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                        <i class="${pageInfo.icon}" style="font-size: 64px; color: #d9d9d9; margin-bottom: 24px;"></i>
                        <h3 style="color: #999; margin-bottom: 16px;">功能开发中</h3>
                        <p style="color: #666;">该功能正在开发中，敬请期待完整版本</p>
                        <button class="btn btn-large" onclick="showPage('home')" style="margin-top: 24px;">
                            返回首页
                        </button>
                    </div>
                </div>
            `;
            
            content.insertAdjacentHTML('beforeend', placeholderHTML);
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 默认显示登录页面
            document.getElementById('loginPage').style.display = 'flex';
            document.getElementById('mainApp').style.display = 'none';
        });
    </script>
</body>
</html>
