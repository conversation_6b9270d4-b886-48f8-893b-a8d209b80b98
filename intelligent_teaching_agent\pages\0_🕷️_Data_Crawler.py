"""
数据爬虫准备模块
展示数据获取和准备过程，帮助学生理解数据来源和预处理方法
"""

import streamlit as st
import pandas as pd
import numpy as np
import plotly.graph_objects as go
import plotly.express as px
from datetime import datetime, timedelta
import time
import requests
from io import StringIO

# 页面配置
st.set_page_config(
    page_title="数据爬虫准备",
    page_icon="🕷️",
    layout="wide"
)

# 自定义CSS
st.markdown("""
<style>
    .crawler-card {
        background-color: #f8f9fa;
        padding: 1.5rem;
        border-radius: 10px;
        border-left: 4px solid #28a745;
        margin: 1rem 0;
    }
    .step-card {
        background-color: #e8f4fd;
        padding: 1rem;
        border-radius: 8px;
        border: 1px solid #bee5eb;
        margin: 0.5rem 0;
    }
    .progress-bar {
        background-color: #e9ecef;
        border-radius: 10px;
        height: 20px;
        overflow: hidden;
    }
    .progress-fill {
        background: linear-gradient(90deg, #28a745, #20c997);
        height: 100%;
        transition: width 0.3s ease;
    }
    .data-source {
        background-color: #fff3cd;
        padding: 1rem;
        border-radius: 5px;
        border-left: 4px solid #ffc107;
        margin: 0.5rem 0;
    }
</style>
""", unsafe_allow_html=True)

def simulate_data_crawling():
    """模拟数据爬取过程"""
    data_sources = [
        {"name": "电商平台API", "url": "https://api.ecommerce.com/sales", "status": "success", "records": 15420},
        {"name": "社交媒体数据", "url": "https://api.social.com/trends", "status": "success", "records": 8930},
        {"name": "市场调研报告", "url": "https://api.research.com/reports", "status": "success", "records": 2340},
        {"name": "供应商信息库", "url": "https://api.suppliers.com/data", "status": "success", "records": 567},
        {"name": "行业统计数据", "url": "https://api.industry.com/stats", "status": "success", "records": 1890}
    ]
    return data_sources

def create_data_quality_chart(data_quality):
    """创建数据质量图表"""
    fig = go.Figure()
    
    categories = list(data_quality.keys())
    values = list(data_quality.values())
    
    fig.add_trace(go.Scatterpolar(
        r=values,
        theta=categories,
        fill='toself',
        name='数据质量',
        line_color='#28a745',
        fillcolor='rgba(40, 167, 69, 0.3)'
    ))
    
    fig.update_layout(
        polar=dict(
            radialaxis=dict(
                visible=True,
                range=[0, 100]
            )),
        showlegend=False,
        title="数据质量评估",
        height=400
    )
    
    return fig

def create_data_flow_diagram():
    """创建数据流程图"""
    fig = go.Figure()
    
    # 定义节点位置
    nodes = {
        "数据源": (1, 3),
        "数据爬取": (2, 3),
        "数据清洗": (3, 3),
        "数据验证": (4, 3),
        "数据存储": (5, 3),
        "数据分析": (6, 3)
    }
    
    # 添加节点
    for name, (x, y) in nodes.items():
        fig.add_trace(go.Scatter(
            x=[x], y=[y],
            mode='markers+text',
            marker=dict(size=60, color='#28a745'),
            text=name,
            textposition="middle center",
            textfont=dict(color="white", size=10),
            showlegend=False
        ))
    
    # 添加连接线
    connections = [
        ("数据源", "数据爬取"),
        ("数据爬取", "数据清洗"),
        ("数据清洗", "数据验证"),
        ("数据验证", "数据存储"),
        ("数据存储", "数据分析")
    ]
    
    for start, end in connections:
        start_pos = nodes[start]
        end_pos = nodes[end]
        fig.add_trace(go.Scatter(
            x=[start_pos[0], end_pos[0]],
            y=[start_pos[1], end_pos[1]],
            mode='lines',
            line=dict(color='#6c757d', width=3),
            showlegend=False
        ))
        
        # 添加箭头
        fig.add_annotation(
            x=end_pos[0], y=end_pos[1],
            ax=start_pos[0], ay=start_pos[1],
            xref='x', yref='y',
            axref='x', ayref='y',
            arrowhead=2,
            arrowsize=1,
            arrowwidth=2,
            arrowcolor='#6c757d'
        )
    
    fig.update_layout(
        title="数据处理工作流",
        xaxis=dict(showgrid=False, zeroline=False, showticklabels=False),
        yaxis=dict(showgrid=False, zeroline=False, showticklabels=False),
        height=300,
        showlegend=False
    )
    
    return fig

def main():
    st.title("🕷️ 数据爬虫准备")
    st.markdown("了解数据获取和准备过程，掌握数据爬虫的基本原理和实践方法")
    
    # 数据爬虫概述
    st.markdown("""
    <div class="crawler-card">
        <h3>🎯 数据爬虫学习目标</h3>
        <ul>
            <li>理解数据获取的基本流程和方法</li>
            <li>掌握数据清洗和预处理技术</li>
            <li>学会评估数据质量和完整性</li>
            <li>了解数据爬虫的法律和道德规范</li>
        </ul>
    </div>
    """, unsafe_allow_html=True)
    
    # 创建选项卡
    tab1, tab2, tab3, tab4 = st.tabs(["🔍 数据源探索", "🕷️ 爬虫演示", "🧹 数据清洗", "📊 质量评估"])
    
    with tab1:
        st.subheader("数据源探索")
        st.markdown("了解不同类型的数据源及其特点")
        
        # 数据源类型
        col1, col2 = st.columns(2)
        
        with col1:
            st.markdown("""
            <div class="data-source">
                <h4>🌐 Web数据源</h4>
                <ul>
                    <li>电商网站：产品信息、价格、评论</li>
                    <li>社交媒体：用户行为、趋势话题</li>
                    <li>新闻网站：市场动态、行业资讯</li>
                    <li>政府网站：统计数据、政策信息</li>
                </ul>
            </div>
            """, unsafe_allow_html=True)
        
        with col2:
            st.markdown("""
            <div class="data-source">
                <h4>📡 API数据源</h4>
                <ul>
                    <li>REST API：结构化数据接口</li>
                    <li>GraphQL：灵活的查询接口</li>
                    <li>WebSocket：实时数据流</li>
                    <li>第三方服务：专业数据提供商</li>
                </ul>
            </div>
            """, unsafe_allow_html=True)
        
        # 数据流程图
        st.subheader("数据处理工作流")
        flow_fig = create_data_flow_diagram()
        st.plotly_chart(flow_fig, use_container_width=True)
        
        # 技术栈介绍
        st.subheader("常用技术栈")
        tech_col1, tech_col2, tech_col3 = st.columns(3)
        
        with tech_col1:
            st.markdown("""
            **爬虫框架**
            - Scrapy：专业爬虫框架
            - BeautifulSoup：HTML解析
            - Selenium：动态网页爬取
            - Requests：HTTP请求库
            """)
        
        with tech_col2:
            st.markdown("""
            **数据处理**
            - Pandas：数据分析
            - NumPy：数值计算
            - Matplotlib：数据可视化
            - SQLAlchemy：数据库操作
            """)
        
        with tech_col3:
            st.markdown("""
            **存储方案**
            - MySQL：关系型数据库
            - MongoDB：文档数据库
            - Redis：缓存数据库
            - Elasticsearch：搜索引擎
            """)
    
    with tab2:
        st.subheader("爬虫演示")
        st.markdown("模拟数据爬取过程，展示实际操作步骤")
        
        # 爬虫控制面板
        st.markdown("### 🎮 爬虫控制面板")
        
        col1, col2, col3 = st.columns(3)
        with col1:
            target_url = st.selectbox(
                "选择目标网站",
                ["电商平台", "社交媒体", "新闻网站", "数据API"]
            )
        
        with col2:
            crawl_depth = st.slider("爬取深度", 1, 5, 2)
        
        with col3:
            delay_time = st.slider("请求间隔(秒)", 0.1, 2.0, 0.5)
        
        # 开始爬取按钮
        if st.button("🚀 开始爬取", type="primary"):
            # 模拟爬取过程
            progress_bar = st.progress(0)
            status_text = st.empty()
            
            data_sources = simulate_data_crawling()
            
            for i, source in enumerate(data_sources):
                progress = (i + 1) / len(data_sources)
                progress_bar.progress(progress)
                status_text.text(f"正在爬取: {source['name']}")
                time.sleep(0.5)
            
            status_text.text("爬取完成！")
            
            # 显示爬取结果
            st.success("数据爬取成功完成！")
            
            # 爬取结果表格
            results_df = pd.DataFrame(data_sources)
            results_df.columns = ['数据源', 'URL', '状态', '记录数']
            st.dataframe(results_df, use_container_width=True)
            
            # 存储到session state
            st.session_state.crawled_data = data_sources
    
    with tab3:
        st.subheader("数据清洗")
        st.markdown("学习数据清洗的方法和技巧")
        
        if 'crawled_data' in st.session_state:
            st.markdown("### 📋 原始数据概览")
            
            # 生成示例原始数据
            raw_data = pd.DataFrame({
                '产品名称': ['连衣裙A', '上衣B', '', '裤装C', '外套D'],
                '价格': [299.99, 0, 159.99, -50, 899.99],
                '销量': [1500, 2300, 800, 1200, 600],
                '评分': [4.5, 5.0, 3.8, 4.2, 4.7],
                '库存': [100, 0, 50, 200, 30],
                '上架日期': ['2023-01-15', '2023-02-20', '', '2023-03-10', '2023-04-05']
            })
            
            st.dataframe(raw_data, use_container_width=True)
            
            # 数据问题识别
            st.markdown("### 🔍 数据质量问题")
            
            col1, col2 = st.columns(2)
            
            with col1:
                st.markdown("""
                **发现的问题：**
                - 缺失值：产品名称和日期字段有空值
                - 异常值：价格字段存在0和负值
                - 格式问题：日期格式不统一
                - 重复数据：可能存在重复记录
                """)
            
            with col2:
                st.markdown("""
                **清洗策略：**
                - 填充缺失值或删除不完整记录
                - 识别并处理异常值
                - 标准化数据格式
                - 去除重复数据
                """)
            
            # 数据清洗演示
            if st.button("🧹 执行数据清洗"):
                with st.spinner("正在清洗数据..."):
                    time.sleep(1)
                    
                    # 清洗后的数据
                    cleaned_data = pd.DataFrame({
                        '产品名称': ['连衣裙A', '上衣B', '裤装C', '外套D'],
                        '价格': [299.99, 159.99, 159.99, 899.99],
                        '销量': [1500, 2300, 1200, 600],
                        '评分': [4.5, 5.0, 4.2, 4.7],
                        '库存': [100, 50, 200, 30],
                        '上架日期': ['2023-01-15', '2023-02-20', '2023-03-10', '2023-04-05']
                    })
                    
                    st.success("数据清洗完成！")
                    st.markdown("### ✨ 清洗后数据")
                    st.dataframe(cleaned_data, use_container_width=True)
                    
                    # 清洗统计
                    col1, col2, col3, col4 = st.columns(4)
                    with col1:
                        st.metric("原始记录", "5")
                    with col2:
                        st.metric("清洗后记录", "4")
                    with col3:
                        st.metric("删除记录", "1")
                    with col4:
                        st.metric("数据完整性", "100%")
        else:
            st.info("请先在'爬虫演示'选项卡中执行数据爬取")
    
    with tab4:
        st.subheader("数据质量评估")
        st.markdown("评估数据质量，确保数据可用性")
        
        # 数据质量指标
        data_quality = {
            '完整性': 95,
            '准确性': 88,
            '一致性': 92,
            '时效性': 85,
            '有效性': 90
        }
        
        # 质量评估图表
        col1, col2 = st.columns(2)
        
        with col1:
            quality_fig = create_data_quality_chart(data_quality)
            st.plotly_chart(quality_fig, use_container_width=True)
        
        with col2:
            st.markdown("### 📊 质量指标说明")
            for metric, score in data_quality.items():
                color = "🟢" if score >= 90 else "🟡" if score >= 80 else "🔴"
                st.markdown(f"{color} **{metric}**: {score}%")
            
            st.markdown("""
            **评估标准：**
            - 🟢 优秀 (90-100%)
            - 🟡 良好 (80-89%)
            - 🔴 需改进 (<80%)
            """)
        
        # 质量改进建议
        st.markdown("### 💡 质量改进建议")
        
        suggestions = [
            {"问题": "时效性较低", "建议": "增加数据更新频率，建立实时数据管道", "优先级": "高"},
            {"问题": "准确性有待提升", "建议": "加强数据验证规则，增加人工审核环节", "优先级": "中"},
            {"问题": "完整性略有不足", "建议": "优化数据收集策略，减少缺失值", "优先级": "中"}
        ]
        
        for suggestion in suggestions:
            priority_color = {"高": "🔴", "中": "🟡", "低": "🟢"}[suggestion["优先级"]]
            st.markdown(f"""
            <div class="step-card">
                <strong>{priority_color} {suggestion['问题']}</strong><br>
                💡 {suggestion['建议']}
            </div>
            """, unsafe_allow_html=True)
    
    # 学习总结
    st.markdown("---")
    st.markdown("""
    ### 📚 学习要点总结
    
    通过本模块的学习，你应该掌握：
    
    1. **数据获取**：了解不同数据源的特点和获取方法
    2. **爬虫技术**：掌握网页爬虫的基本原理和实现
    3. **数据清洗**：学会识别和处理数据质量问题
    4. **质量评估**：建立数据质量评估体系
    5. **工具使用**：熟悉常用的数据处理工具和框架
    
    **下一步**：使用准备好的数据进行需求预测和供应商选择分析！
    """)

if __name__ == "__main__":
    main()
